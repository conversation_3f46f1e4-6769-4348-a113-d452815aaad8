This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, line numbers have been added.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: repomix.config.json, target/, node_modules/, public/, assets/, logo.svg, Cargo.lock, package-lock.json, build/, backend-source.tar.gz, .git/, .vscode/, **/.vscode/**, **/.gitignore, **/.gitmodules, **/.gitattributes, **/.env*, **/config.*, **/secrets.*, **/private.*, **/.DS_Store, **/Thumbs.db, .cursorignore, .gitignore, **/*.svg, README.md
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Line numbers have been added to the beginning of each line
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
План развёртывания приложения и CI:CD/
  1. local-dev-setup.md
  2. server-infrastructure-setup.md
  3. nginx-ssl-config.md
  4. cloudflare-setup.md
  5. deployment-plan.md
  6. secrets-config-management-plan.md
  7. monitoring-logging-plan.md
  8. backup-maintenance-plan.md
  9. nextjs-hybrid-rendering-plan.md
5. deployment-guide.md
complete-security-guide.md
```

# Files

## File: План развёртывания приложения и CI:CD/1. local-dev-setup.md
````markdown
  1: # Подробный план подготовки локального окружения (Development на Mac OS)
  2: 
  3: ## 1. Установка базовых инструментов разработки
  4: 
  5: ### 1.1 Установка Homebrew
  6: ```bash
  7: # Проверьте, установлен ли Homebrew
  8: brew --version
  9: 
 10: # Если не установлен, выполните установку
 11: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
 12: 
 13: # Добавьте Homebrew в PATH, если это требуется (следуйте инструкциям в выводе команды установки)
 14: ```
 15: 
 16: ### 1.2 Установка Git и базовых утилит
 17: ```bash
 18: # Установка Git, если он еще не установлен
 19: brew install git
 20: 
 21: # Установка дополнительных утилит
 22: brew install wget curl jq
 23: ```
 24: 
 25: ### 1.3 Настройка Git
 26: ```bash
 27: # Настройка имени и email для Git
 28: git config --global user.name "Ваше Имя"
 29: git config --global user.email "ваш.email@пример.com"
 30: 
 31: # Настройка редактора по умолчанию (опционально)
 32: git config --global core.editor "code --wait"  # для VS Code
 33: ```
 34: 
 35: ## 2. Установка Node.js и npm для Next.js
 36: 
 37: ### 2.1 Установка Node Version Manager (nvm)
 38: ```bash
 39: # Установка nvm
 40: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash
 41: 
 42: # Добавление nvm в текущую сессию
 43: export NVM_DIR="$HOME/.nvm"
 44: [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
 45: [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
 46: 
 47: # Проверка установки
 48: nvm --version
 49: ```
 50: 
 51: ### 2.2 Установка Node.js и npm
 52: ```bash
 53: # Просмотр доступных версий Node.js
 54: nvm ls-remote --lts
 55: 
 56: # Установка последней LTS версии Node.js (рекомендуется для Next.js)
 57: nvm install --lts
 58: 
 59: # Проверка установки
 60: node --version
 61: npm --version
 62: 
 63: # Установка глобальных npm пакетов
 64: npm install -g npm@latest
 65: npm install -g yarn
 66: ```
 67: 
 68: ### 2.3 Настройка npm
 69: ```bash
 70: # Создание или обновление .npmrc с полезными настройками
 71: cat > ~/.npmrc << EOF
 72: save-exact=true
 73: fund=false
 74: audit=false
 75: EOF
 76: ```
 77: 
 78: ## 3. Установка Rust и Cargo для бэкенда
 79: 
 80: ### 3.1 Установка Rust через rustup
 81: ```bash
 82: # Установка Rust
 83: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
 84: 
 85: # Добавление Rust в текущую сессию
 86: source "$HOME/.cargo/env"
 87: 
 88: # Проверка установки
 89: rustc --version
 90: cargo --version
 91: ```
 92: 
 93: ### 3.2 Установка дополнительных компонентов Rust
 94: ```bash
 95: # Установка rustfmt для форматирования кода
 96: rustup component add rustfmt
 97: 
 98: # Установка clippy для статического анализа
 99: rustup component add clippy
100: 
101: # Установка инструментов для документирования
102: rustup component add rust-docs
103: ```
104: 
105: ### 3.3 Установка дополнительных инструментов для Rust
106: ```bash
107: # Установка cargo-watch для автоматической перекомпиляции
108: cargo install cargo-watch
109: 
110: # Установка инструментов для тестирования API
111: cargo install cargo-insta  # для snapshot-тестирования
112: ```
113: 
114: ## 4. Настройка локальных SSL-сертификатов
115: 
116: ### 4.1 Установка mkcert
117: ```bash
118: # Установка mkcert через Homebrew
119: brew install mkcert
120: 
121: # Установка локального CA
122: mkcert -install
123: ```
124: 
125: ### 4.2 Создание SSL-сертификатов для локальной разработки
126: ```bash
127: # Создание директории для сертификатов
128: mkdir -p ~/.local-certs
129: 
130: # Генерация сертификатов
131: cd ~/.local-certs
132: mkcert localhost 127.0.0.1 ::1 dev.quer.us
133: 
134: # Проверка созданных сертификатов
135: ls -la
136: # Вы должны увидеть файлы localhost+3.pem (сертификат) и localhost+3-key.pem (ключ)
137: ```
138: 
139: ## 5. Настройка локального прокси для HTTPS
140: 
141: ### 5.1 Установка local-ssl-proxy
142: ```bash
143: # Установка через npm
144: npm install -g local-ssl-proxy
145: 
146: # Или альтернативно, установка caddy через Homebrew
147: brew install caddy
148: ```
149: 
150: ### 5.2 Создание скрипта для запуска локального SSL-прокси
151: ```bash
152: # Создаем директорию для скриптов
153: mkdir -p ~/scripts
154: 
155: # Создаем скрипт запуска прокси
156: cat > ~/scripts/start-ssl-proxy.sh << 'EOF'
157: #!/bin/bash
158: 
159: # Проверка наличия сертификатов
160: if [ ! -f ~/.local-certs/localhost+3.pem ] || [ ! -f ~/.local-certs/localhost+3-key.pem ]; then
161:   echo "SSL certificates not found. Please run mkcert first."
162:   exit 1
163: fi
164: 
165: # Запуск прокси для фронтенда
166: local-ssl-proxy --source 3001 --target 3000 --cert ~/.local-certs/localhost+3.pem --key ~/.local-certs/localhost+3-key.pem &
167: echo "Frontend SSL proxy started: https://localhost:3001 -> http://localhost:3000"
168: 
169: # Запуск прокси для бэкенда
170: local-ssl-proxy --source 8081 --target 8080 --cert ~/.local-certs/localhost+3.pem --key ~/.local-certs/localhost+3-key.pem &
171: echo "Backend SSL proxy started: https://localhost:8081 -> http://localhost:8080"
172: 
173: # Вывод информации о запущенных процессах
174: echo "Proxies started. Press Ctrl+C to stop."
175: wait
176: EOF
177: 
178: # Делаем скрипт исполняемым
179: chmod +x ~/scripts/start-ssl-proxy.sh
180: ```
181: 
182: ## 6. Клонирование и настройка проекта
183: 
184: ### 6.1 Клонирование репозитория
185: ```bash
186: # Создание рабочей директории
187: mkdir -p ~/projects
188: cd ~/projects
189: 
190: # Клонирование репозитория
191: git clone https://github.com/yourusername/quer-calc.git
192: cd quer-calc
193: ```
194: или
195: 
196: # Перейдите в директорию проекта, если вы ещё не в ней
197: cd /Users/<USER>/quer-calc-next
198: 
199: # Инициализируйте Git
200: git init
201: 
202: # Создайте первый коммит с существующим .gitignore + добавленными правилами для Rust
203: git add .
204: git commit -m "Initial commit"
205: 
206: # Создайте develop ветку как основную для начала работы
207: git branch -M develop
208: 
209: # Создайте репозиторий на GitHub через веб-интерфейс
210: 
211: # Затем свяжите локальный репозиторий с удаленным
212: git remote add origin https://github.com/yourusername/quer-calc.git
213: git push -u origin develop
214: 
215: # Создайте main ветку как пустую базовую ветку для production
216: git checkout --orphan main
217: git rm -rf .
218: echo "# Quer Calculator" > README.md
219: git add README.md
220: git commit -m "Initial main branch setup"
221: git push -u origin main
222: git checkout develop
223: 
224: ### 6.2 Установка зависимостей для фронтенда
225: ```bash
226: # Переход в директорию проекта
227: cd /Users/<USER>/quer-calc-next
228: 
229: # Установка зависимостей
230: npm install
231: ```
232: 
233: ### 6.3 Сборка бэкенда
234: ```bash
235: # Переход в директорию бэкенда
236: cd /Users/<USER>/quer-calc-next/backend-rust
237: 
238: # Сборка проекта в debug-режиме
239: cargo build
240: ```
241: 
242: ## 7. Настройка переменных окружения
243: 
244: ### 7.1 Создание файлов окружения
245: ```bash
246: # Переход в корневую директорию проекта
247: cd /Users/<USER>/quer-calc-next
248: 
249: # Создание .env.local для Next.js
250: cat > .env.local << EOF
251: # Базовые настройки
252: NODE_ENV=development
253: NEXT_PUBLIC_API_URL=https://localhost:8081
254: 
255: # Настройки Clerk
256: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
257: CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
258: CLERK_WEBHOOK_SECRET=your_webhook_secret
259: 
260: # Настройки Stripe
261: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
262: STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
263: NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_monthly_id
264: NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_annual_id
265: STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
266: 
267: # URL сайта для локальной разработки
268: NEXT_PUBLIC_SITE_URL=https://localhost:3001
269: 
270: # Дополнительные настройки
271: ALLOW_DIRECT_USER_ID=true
272: EOF
273: ```
274: 
275: ### 7.2 Создание переменных окружения для бэкенда
276: ```bash
277: # Переход в директорию бэкенда
278: cd /Users/<USER>/quer-calc-next/backend-rust
279: 
280: # Создание .env файла
281: cat > .env << EOF
282: CORS_ORIGINS=http://localhost:3000,https://localhost:3001
283: RATE_LIMIT_BURST=100
284: RATE_LIMIT_PER_SEC=50
285: EOF
286: ```
287: 
288: ## 8. Создание скриптов для удобной разработки
289: 
290: ### 8.1 Создание скрипта для запуска всего проекта
291: ```bash
292: # Создание скрипта в корневой директории проекта
293: cd /Users/<USER>/quer-calc-next
294: 
295: # Добавление скриптов в package.json
296: cat > dev-scripts.json << EOF
297: {
298:   "scripts": {
299:     "dev:front": "next dev",
300:     "dev:back": "cd backend-rust && cargo watch -x run",
301:     "dev:ssl": "~/scripts/start-ssl-proxy.sh",
302:     "dev": "concurrently \"npm run dev:front\" \"npm run dev:back\" \"npm run dev:ssl\""
303:   }
304: }
305: EOF
306: 
307: # Обновление package.json
308: npm install --save-dev concurrently
309: jq -s '.[0] * .[1]' package.json dev-scripts.json > package.json.new
310: mv package.json.new package.json
311: rm dev-scripts.json
312: ```
313: 
314: ## 9. Настройка инструментов разработки для Next.js
315: 
316: ### 9.1 Настройка ESLint
317: ```bash
318: # Установка ESLint и плагинов
319: npm install --save-dev eslint eslint-plugin-react eslint-plugin-react-hooks eslint-plugin-next
320: 
321: # Создание конфигурации ESLint
322: cat > .eslintrc.json << EOF
323: {
324:   "extends": [
325:     "eslint:recommended",
326:     "plugin:react/recommended",
327:     "plugin:react-hooks/recommended",
328:     "plugin:next/recommended"
329:   ],
330:   "rules": {
331:     "react/react-in-jsx-scope": "off"
332:   },
333:   "env": {
334:     "browser": true,
335:     "node": true,
336:     "es6": true
337:   }
338: }
339: EOF
340: ```
341: 
342: ### 9.2 Настройка Prettier
343: ```bash
344: # Установка Prettier
345: npm install --save-dev prettier
346: 
347: # Создание конфигурации Prettier
348: cat > .prettierrc << EOF
349: {
350:   "semi": true,
351:   "trailingComma": "es5",
352:   "singleQuote": true,
353:   "tabWidth": 2,
354:   "printWidth": 100
355: }
356: EOF
357: ```
358: 
359: ## 10. Настройка удобной отладки
360: 
361: ### 10.1 Настройка отладки для Next.js
362: ```bash
363: # Создание конфигурации отладки для VS Code
364: mkdir -p .vscode
365: cat > .vscode/launch.json << EOF
366: {
367:   "version": "0.2.0",
368:   "configurations": [
369:     {
370:       "name": "Next.js: debug server-side",
371:       "type": "node-terminal",
372:       "request": "launch",
373:       "command": "npm run dev:front"
374:     },
375:     {
376:       "name": "Next.js: debug client-side",
377:       "type": "chrome",
378:       "request": "launch",
379:       "url": "https://localhost:3001"
380:     },
381:     {
382:       "name": "Next.js: debug full stack",
383:       "type": "node-terminal",
384:       "request": "launch",
385:       "command": "npm run dev:front",
386:       "serverReadyAction": {
387:         "pattern": "started server on .+, url: (https?://.+)",
388:         "uriFormat": "https://localhost:3001",
389:         "action": "debugWithChrome"
390:       }
391:     }
392:   ]
393: }
394: EOF
395: ```
396: 
397: ### 10.2 Настройка отладки для Rust
398: ```bash
399: # Добавление конфигурации отладки для Rust в VS Code
400: cat >> .vscode/launch.json << EOF
401: ,
402:   {
403:     "name": "Debug Rust Backend",
404:     "type": "lldb",
405:     "request": "launch",
406:     "cargo": {
407:       "args": ["build", "--bin", "quer-calc"],
408:       "filter": {
409:         "name": "quer-calc",
410:         "kind": "bin"
411:       }
412:     },
413:     "args": [],
414:     "cwd": "${workspaceFolder}/backend-rust"
415:   }
416: EOF
417: ```
418: 
419: ## 11. Проверка установки и тестовый запуск
420: 
421: ### 11.1 Проверка установленных инструментов
422: ```bash
423: # Проверка Node.js и npm
424: node --version
425: npm --version
426: 
427: # Проверка Rust и Cargo
428: rustc --version
429: cargo --version
430: 
431: # Проверка SSL-сертификатов
432: ls -la ~/.local-certs
433: ```
434: 
435: ### 11.2 Запуск проекта
436: ```bash
437: # Переход в директорию проекта
438: cd /Users/<USER>/quer-calc-next
439: 
440: # Запуск проекта
441: npm run dev
442: 
443: # В браузере можно будет открыть:
444: # - Фронтенд: https://localhost:3001
445: # - Бэкенд API: https://localhost:8081
446: ```
447: 
448: ## 12. Дополнительные инструменты и настройки (опционально)
449: 
450: ### 12.1 Настройка Postman для тестирования API
451: ```bash
452: # Установка Postman через Homebrew
453: brew install --cask postman
454: 
455: # Импорт сертификатов в Postman:
456: # 1. Откройте Postman
457: # 2. Перейдите в Settings > Certificates
458: # 3. Выключите SSL certificate verification
459: # 4. Или добавьте CA сертификат из ~/.local-certs
460: ```
461: 
462: ### 12.2 Настройка MongoDB (если используется)
463: ```bash
464: # Установка MongoDB через Homebrew
465: brew tap mongodb/brew
466: brew install mongodb-community
467: 
468: # Запуск MongoDB
469: brew services start mongodb-community
470: 
471: # Установка MongoDB Compass (GUI)
472: brew install --cask mongodb-compass
473: ```
474: 
475: ### 12.3 Установка редактора (если еще не установлен)
476: ```bash
477: # Установка VS Code
478: brew install --cask visual-studio-code
479: 
480: # Установка полезных расширений для проекта
481: code --install-extension dbaeumer.vscode-eslint
482: code --install-extension esbenp.prettier-vscode
483: code --install-extension bradlc.vscode-tailwindcss
484: code --install-extension rust-lang.rust-analyzer
485: code --install-extension svelte.svelte-vscode
486: ```
487: 
488: ### 12.4 Настройка Docker (опционально, для совместимости с CI)
489: ```bash
490: # Установка Docker Desktop
491: brew install --cask docker
492: 
493: # Запуск Docker
494: open -a Docker
495: ```
496: 
497: ## Заключение
498: 
499: После выполнения всех шагов у вас будет полностью настроенное локальное окружение для разработки проекта quer-calc, включающее:
500: 
501: - Node.js и npm для разработки фронтенда на Next.js
502: - Rust и Cargo для разработки бэкенда
503: - Локальные SSL-сертификаты для безопасной разработки
504: - Правильно настроенные переменные окружения
505: - Удобные скрипты для запуска и отладки
506: - Инструменты для повышения качества кода (ESLint, Prettier)
507: 
508: Теперь вы можете начать разработку, запустив команду `npm run dev` в корневой директории проекта. Фронтенд будет доступен по адресу https://localhost:3001, а бэкенд API — по адресу https://localhost:8081.
````

## File: План развёртывания приложения и CI:CD/2. server-infrastructure-setup.md
````markdown
  1: # Подробный план настройки серверной инфраструктуры
  2: 
  3: В этом документе представлен детальный план настройки серверной инфраструктуры для проекта quer-calc, включая настройку отдельных окружений для staging (s.quer.us) и production (quer.us).
  4: 
  5: ## 1. Подготовка базовой структуры сервера
  6: 
  7: ### 1.1 Подключение к серверу
  8: ```bash
  9: # Подключение по SSH (как x0 - пользователь с правами sudo)
 10: ssh quer-x0
 11: ```
 12: 
 13: ### 1.2 Обновление системы и установка необходимых пакетов
 14: ```bash
 15: # Обновление списка пакетов и установка обновлений
 16: sudo apt update && sudo apt upgrade -y
 17: 
 18: # Установка базовых зависимостей
 19: sudo apt install -y build-essential pkg-config libssl-dev nodejs npm
 20: ```
 21: 
 22: ### 1.3 Установка Node.js нужной версии
 23: ```bash
 24: # Проверка текущей версии Node.js
 25: node --version
 26: 
 27: # Если требуется более новая версия (например, для Next.js), устанавливаем её через nodesource
 28: sudo apt remove -y nodejs npm  # удаляем старую версию, если есть
 29: curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
 30: sudo apt install -y nodejs
 31: 
 32: # Проверка установленной версии
 33: node --version
 34: npm --version
 35: 
 36: # Установка Yarn (опционально)
 37: sudo npm install -g yarn
 38: ```
 39: 
 40: ### 1.4 Создание структуры директорий для проекта
 41: ```bash
 42: # Создание основных директорий для приложений
 43: sudo mkdir -p /var/www/{quer.us,s.quer.us}/{current,releases,shared}
 44: 
 45: # Создание поддиректорий для хранения общих ресурсов
 46: sudo mkdir -p /var/www/{quer.us,s.quer.us}/shared/{logs,public,uploads,.env}
 47: 
 48: # Назначение прав пользователю duploy
 49: sudo chown -R duploy:duploy /var/www/quer.us /var/www/s.quer.us
 50: 
 51: # Установка правильных разрешений
 52: sudo chmod -R 755 /var/www
 53: ```
 54: 
 55: ## 2. Настройка Rust для бэкенда
 56: 
 57: ### 2.1 Установка Rust
 58: ```bash
 59: # Переключение на пользователя duploy для установки Rust
 60: sudo su - duploy
 61: 
 62: # Установка Rust через rustup
 63: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
 64: # Выбираем опцию 1) Proceed with installation (default)
 65: 
 66: # Настройка переменных окружения в текущей сессии
 67: source $HOME/.cargo/env
 68: 
 69: # Проверка установки
 70: rustc --version
 71: cargo --version
 72: 
 73: # Выход из сессии пользователя duploy
 74: exit
 75: ```
 76: 
 77: ### 2.2 Создание systemd-сервисов для бэкенда Rust
 78: 
 79: #### 2.2.1 Создание сервиса для staging
 80: 
 81: ```bash
 82: # Создание файла сервиса systemd для staging
 83: sudo nano /etc/systemd/system/quer-backend-staging.service
 84: ```
 85: 
 86: Содержимое файла:
 87: ```ini
 88: [Unit]
 89: Description=Quer Calculator Staging Backend
 90: After=network.target
 91: 
 92: [Service]
 93: User=duploy
 94: Group=duploy
 95: WorkingDirectory=/var/www/s.quer.us/current/backend-rust
 96: ExecStart=/var/www/s.quer.us/current/backend-rust/target/release/quer-calc
 97: Restart=always
 98: RestartSec=5
 99: Environment="RUST_ENV=staging"
100: Environment="CORS_ORIGINS=https://s.quer.us"
101: Environment="RATE_LIMIT_BURST=100"
102: Environment="RATE_LIMIT_PER_SEC=50"
103: 
104: # Загрузка переменных окружения из файла
105: EnvironmentFile=-/var/www/s.quer.us/shared/.env/backend.env
106: 
107: # Ограничения безопасности
108: NoNewPrivileges=true
109: PrivateTmp=true
110: ProtectSystem=full
111: ProtectHome=true
112: RestrictSUIDSGID=true
113: 
114: [Install]
115: WantedBy=multi-user.target
116: ```
117: 
118: #### 2.2.2 Создание сервиса для production
119: 
120: ```bash
121: # Создание файла сервиса systemd для production
122: sudo nano /etc/systemd/system/quer-backend-production.service
123: ```
124: 
125: Содержимое файла:
126: ```ini
127: [Unit]
128: Description=Quer Calculator Production Backend
129: After=network.target
130: 
131: [Service]
132: User=duploy
133: Group=duploy
134: WorkingDirectory=/var/www/quer.us/current/backend-rust
135: ExecStart=/var/www/quer.us/current/backend-rust/target/release/quer-calc
136: Restart=always
137: RestartSec=5
138: Environment="RUST_ENV=production"
139: Environment="CORS_ORIGINS=https://quer.us"
140: Environment="RATE_LIMIT_BURST=20"
141: Environment="RATE_LIMIT_PER_SEC=10"
142: 
143: # Загрузка переменных окружения из файла
144: EnvironmentFile=-/var/www/quer.us/shared/.env/backend.env
145: 
146: # Ограничения безопасности
147: NoNewPrivileges=true
148: PrivateTmp=true
149: ProtectSystem=full
150: ProtectHome=true
151: RestrictSUIDSGID=true
152: 
153: [Install]
154: WantedBy=multi-user.target
155: ```
156: 
157: #### 2.2.3 Создание директорий для переменных окружения
158: 
159: ```bash
160: # Создание директорий для .env файлов
161: sudo mkdir -p /var/www/{quer.us,s.quer.us}/shared/.env
162: sudo chown duploy:duploy /var/www/{quer.us,s.quer.us}/shared/.env
163: sudo chmod 750 /var/www/{quer.us,s.quer.us}/shared/.env
164: ```
165: 
166: ## 3. Настройка Next.js для фронтенда
167: 
168: ### 3.1 Настройка systemd-сервисов для Next.js
169: 
170: #### 3.1.1 Создание сервиса для staging
171: 
172: ```bash
173: # Создание файла сервиса systemd для staging
174: sudo nano /etc/systemd/system/quer-frontend-staging.service
175: ```
176: 
177: Содержимое файла:
178: ```ini
179: [Unit]
180: Description=Quer Calculator Staging Frontend
181: After=network.target
182: 
183: [Service]
184: User=duploy
185: Group=duploy
186: WorkingDirectory=/var/www/s.quer.us/current
187: ExecStart=/usr/bin/node /var/www/s.quer.us/current/next.config.server.js
188: Restart=always
189: RestartSec=5
190: Environment="NODE_ENV=production"
191: Environment="PORT=3000"
192: Environment="NEXT_PUBLIC_API_URL=https://s.quer.us/api"
193: Environment="NEXT_PUBLIC_SITE_URL=https://s.quer.us"
194: 
195: # Загрузка переменных окружения из файла
196: EnvironmentFile=-/var/www/s.quer.us/shared/.env/frontend.env
197: 
198: # Ограничения безопасности
199: NoNewPrivileges=true
200: PrivateTmp=true
201: ProtectSystem=full
202: ProtectHome=true
203: RestrictSUIDSGID=true
204: 
205: [Install]
206: WantedBy=multi-user.target
207: ```
208: 
209: #### 3.1.2 Создание сервиса для production
210: 
211: ```bash
212: # Создание файла сервиса systemd для production
213: sudo nano /etc/systemd/system/quer-frontend-production.service
214: ```
215: 
216: Содержимое файла:
217: ```ini
218: [Unit]
219: Description=Quer Calculator Production Frontend
220: After=network.target
221: 
222: [Service]
223: User=duploy
224: Group=duploy
225: WorkingDirectory=/var/www/quer.us/current
226: ExecStart=/usr/bin/node /var/www/quer.us/current/next.config.server.js
227: Restart=always
228: RestartSec=5
229: Environment="NODE_ENV=production"
230: Environment="PORT=3100"
231: Environment="NEXT_PUBLIC_API_URL=https://quer.us/api"
232: Environment="NEXT_PUBLIC_SITE_URL=https://quer.us"
233: 
234: # Загрузка переменных окружения из файла
235: EnvironmentFile=-/var/www/quer.us/shared/.env/frontend.env
236: 
237: # Ограничения безопасности
238: NoNewPrivileges=true
239: PrivateTmp=true
240: ProtectSystem=full
241: ProtectHome=true
242: RestrictSUIDSGID=true
243: 
244: [Install]
245: WantedBy=multi-user.target
246: ```
247: 
248: ### 3.2 Создание скрипта для запуска Next.js сервера
249: 
250: ```bash
251: # Для staging
252: sudo su - duploy
253: mkdir -p /var/www/s.quer.us/current
254: nano /var/www/s.quer.us/current/next.config.server.js
255: ```
256: 
257: Содержимое файла для staging:
258: ```javascript
259: // next.config.server.js
260: const { createServer } = require('http')
261: const { parse } = require('url')
262: const next = require('next')
263: 
264: const app = next({ dir: '.', dev: false })
265: const handle = app.getRequestHandler()
266: 
267: app.prepare().then(() => {
268:   createServer((req, res) => {
269:     const parsedUrl = parse(req.url, true)
270:     handle(req, res, parsedUrl)
271:   }).listen(process.env.PORT || 3000, (err) => {
272:     if (err) throw err
273:     console.log(`> Ready on http://localhost:${process.env.PORT || 3000}`)
274:   })
275: })
276: ```
277: 
278: ```bash
279: # Повторяем для production
280: mkdir -p /var/www/quer.us/current
281: nano /var/www/quer.us/current/next.config.server.js
282: ```
283: 
284: Содержимое файла для production (идентично staging, просто скопируйте):
285: ```javascript
286: // next.config.server.js
287: const { createServer } = require('http')
288: const { parse } = require('url')
289: const next = require('next')
290: 
291: const app = next({ dir: '.', dev: false })
292: const handle = app.getRequestHandler()
293: 
294: app.prepare().then(() => {
295:   createServer((req, res) => {
296:     const parsedUrl = parse(req.url, true)
297:     handle(req, res, parsedUrl)
298:   }).listen(process.env.PORT || 3100, (err) => {
299:     if (err) throw err
300:     console.log(`> Ready on http://localhost:${process.env.PORT || 3100}`)
301:   })
302: })
303: ```
304: 
305: ```bash
306: # Выход из сессии пользователя duploy
307: exit
308: ```
309: 
310: ### 3.3 Создание файлов окружения для Next.js
311: 
312: ```bash
313: # Переключаемся на пользователя duploy для создания .env файлов
314: sudo su - duploy
315: 
316: # Создание .env файла для staging
317: mkdir -p /var/www/s.quer.us/shared/.env
318: nano /var/www/s.quer.us/shared/.env/frontend.env
319: ```
320: 
321: Содержимое файла frontend.env для staging:
322: ```
323: # Настройки аутентификации
324: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
325: CLERK_SECRET_KEY=sk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
326: CLERK_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
327: 
328: # Настройки оплаты
329: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
330: STRIPE_SECRET_KEY=sk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
331: NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
332: NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
333: STRIPE_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
334: ```
335: 
336: ```bash
337: # Создание .env файла для production
338: mkdir -p /var/www/quer.us/shared/.env
339: nano /var/www/quer.us/shared/.env/frontend.env
340: ```
341: 
342: Содержимое файла frontend.env для production:
343: ```
344: # Настройки аутентификации
345: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
346: CLERK_SECRET_KEY=sk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
347: CLERK_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
348: 
349: # Настройки оплаты
350: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
351: STRIPE_SECRET_KEY=sk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
352: NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
353: NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
354: STRIPE_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
355: ```
356: 
357: ```bash
358: # Создание .env файлов для бэкенда Rust
359: nano /var/www/s.quer.us/shared/.env/backend.env
360: ```
361: 
362: Содержимое файла backend.env для staging:
363: ```
364: # Настройки CORS и безопасности
365: CORS_ORIGINS=https://s.quer.us,http://localhost:3000,https://localhost:3001
366: RATE_LIMIT_BURST=100
367: RATE_LIMIT_PER_SEC=50
368: ```
369: 
370: ```bash
371: # Создание .env файла для production бэкенда
372: nano /var/www/quer.us/shared/.env/backend.env
373: ```
374: 
375: Содержимое файла backend.env для production:
376: ```
377: # Настройки CORS и безопасности
378: CORS_ORIGINS=https://quer.us
379: RATE_LIMIT_BURST=150
380: RATE_LIMIT_PER_SEC=75
381: ```
382: 
383: ```bash
384: # Выход из сессии пользователя duploy
385: exit
386: ```
387: 
388: ### 3.4 Создание директорий для логов
389: 
390: ```bash
391: # Создание директорий для логов
392: sudo mkdir -p /var/www/{quer.us,s.quer.us}/shared/logs/{frontend,backend}
393: sudo chown -R duploy:duploy /var/www/{quer.us,s.quer.us}/shared/logs
394: ```
395: 
396: ## 4. Настройка Nginx для проксирования запросов
397: 
398: ### 4.1 Установка и базовая настройка Nginx
399: 
400: ```bash
401: # Установка Nginx
402: sudo apt install -y nginx
403: 
404: # Создание директории для кастомных конфигураций
405: sudo mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
406: ```
407: 
408: ### 4.2 Создание конфигурационных файлов для проектов
409: 
410: #### 4.2.1 Создание конфигурации для staging
411: 
412: ```bash
413: # Создание конфигурации Nginx для s.quer.us
414: sudo nano /etc/nginx/sites-available/s.quer.us
415: ```
416: 
417: Содержимое файла:
418: ```nginx
419: # Базовая конфигурация для staging
420: server {
421:     listen 80;
422:     server_name s.quer.us;
423:     
424:     # Редирект на HTTPS
425:     location / {
426:         return 301 https://$host$request_uri;
427:     }
428: }
429: 
430: server {
431:     listen 443 ssl;
432:     server_name s.quer.us;
433:     
434:     # SSL настройки (будут добавлены после настройки Cloudflare)
435:     ssl_certificate /etc/ssl/certs/cloudflare-s.quer.us.pem;
436:     ssl_certificate_key /etc/ssl/private/cloudflare-s.quer.us.key;
437:     
438:     # Логирование
439:     access_log /var/www/s.quer.us/shared/logs/nginx-access.log;
440:     error_log /var/www/s.quer.us/shared/logs/nginx-error.log;
441: 
442:     # Настройки безопасности
443:     add_header X-Frame-Options "SAMEORIGIN";
444:     add_header X-XSS-Protection "1; mode=block";
445:     add_header X-Content-Type-Options "nosniff";
446:     add_header Referrer-Policy "strict-origin-when-cross-origin";
447:     add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';";
448:     
449:     # Проксирование запросов к API бэкенда
450:     location /api/ {
451:         proxy_pass http://localhost:8080/api/;
452:         proxy_http_version 1.1;
453:         proxy_set_header Upgrade $http_upgrade;
454:         proxy_set_header Connection 'upgrade';
455:         proxy_set_header Host $host;
456:         proxy_set_header X-Real-IP $remote_addr;
457:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
458:         proxy_set_header X-Forwarded-Proto $scheme;
459:         proxy_cache_bypass $http_upgrade;
460:     }
461:     
462:     # Проксирование запросов к фронтенду Next.js
463:     location / {
464:         proxy_pass http://localhost:3000;
465:         proxy_http_version 1.1;
466:         proxy_set_header Upgrade $http_upgrade;
467:         proxy_set_header Connection 'upgrade';
468:         proxy_set_header Host $host;
469:         proxy_set_header X-Real-IP $remote_addr;
470:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
471:         proxy_set_header X-Forwarded-Proto $scheme;
472:         proxy_cache_bypass $http_upgrade;
473:     }
474:     
475:     # Кэширование статических файлов
476:     location /_next/static/ {
477:         proxy_pass http://localhost:3000/_next/static/;
478:         proxy_cache_valid 60m;
479:         proxy_set_header Host $host;
480:         expires 1y;
481:         add_header Cache-Control "public, max-age=31536000, immutable";
482:     }
483:     
484:     location /static/ {
485:         proxy_pass http://localhost:3000/static/;
486:         proxy_cache_valid 60m;
487:         proxy_set_header Host $host;
488:         expires 1y;
489:         add_header Cache-Control "public, max-age=31536000, immutable";
490:     }
491: }
492: ```
493: 
494: #### 4.2.2 Создание конфигурации для production
495: 
496: ```bash
497: # Создание конфигурации Nginx для quer.us
498: sudo nano /etc/nginx/sites-available/quer.us
499: ```
500: 
501: Содержимое файла:
502: ```nginx
503: # Базовая конфигурация для production
504: server {
505:     listen 80;
506:     server_name quer.us www.quer.us;
507:     
508:     # Редирект на HTTPS
509:     location / {
510:         return 301 https://quer.us$request_uri;
511:     }
512: }
513: 
514: server {
515:     listen 443 ssl;
516:     server_name www.quer.us;
517:     
518:     # Редирект с www на без www
519:     return 301 https://quer.us$request_uri;
520: }
521: 
522: server {
523:     listen 443 ssl;
524:     server_name quer.us;
525:     
526:     # SSL настройки (будут добавлены после настройки Cloudflare)
527:     ssl_certificate /etc/ssl/certs/cloudflare-quer.us.pem;
528:     ssl_certificate_key /etc/ssl/private/cloudflare-quer.us.key;
529:     
530:     # Логирование
531:     access_log /var/www/quer.us/shared/logs/nginx-access.log;
532:     error_log /var/www/quer.us/shared/logs/nginx-error.log;
533: 
534:     # Настройки безопасности
535:     add_header X-Frame-Options "SAMEORIGIN";
536:     add_header X-XSS-Protection "1; mode=block";
537:     add_header X-Content-Type-Options "nosniff";
538:     add_header Referrer-Policy "strict-origin-when-cross-origin";
539:     add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';";
540:     
541:     # Проксирование запросов к API бэкенда
542:     location /api/ {
543:         proxy_pass http://localhost:8080/api/;
544:         proxy_http_version 1.1;
545:         proxy_set_header Upgrade $http_upgrade;
546:         proxy_set_header Connection 'upgrade';
547:         proxy_set_header Host $host;
548:         proxy_set_header X-Real-IP $remote_addr;
549:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
550:         proxy_set_header X-Forwarded-Proto $scheme;
551:         proxy_cache_bypass $http_upgrade;
552:     }
553:     
554:     # Проксирование запросов к фронтенду Next.js
555:     location / {
556:         proxy_pass http://localhost:3100;
557:         proxy_http_version 1.1;
558:         proxy_set_header Upgrade $http_upgrade;
559:         proxy_set_header Connection 'upgrade';
560:         proxy_set_header Host $host;
561:         proxy_set_header X-Real-IP $remote_addr;
562:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
563:         proxy_set_header X-Forwarded-Proto $scheme;
564:         proxy_cache_bypass $http_upgrade;
565:     }
566:     
567:     # Кэширование статических файлов
568:     location /_next/static/ {
569:         proxy_pass http://localhost:3100/_next/static/;
570:         proxy_cache_valid 60m;
571:         proxy_set_header Host $host;
572:         expires 1y;
573:         add_header Cache-Control "public, max-age=31536000, immutable";
574:     }
575:     
576:     location /static/ {
577:         proxy_pass http://localhost:3100/static/;
578:         proxy_cache_valid 60m;
579:         proxy_set_header Host $host;
580:         expires 1y;
581:         add_header Cache-Control "public, max-age=31536000, immutable";
582:     }
583: }
584: ```
585: 
586: ### 4.3 Активация конфигураций и проверка синтаксиса
587: 
588: ```bash
589: # Создание символических ссылок для активации конфигураций
590: sudo ln -s /etc/nginx/sites-available/s.quer.us /etc/nginx/sites-enabled/
591: sudo ln -s /etc/nginx/sites-available/quer.us /etc/nginx/sites-enabled/
592: 
593: # Проверка синтаксиса конфигурационных файлов Nginx
594: sudo nginx -t
595: 
596: # Перезапуск Nginx для применения конфигурации
597: sudo systemctl restart nginx
598: ```
599: 
600: ## 5. Настройка разрешений и дополнительные файрвол-правила
601: 
602: ### 5.1 Настройка разрешений для деплоя
603: 
604: ```bash
605: # Обеспечение правильных прав для развертывания
606: sudo chown -R duploy:duploy /var/www/quer.us /var/www/s.quer.us
607: 
608: # Обеспечение необходимых разрешений для Nginx логов
609: sudo chmod -R 755 /var/www/quer.us/shared/logs /var/www/s.quer.us/shared/logs
610: ```
611: 
612: ### 5.2 Обновление правил файрвола
613: 
614: ```bash
615: # Проверка текущих правил
616: sudo ufw status
617: 
618: # Обеспечение доступа к Nginx (если правила уже не активированы)
619: sudo ufw allow 80/tcp
620: sudo ufw allow 443/tcp
621: 
622: # Применение изменений
623: sudo ufw reload
624: ```
625: 
626: ## 6. Подготовка временных директорий для деплоя
627: 
628: ```bash
629: # Переход на пользователя duploy для создания структуры
630: sudo su - duploy
631: 
632: # Создание простых placeholder файлов для тестирования
633: mkdir -p /var/www/s.quer.us/current/public
634: echo "Staging environment" > /var/www/s.quer.us/current/public/index.html
635: 
636: mkdir -p /var/www/quer.us/current/public
637: echo "Production environment" > /var/www/quer.us/current/public/index.html
638: 
639: exit
640: ```
641: 
642: ## 7. Настройка базовых инструментов мониторинга
643: 
644: ### 7.1 Установка инструментов мониторинга
645: 
646: ```bash
647: # Установка базовых инструментов мониторинга
648: sudo apt install -y htop iotop iftop
649: 
650: # Установка netdata для базового мониторинга системы
651: sudo apt install -y netdata
652: ```
653: 
654: ### 7.2 Настройка netdata для локального доступа
655: 
656: ```bash
657: # Редактирование конфигурации netdata
658: sudo nano /etc/netdata/netdata.conf
659: ```
660: 
661: Настройте доступ только с localhost:
662: ```
663: [web]
664: allow connections from = localhost 127.0.0.1 ::1
665: ```
666: 
667: ```bash
668: # Перезапуск netdata
669: sudo systemctl restart netdata
670: ```
671: 
672: ## 8. Первоначальная проверка установки
673: 
674: ### 8.1 Активация сервисов
675: 
676: ```bash
677: # Перезагрузка конфигурации systemd
678: sudo systemctl daemon-reload
679: 
680: # Проверка и запуск сервисов (если все файлы на месте)
681: sudo systemctl start quer-backend-staging.service
682: sudo systemctl start quer-frontend-staging.service
683: 
684: # Включение автозапуска сервисов при загрузке системы
685: sudo systemctl enable quer-backend-staging.service
686: sudo systemctl enable quer-frontend-staging.service
687: sudo systemctl enable quer-backend-production.service
688: sudo systemctl enable quer-frontend-production.service
689: 
690: # Проверка статуса сервисов
691: sudo systemctl status quer-backend-staging.service
692: sudo systemctl status quer-frontend-staging.service
693: ```
694: 
695: ### 8.2 Проверка конфигурации Nginx
696: 
697: ```bash
698: # Проверка синтаксиса Nginx
699: sudo nginx -t
700: 
701: # Проверка статуса Nginx
702: sudo systemctl status nginx
703: ```
704: 
705: ## 9. Настройка логирования и ротации логов
706: 
707: ### 9.1 Настройка логирования для приложений
708: 
709: ```bash
710: # Создание конфигурации logrotate для логов приложения
711: sudo nano /etc/logrotate.d/quer-apps
712: ```
713: 
714: Содержимое файла:
715: ```
716: /var/www/*/shared/logs/*/*.log {
717:     daily
718:     missingok
719:     rotate 14
720:     compress
721:     delaycompress
722:     notifempty
723:     create 0640 duploy duploy
724:     sharedscripts
725:     postrotate
726:         [ -s /run/nginx.pid ] && kill -USR1 `cat /run/nginx.pid`
727:     endscript
728: }
729: ```
730: 
731: ### 9.2 Настройка logwatch для мониторинга логов
732: 
733: ```bash
734: # Установка logwatch
735: sudo apt install -y logwatch
736: 
737: # Настройка ежедневных отчетов
738: sudo nano /etc/cron.daily/00logwatch
739: ```
740: 
741: ## 10. Заключительные шаги
742: 
743: ### 10.1 Перезагрузка сервера для проверки автозапуска
744: 
745: ```bash
746: # Перезагрузка сервера
747: sudo reboot
748: ```
749: 
750: ### 10.2 Проверка после перезагрузки
751: 
752: ```bash
753: # Подключение по SSH после перезагрузки
754: ssh quer-x0
755: 
756: # Проверка статуса сервисов
757: sudo systemctl status nginx
758: sudo systemctl status quer-backend-staging.service
759: sudo systemctl status quer-frontend-staging.service
760: ```
761: 
762: ## Заключение
763: 
764: После выполнения всех шагов вы будете иметь полностью настроенную серверную инфраструктуру для проекта quer-calc с отдельными окружениями для staging и production. Nginx будет настроен для проксирования запросов к соответствующим приложениям, а systemd будет управлять запуском и перезапуском сервисов.
765: 
766: Дальнейшие шаги:
767: - Настройка SSL с использованием Cloudflare
768: - Настройка процесса деплоя
769: - Детальная настройка мониторинга и оповещений
770: - Создание резервного копирования
771: 
772: Эта конфигурация обеспечивает надежную основу для вашего приложения и подготавливает сервер к приему реальных запросов и деплою кода.
````

## File: План развёртывания приложения и CI:CD/3. nginx-ssl-config.md
````markdown
   1: # Подробный план настройки Nginx и SSL с Cloudflare
   2: 
   3: В этом документе представлен детальный план настройки Nginx и SSL с использованием Cloudflare для проекта quer-calc, включая оптимизацию производительности и кэширования.
   4: 
   5: ## 1. Настройка виртуальных хостов в Nginx
   6: 
   7: ### 1.1 Предварительная проверка и подготовка
   8: 
   9: ```bash
  10: # Подключение к серверу
  11: ssh quer-x0
  12: 
  13: # Проверка установки Nginx
  14: nginx -v
  15: 
  16: # Если Nginx не установлен, установите его
  17: sudo apt update
  18: sudo apt install -y nginx
  19: 
  20: # Создание директорий для хранения конфигураций
  21: sudo mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
  22: sudo mkdir -p /etc/nginx/conf.d/includes
  23: ```
  24: 
  25: ### 1.2 Создание общего конфигурационного файла для оптимизаций
  26: 
  27: Мы создадим общие включаемые файлы для унификации настроек между сайтами:
  28: 
  29: ```bash
  30: # Создание общего файла для оптимизации SSL
  31: sudo nano /etc/nginx/conf.d/includes/ssl-params.conf
  32: ```
  33: 
  34: Содержимое файла `ssl-params.conf`:
  35: ```nginx
  36: # Оптимальные параметры SSL
  37: ssl_protocols TLSv1.2 TLSv1.3;
  38: ssl_prefer_server_ciphers on;
  39: ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
  40: ssl_session_cache shared:SSL:10m;
  41: ssl_session_timeout 1d;
  42: ssl_session_tickets off;
  43: ssl_ecdh_curve secp384r1;
  44: 
  45: # OCSP Stapling
  46: ssl_stapling on;
  47: ssl_stapling_verify on;
  48: resolver ******* ******* ******* ******* valid=300s;
  49: resolver_timeout 5s;
  50: 
  51: # Заголовки безопасности
  52: add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
  53: add_header X-Frame-Options "SAMEORIGIN" always;
  54: add_header X-Content-Type-Options "nosniff" always;
  55: add_header X-XSS-Protection "1; mode=block" always;
  56: add_header Referrer-Policy "strict-origin-when-cross-origin" always;
  57: ```
  58: 
  59: ```bash
  60: # Создание общего файла для оптимизации кэширования
  61: sudo nano /etc/nginx/conf.d/includes/cache-params.conf
  62: ```
  63: 
  64: Содержимое файла `cache-params.conf`:
  65: ```nginx
  66: # Кэширование статических файлов
  67: location ~* \.(jpg|jpeg|png|gif|ico|css|js|webp|svg|woff|woff2|ttf|eot)$ {
  68:     expires 1y;
  69:     add_header Cache-Control "public, max-age=31536000, immutable";
  70:     access_log off;
  71: }
  72: 
  73: # Специальные правила для файлов Next.js
  74: location /_next/static/ {
  75:     expires 1y;
  76:     add_header Cache-Control "public, max-age=31536000, immutable";
  77:     access_log off;
  78: }
  79: 
  80: location /_next/image {
  81:     expires 1w;
  82:     add_header Cache-Control "public, max-age=604800";
  83: }
  84: 
  85: location /_next/data/ {
  86:     expires 1d;
  87:     add_header Cache-Control "public, max-age=86400";
  88: }
  89: ```
  90: 
  91: ```bash
  92: # Создание общего файла для настроек сжатия
  93: sudo nano /etc/nginx/conf.d/includes/compression.conf
  94: ```
  95: 
  96: Содержимое файла `compression.conf`:
  97: ```nginx
  98: # Настройки Gzip сжатия
  99: gzip on;
 100: gzip_vary on;
 101: gzip_proxied any;
 102: gzip_comp_level 6;
 103: gzip_buffers 16 8k;
 104: gzip_http_version 1.1;
 105: gzip_min_length 256;
 106: gzip_types
 107:     application/atom+xml
 108:     application/geo+json
 109:     application/javascript
 110:     application/json
 111:     application/ld+json
 112:     application/manifest+json
 113:     application/rdf+xml
 114:     application/rss+xml
 115:     application/vnd.ms-fontobject
 116:     application/wasm
 117:     application/x-font-ttf
 118:     application/x-javascript
 119:     application/x-web-app-manifest+json
 120:     application/xhtml+xml
 121:     application/xml
 122:     font/collection
 123:     font/eot
 124:     font/opentype
 125:     font/otf
 126:     font/ttf
 127:     image/bmp
 128:     image/svg+xml
 129:     image/x-icon
 130:     text/cache-manifest
 131:     text/calendar
 132:     text/css
 133:     text/javascript
 134:     text/markdown
 135:     text/plain
 136:     text/vcard
 137:     text/vnd.rim.location.xloc
 138:     text/vtt
 139:     text/x-component
 140:     text/x-cross-domain-policy;
 141: ```
 142: 
 143: ### 1.3 Настройка виртуального хоста для staging (s.quer.us)
 144: 
 145: ```bash
 146: # Создание конфигурации для s.quer.us
 147: sudo nano /etc/nginx/sites-available/s.quer.us.conf
 148: ```
 149: 
 150: Содержимое файла `s.quer.us.conf`:
 151: ```nginx
 152: # HTTP сервер - редирект на HTTPS
 153: server {
 154:     listen 80;
 155:     listen [::]:80;
 156:     server_name s.quer.us;
 157:     
 158:     # Лог файлы
 159:     access_log /var/www/s.quer.us/shared/logs/nginx-access.log;
 160:     error_log /var/www/s.quer.us/shared/logs/nginx-error.log warn;
 161:     
 162:     # Редирект на HTTPS
 163:     location / {
 164:         return 301 https://$host$request_uri;
 165:     }
 166:     
 167:     # Путь для проверки Let's Encrypt (если будет использоваться)
 168:     location /.well-known/acme-challenge/ {
 169:         root /var/www/s.quer.us/shared/acme;
 170:     }
 171: }
 172: 
 173: # HTTPS сервер
 174: server {
 175:     listen 443 ssl http2;
 176:     listen [::]:443 ssl http2;
 177:     server_name s.quer.us;
 178:     
 179:     # Директории сайта
 180:     root /var/www/s.quer.us/current/public;
 181:     
 182:     # Лог файлы
 183:     access_log /var/www/s.quer.us/shared/logs/nginx-access.log;
 184:     error_log /var/www/s.quer.us/shared/logs/nginx-error.log warn;
 185:     
 186:     # SSL сертификаты (пути будут обновлены после получения сертификатов)
 187:     ssl_certificate /etc/ssl/cloudflare/s.quer.us.pem;
 188:     ssl_certificate_key /etc/ssl/cloudflare/s.quer.us.key;
 189:     
 190:     # Включение общих параметров
 191:     include /etc/nginx/conf.d/includes/ssl-params.conf;
 192:     include /etc/nginx/conf.d/includes/compression.conf;
 193:     
 194:     # Проксирование запросов к API бэкенду
 195:     location /api/ {
 196:         proxy_pass http://localhost:8080/api/;
 197:         proxy_http_version 1.1;
 198:         proxy_set_header Upgrade $http_upgrade;
 199:         proxy_set_header Connection 'upgrade';
 200:         proxy_set_header Host $host;
 201:         proxy_set_header X-Real-IP $remote_addr;
 202:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 203:         proxy_set_header X-Forwarded-Proto $scheme;
 204:         proxy_cache_bypass $http_upgrade;
 205:         
 206:         # Настройки таймаутов для API (можно увеличить для длительных операций)
 207:         proxy_connect_timeout 60s;
 208:         proxy_send_timeout 60s;
 209:         proxy_read_timeout 60s;
 210:     }
 211:     
 212:     # Обработка webhook-запросов (для Clerk, Stripe и т.д.)
 213:     location /api/webhook/ {
 214:         proxy_pass http://localhost:8080/api/webhook/;
 215:         proxy_http_version 1.1;
 216:         proxy_set_header Host $host;
 217:         proxy_set_header X-Real-IP $remote_addr;
 218:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 219:         proxy_set_header X-Forwarded-Proto $scheme;
 220:         
 221:         # Увеличенные таймауты для webhook
 222:         proxy_connect_timeout 180s;
 223:         proxy_send_timeout 180s;
 224:         proxy_read_timeout 180s;
 225:     }
 226:     
 227:     # Проксирование к фронтенду Next.js
 228:     location / {
 229:         proxy_pass http://localhost:3000;
 230:         proxy_http_version 1.1;
 231:         proxy_set_header Upgrade $http_upgrade;
 232:         proxy_set_header Connection 'upgrade';
 233:         proxy_set_header Host $host;
 234:         proxy_set_header X-Real-IP $remote_addr;
 235:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 236:         proxy_set_header X-Forwarded-Proto $scheme;
 237:         proxy_cache_bypass $http_upgrade;
 238:         
 239:         # Буферизация ответов для лучшей производительности
 240:         proxy_buffering on;
 241:         proxy_buffer_size 128k;
 242:         proxy_buffers 4 256k;
 243:         proxy_busy_buffers_size 256k;
 244:     }
 245:     
 246:     # Включение настроек кэширования статического контента
 247:     include /etc/nginx/conf.d/includes/cache-params.conf;
 248:     
 249:     # Отдача favicon.ico
 250:     location = /favicon.ico {
 251:         log_not_found off;
 252:         access_log off;
 253:     }
 254:     
 255:     # Отдача robots.txt
 256:     location = /robots.txt {
 257:         log_not_found off;
 258:         access_log off;
 259:     }
 260:     
 261:     # Запрет доступа к скрытым файлам
 262:     location ~ /\. {
 263:         deny all;
 264:     }
 265: }
 266: ```
 267: 
 268: ### 1.4 Настройка виртуального хоста для production (quer.us)
 269: 
 270: ```bash
 271: # Создание конфигурации для quer.us
 272: sudo nano /etc/nginx/sites-available/quer.us.conf
 273: ```
 274: 
 275: Содержимое файла `quer.us.conf`:
 276: ```nginx
 277: # HTTP сервер - редирект на HTTPS
 278: server {
 279:     listen 80;
 280:     listen [::]:80;
 281:     server_name quer.us www.quer.us;
 282:     
 283:     # Лог файлы
 284:     access_log /var/www/quer.us/shared/logs/nginx-access.log;
 285:     error_log /var/www/quer.us/shared/logs/nginx-error.log warn;
 286:     
 287:     # Редирект на HTTPS
 288:     location / {
 289:         return 301 https://quer.us$request_uri;
 290:     }
 291:     
 292:     # Путь для проверки Let's Encrypt (если будет использоваться)
 293:     location /.well-known/acme-challenge/ {
 294:         root /var/www/quer.us/shared/acme;
 295:     }
 296: }
 297: 
 298: # HTTPS редирект с www на без www
 299: server {
 300:     listen 443 ssl http2;
 301:     listen [::]:443 ssl http2;
 302:     server_name www.quer.us;
 303:     
 304:     # SSL сертификаты (пути будут обновлены после получения сертификатов)
 305:     ssl_certificate /etc/ssl/cloudflare/quer.us.pem;
 306:     ssl_certificate_key /etc/ssl/cloudflare/quer.us.key;
 307:     
 308:     # Включение общих SSL параметров
 309:     include /etc/nginx/conf.d/includes/ssl-params.conf;
 310:     
 311:     # Редирект с www на без www
 312:     return 301 https://quer.us$request_uri;
 313: }
 314: 
 315: # HTTPS основной сервер
 316: server {
 317:     listen 443 ssl http2;
 318:     listen [::]:443 ssl http2;
 319:     server_name quer.us;
 320:     
 321:     # Директории сайта
 322:     root /var/www/quer.us/current/public;
 323:     
 324:     # Лог файлы
 325:     access_log /var/www/quer.us/shared/logs/nginx-access.log;
 326:     error_log /var/www/quer.us/shared/logs/nginx-error.log warn;
 327:     
 328:     # SSL сертификаты (пути будут обновлены после получения сертификатов)
 329:     ssl_certificate /etc/ssl/cloudflare/quer.us.pem;
 330:     ssl_certificate_key /etc/ssl/cloudflare/quer.us.key;
 331:     
 332:     # Включение общих параметров
 333:     include /etc/nginx/conf.d/includes/ssl-params.conf;
 334:     include /etc/nginx/conf.d/includes/compression.conf;
 335:     
 336:     # Проксирование запросов к API бэкенду
 337:     location /api/ {
 338:         proxy_pass http://localhost:8080/api/;
 339:         proxy_http_version 1.1;
 340:         proxy_set_header Upgrade $http_upgrade;
 341:         proxy_set_header Connection 'upgrade';
 342:         proxy_set_header Host $host;
 343:         proxy_set_header X-Real-IP $remote_addr;
 344:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 345:         proxy_set_header X-Forwarded-Proto $scheme;
 346:         proxy_cache_bypass $http_upgrade;
 347:         
 348:         # Настройки таймаутов для API
 349:         proxy_connect_timeout 60s;
 350:         proxy_send_timeout 60s;
 351:         proxy_read_timeout 60s;
 352:     }
 353:     
 354:     # Обработка webhook-запросов (для Clerk, Stripe и т.д.)
 355:     location /api/webhook/ {
 356:         proxy_pass http://localhost:8080/api/webhook/;
 357:         proxy_http_version 1.1;
 358:         proxy_set_header Host $host;
 359:         proxy_set_header X-Real-IP $remote_addr;
 360:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 361:         proxy_set_header X-Forwarded-Proto $scheme;
 362:         
 363:         # Увеличенные таймауты для webhook
 364:         proxy_connect_timeout 180s;
 365:         proxy_send_timeout 180s;
 366:         proxy_read_timeout 180s;
 367:     }
 368:     
 369:     # Проксирование к фронтенду Next.js
 370:     location / {
 371:         proxy_pass http://localhost:3100;
 372:         proxy_http_version 1.1;
 373:         proxy_set_header Upgrade $http_upgrade;
 374:         proxy_set_header Connection 'upgrade';
 375:         proxy_set_header Host $host;
 376:         proxy_set_header X-Real-IP $remote_addr;
 377:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 378:         proxy_set_header X-Forwarded-Proto $scheme;
 379:         proxy_cache_bypass $http_upgrade;
 380:         
 381:         # Буферизация ответов для лучшей производительности
 382:         proxy_buffering on;
 383:         proxy_buffer_size 128k;
 384:         proxy_buffers 4 256k;
 385:         proxy_busy_buffers_size 256k;
 386:     }
 387:     
 388:     # Включение настроек кэширования статического контента
 389:     include /etc/nginx/conf.d/includes/cache-params.conf;
 390:     
 391:     # Отдача favicon.ico
 392:     location = /favicon.ico {
 393:         log_not_found off;
 394:         access_log off;
 395:     }
 396:     
 397:     # Отдача robots.txt
 398:     location = /robots.txt {
 399:         log_not_found off;
 400:         access_log off;
 401:     }
 402:     
 403:     # Запрет доступа к скрытым файлам
 404:     location ~ /\. {
 405:         deny all;
 406:     }
 407: }
 408: ```
 409: 
 410: ### 1.5 Активация конфигураций и проверка синтаксиса
 411: 
 412: ```bash
 413: # Создание символических ссылок для активации конфигураций
 414: sudo ln -sf /etc/nginx/sites-available/s.quer.us.conf /etc/nginx/sites-enabled/
 415: sudo ln -sf /etc/nginx/sites-available/quer.us.conf /etc/nginx/sites-enabled/
 416: 
 417: # Удаление дефолтной конфигурации (если существует)
 418: sudo rm -f /etc/nginx/sites-enabled/default
 419: 
 420: # Проверка конфигурации Nginx на наличие ошибок
 421: sudo nginx -t
 422: ```
 423: 
 424: ## 2. Настройка SSL для Origin сервера с Cloudflare
 425: 
 426: ### 2.1 Подготовка директорий для SSL-сертификатов
 427: 
 428: ```bash
 429: # Создание директории для сертификатов Cloudflare
 430: sudo mkdir -p /etc/ssl/cloudflare
 431: sudo chmod 700 /etc/ssl/cloudflare
 432: ```
 433: 
 434: ### 2.2 Получение Origin сертификатов от Cloudflare
 435: 
 436: Для получения Origin сертификатов необходимо перейти в панель управления Cloudflare:
 437: 
 438: 1. Войдите в аккаунт Cloudflare
 439: 2. Выберите нужный домен (quer.us)
 440: 3. Перейдите в раздел SSL/TLS > Origin Server
 441: 4. Нажмите "Create Certificate"
 442: 5. Выберите следующие параметры:
 443:    - Private key type: RSA (2048)
 444:    - Hostnames: quer.us, *.quer.us, s.quer.us
 445:    - Validity period: 15 years (или максимальный доступный период)
 446: 6. Нажмите "Create"
 447: 7. Скопируйте содержимое сертификата и приватного ключа
 448: 
 449: ```bash
 450: # Создание файла сертификата для quer.us
 451: sudo nano /etc/ssl/cloudflare/quer.us.pem
 452: # Вставьте содержимое Origin Certificate (сертификата)
 453: 
 454: # Создание файла ключа для quer.us
 455: sudo nano /etc/ssl/cloudflare/quer.us.key
 456: # Вставьте содержимое Private key (приватного ключа)
 457: 
 458: # Повторить то же самое для s.quer.us или скопировать те же файлы
 459: sudo cp /etc/ssl/cloudflare/quer.us.pem /etc/ssl/cloudflare/s.quer.us.pem
 460: sudo cp /etc/ssl/cloudflare/quer.us.key /etc/ssl/cloudflare/s.quer.us.key
 461: ```
 462: 
 463: ### 2.3 Настройка прав доступа для сертификатов
 464: 
 465: ```bash
 466: # Установка правильных прав доступа для сертификатов
 467: sudo chmod 644 /etc/ssl/cloudflare/*.pem
 468: sudo chmod 600 /etc/ssl/cloudflare/*.key
 469: sudo chown root:root /etc/ssl/cloudflare/*
 470: ```
 471: 
 472: Если не работает, то попробовать вот так:
 473: 
 474: ```bash
 475: # Установка правильных прав доступа для сертификатов (указываем файлы явно)
 476: sudo chmod 644 /etc/ssl/cloudflare/quer.us.pem /etc/ssl/cloudflare/s.quer.us.pem
 477: sudo chmod 600 /etc/ssl/cloudflare/quer.us.key /etc/ssl/cloudflare/s.quer.us.key
 478: sudo chown root:root /etc/ssl/cloudflare/quer.us.pem /etc/ssl/cloudflare/s.quer.us.pem /etc/ssl/cloudflare/quer.us.key /etc/ssl/cloudflare/s.quer.us.key
 479: ```
 480: 
 481: ### 2.4 Создание скрипта для мониторинга срока действия сертификатов
 482: 
 483: ```bash
 484: # Создание скрипта для проверки срока действия сертификатов
 485: sudo nano /usr/local/bin/check-ssl-expiry
 486: ```
 487: 
 488: Содержимое скрипта `check-ssl-expiry`:
 489: 
 490: ```bash
 491: #!/bin/bash
 492: 
 493: # Скрипт для проверки срока истечения SSL-сертификатов
 494: 
 495: CERTS=("/etc/ssl/cloudflare/quer.us.pem" "/etc/ssl/cloudflare/s.quer.us.pem")
 496: THRESHOLD_DAYS=30
 497: TELEGRAM_BOT_TOKEN="**********************************************"
 498: TELEGRAM_CHAT_ID="506972189"
 499: 
 500: # Функция для отправки уведомлений в Telegram
 501: send_telegram_message() {
 502:     local message="$1"
 503:     curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
 504:         -d chat_id="$TELEGRAM_CHAT_ID" \
 505:         -d text="$message" \
 506:         -d parse_mode="HTML"
 507: }
 508: 
 509: for CERT in "${CERTS[@]}"; do
 510:     if [ ! -f "$CERT" ]; then
 511:         echo "Сертификат не найден: $CERT"
 512:         continue
 513:     fi
 514:     
 515:     # Получаем дату истечения сертификата
 516:     EXPIRY_DATE=$(openssl x509 -enddate -noout -in "$CERT" | cut -d= -f2)
 517:     EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
 518:     CURRENT_EPOCH=$(date +%s)
 519:     
 520:     # Вычисляем оставшееся количество дней
 521:     DAYS_LEFT=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))
 522:     
 523:     echo "Сертификат $CERT истекает через $DAYS_LEFT дней"
 524:     
 525:     # Если остается меньше указанного количества дней, отправляем уведомление
 526:     if [ $DAYS_LEFT -lt $THRESHOLD_DAYS ]; then
 527:         MESSAGE="⚠️ <b>Внимание!</b> SSL сертификат $CERT истекает через $DAYS_LEFT дней!"
 528:         send_telegram_message "$MESSAGE"
 529:         echo "$MESSAGE"
 530:     fi
 531: done
 532: ```
 533: 
 534: ```bash
 535: # Делаем скрипт исполняемым
 536: sudo chmod +x /usr/local/bin/check-ssl-expiry
 537: 
 538: # Добавляем скрипт в cron для регулярной проверки
 539: sudo nano /etc/cron.weekly/check-ssl
 540: ```
 541: 
 542: Содержимое файла `/etc/cron.weekly/check-ssl`:
 543: ```bash
 544: #!/bin/bash
 545: /usr/local/bin/check-ssl-expiry
 546: ```
 547: 
 548: ```bash
 549: # Делаем cron скрипт исполняемым
 550: sudo chmod +x /etc/cron.weekly/check-ssl
 551: ```
 552: 
 553: ### 2.5 Настройка автоматического обновления сертификатов (опционально)
 554: 
 555: Если вы обновляете сертификаты Cloudflare реже чем раз в год, вы можете настроить напоминания через Telegram/Email, как в скрипте выше. Если вы хотите автоматизировать процесс обновления через Cloudflare API, необходимо создать API токен:
 556: 
 557: ```bash
 558: # Создание скрипта для автоматического обновления сертификатов через API
 559: sudo nano /usr/local/bin/update-cloudflare-certs.py
 560: ```
 561: 
 562: Содержимое скрипта `update-cloudflare-certs.py` (требует настройки API токена Cloudflare):
 563: ```python
 564: #!/usr/bin/env python3
 565: 
 566: import requests
 567: import json
 568: import os
 569: import subprocess
 570: from datetime import datetime
 571: 
 572: # Настройки
 573: CLOUDFLARE_API_TOKEN = "ВАШ_API_ТОКЕН"  # Токен с доступом к Origin Certificates
 574: ZONE_ID = "ВАШ_ZONE_ID"  # ID зоны для домена quer.us
 575: HOSTNAMES = ["quer.us", "*.quer.us", "s.quer.us"]
 576: CERT_VALIDITY = 5475  # 15 лет в днях
 577: CERT_PATH = "/etc/ssl/cloudflare/"
 578: 
 579: # Заголовки для запросов API
 580: headers = {
 581:     "Authorization": f"Bearer {CLOUDFLARE_API_TOKEN}",
 582:     "Content-Type": "application/json"
 583: }
 584: 
 585: # Создание нового сертификата
 586: def create_new_certificate():
 587:     url = f"https://api.cloudflare.com/client/v4/zones/{ZONE_ID}/origin_certificates"
 588:     
 589:     payload = {
 590:         "hostnames": HOSTNAMES,
 591:         "requested_validity": CERT_VALIDITY,
 592:         "request_type": "origin-rsa",
 593:         "csr": None
 594:     }
 595:     
 596:     response = requests.post(url, headers=headers, json=payload)
 597:     
 598:     if response.status_code == 200:
 599:         data = response.json()
 600:         if data["success"]:
 601:             cert = data["result"]["certificate"]
 602:             key = data["result"]["private_key"]
 603:             
 604:             # Сохранение сертификата и ключа
 605:             with open(f"{CERT_PATH}quer.us.pem.new", "w") as f:
 606:                 f.write(cert)
 607:             
 608:             with open(f"{CERT_PATH}quer.us.key.new", "w") as f:
 609:                 f.write(key)
 610:             
 611:             # Настройка прав доступа
 612:             os.chmod(f"{CERT_PATH}quer.us.pem.new", 0o644)
 613:             os.chmod(f"{CERT_PATH}quer.us.key.new", 0o600)
 614:             
 615:             # Копирование для s.quer.us
 616:             subprocess.run(f"cp {CERT_PATH}quer.us.pem.new {CERT_PATH}s.quer.us.pem.new", shell=True)
 617:             subprocess.run(f"cp {CERT_PATH}quer.us.key.new {CERT_PATH}s.quer.us.key.new", shell=True)
 618:             
 619:             # Перемещение новых файлов
 620:             subprocess.run(f"mv {CERT_PATH}quer.us.pem.new {CERT_PATH}quer.us.pem", shell=True)
 621:             subprocess.run(f"mv {CERT_PATH}quer.us.key.new {CERT_PATH}quer.us.key", shell=True)
 622:             subprocess.run(f"mv {CERT_PATH}s.quer.us.pem.new {CERT_PATH}s.quer.us.pem", shell=True)
 623:             subprocess.run(f"mv {CERT_PATH}s.quer.us.key.new {CERT_PATH}s.quer.us.key", shell=True)
 624:             
 625:             # Перезапуск Nginx
 626:             subprocess.run("systemctl reload nginx", shell=True)
 627:             
 628:             print(f"Certificate renewed successfully - {datetime.now()}")
 629:             return True
 630:         else:
 631:             print(f"Failed to renew certificate: {data['errors']}")
 632:             return False
 633:     else:
 634:         print(f"Request failed with status code {response.status_code}: {response.text}")
 635:         return False
 636: 
 637: if __name__ == "__main__":
 638:     create_new_certificate()
 639: ```
 640: 
 641: ```bash
 642: # Делаем скрипт исполняемым
 643: sudo chmod +x /usr/local/bin/update-cloudflare-certs.py
 644: 
 645: # Устанавливаем необходимые зависимости
 646: sudo apt-get install -y python3-requests
 647: 
 648: # Создаем cron задачу для запуска обновления раз в год
 649: sudo nano /etc/cron.d/update-certs
 650: ```
 651: 
 652: Содержимое файла `/etc/cron.d/update-certs`:
 653: ```
 654: # Запуск обновления сертификатов в первый день года в 3 часа ночи
 655: 0 3 1 1 * root /usr/local/bin/update-cloudflare-certs.py >> /var/log/update-cloudflare-certs.log 2>&1
 656: ```
 657: 
 658: ## 3. Оптимизация производительности Nginx
 659: 
 660: ### 3.1 Настройка общих параметров Nginx
 661: 
 662: ```bash
 663: # Редактирование основного файла конфигурации Nginx
 664: sudo nano /etc/nginx/nginx.conf
 665: ```
 666: 
 667: Оптимизированная конфигурация `nginx.conf`:
 668: ```nginx
 669: user www-data;
 670: worker_processes auto;
 671: pid /run/nginx.pid;
 672: include /etc/nginx/modules-enabled/*.conf;
 673: 
 674: # Оптимизация количества соединений
 675: events {
 676:     worker_connections 1024;
 677:     multi_accept on;
 678:     use epoll;
 679: }
 680: 
 681: http {
 682:     # Базовые настройки
 683:     sendfile on;
 684:     tcp_nopush on;
 685:     tcp_nodelay on;
 686:     server_tokens off;
 687:     
 688:     # Настройки таймаутов
 689:     keepalive_timeout 65;
 690:     keepalive_requests 100;
 691:     client_body_timeout 10;
 692:     client_header_timeout 10;
 693:     send_timeout 10;
 694:     
 695:     # Настройки буферов
 696:     client_max_body_size 100m;
 697:     client_body_buffer_size 128k;
 698:     client_header_buffer_size 1k;
 699:     large_client_header_buffers 4 4k;
 700:     output_buffers 1 32k;
 701:     postpone_output 1460;
 702:     
 703:     # Типы MIME
 704:     include /etc/nginx/mime.types;
 705:     default_type application/octet-stream;
 706:     
 707:     # Настройки логирования
 708:     access_log /var/log/nginx/access.log;
 709:     error_log /var/log/nginx/error.log;
 710:     
 711:     # Включение сжатия
 712:     include /etc/nginx/conf.d/includes/compression.conf;
 713:     
 714:     # Настройки кэширования для FastCGI, uwsgi и SCGI
 715:     fastcgi_cache_path /var/cache/nginx levels=1:2 keys_zone=microcache:10m max_size=1000m inactive=60m;
 716:     fastcgi_cache_key "$scheme$request_method$host$request_uri";
 717:     
 718:     # Настройки кэширования для проксирования
 719:     proxy_cache_path /var/cache/nginx/proxy levels=1:2 keys_zone=proxy_cache:10m max_size=1000m inactive=60m;
 720:     proxy_temp_path /var/cache/nginx/proxy_temp;
 721:     
 722:     # Настройки файла конфигурации кэширования
 723:     map $sent_http_content_type $expires {
 724:         default                    off;
 725:         text/html                  epoch;
 726:         text/css                   max;
 727:         application/javascript     max;
 728:         ~image/                    max;
 729:         ~font/                     max;
 730:     }
 731:     
 732:     # Включение виртуальных хостов
 733:     include /etc/nginx/conf.d/*.conf;
 734:     include /etc/nginx/sites-enabled/*;
 735: }
 736: ```
 737: 
 738: ### 3.2 Создание структуры для кэширования
 739: 
 740: ```bash
 741: # Создание директорий для кэширования
 742: sudo mkdir -p /var/cache/nginx/proxy
 743: sudo chown -R www-data:www-data /var/cache/nginx
 744: ```
 745: 
 746: ### 3.3 Настройка Brotli для более эффективного сжатия (опционально)
 747: 
 748: ```bash
 749: # Установка модуля Brotli
 750: sudo apt install -y nginx-module-brotli
 751: 
 752: # Создание конфигурации Brotli
 753: sudo nano /etc/nginx/conf.d/includes/brotli.conf
 754: ```
 755: 
 756: Содержимое файла `brotli.conf`:
 757: ```nginx
 758: # Загрузка модуля Brotli (если установлен динамически)
 759: load_module modules/ngx_http_brotli_filter_module.so;
 760: load_module modules/ngx_http_brotli_static_module.so;
 761: 
 762: # Настройки Brotli сжатия
 763: brotli on;
 764: brotli_comp_level 6;
 765: brotli_static on;
 766: brotli_types
 767:     application/atom+xml
 768:     application/javascript
 769:     application/json
 770:     application/ld+json
 771:     application/manifest+json
 772:     application/rss+xml
 773:     application/vnd.geo+json
 774:     application/vnd.ms-fontobject
 775:     application/wasm
 776:     application/x-font-ttf
 777:     application/x-web-app-manifest+json
 778:     application/xhtml+xml
 779:     application/xml
 780:     font/eot
 781:     font/otf
 782:     font/ttf
 783:     image/bmp
 784:     image/svg+xml
 785:     text/cache-manifest
 786:     text/calendar
 787:     text/css
 788:     text/javascript
 789:     text/markdown
 790:     text/plain
 791:     text/xml
 792:     text/vcard
 793:     text/vnd.rim.location.xloc
 794:     text/vtt
 795:     text/x-component
 796:     text/x-cross-domain-policy;
 797: ```
 798: 
 799: ```bash
 800: # Включение Brotli в основном конфигурационном файле
 801: sudo nano /etc/nginx/nginx.conf
 802: ```
 803: 
 804: Добавьте строку для включения Brotli в раздел `http` после строки включения compression.conf:
 805: ```nginx
 806: # Включение сжатия
 807: include /etc/nginx/conf.d/includes/compression.conf;
 808: include /etc/nginx/conf.d/includes/brotli.conf;  # Добавьте эту строку
 809: ```
 810: 
 811: ### 3.4 Настройка оптимального кэширования различных типов контента
 812: 
 813: ```bash
 814: # Создание расширенного файла настроек кэширования
 815: sudo nano /etc/nginx/conf.d/includes/cache-control.conf
 816: ```
 817: 
 818: Содержимое файла `cache-control.conf`:
 819: ```nginx
 820: # Настройки Cache-Control заголовков для разных типов контента
 821: 
 822: # HTML файлы - небольшое время кэширования
 823: location ~* \.(?:html|htm)$ {
 824:     expires 1h;
 825:     add_header Cache-Control "public, max-age=3600";
 826: }
 827: 
 828: # CSS и JavaScript с версионированием (_v1, .min, хеш в URL)
 829: location ~* \.(?:css|js)(?:$|\?|#|_v[0-9]+) {
 830:     expires 1y;
 831:     add_header Cache-Control "public, max-age=31536000, immutable";
 832: }
 833: 
 834: # Изображения и медиа
 835: location ~* \.(?:jpg|jpeg|gif|png|ico|cur|webp|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
 836:     expires 1M;
 837:     add_header Cache-Control "public, max-age=2592000";
 838: }
 839: 
 840: # Веб-шрифты
 841: location ~* \.(?:ttf|ttc|otf|eot|woff|woff2)$ {
 842:     expires 1y;
 843:     add_header Cache-Control "public, max-age=31536000, immutable";
 844: }
 845: 
 846: # Данные и другие файлы
 847: location ~* \.(?:json|xml)$ {
 848:     expires 1d;
 849:     add_header Cache-Control "public, max-age=86400";
 850: }
 851: 
 852: # PDF документы
 853: location ~* \.pdf$ {
 854:     expires 1M;
 855:     add_header Cache-Control "public, max-age=2592000";
 856: }
 857: 
 858: # Специфичные настройки для Next.js
 859: location /_next/static/ {
 860:     expires 1y;
 861:     add_header Cache-Control "public, max-age=31536000, immutable";
 862: }
 863: 
 864: location /_next/image {
 865:     expires 1w;
 866:     add_header Cache-Control "public, max-age=604800";
 867: }
 868: 
 869: location /_next/data/ {
 870:     expires 1h;
 871:     add_header Cache-Control "public, max-age=3600";
 872: }
 873: ```
 874: 
 875: Добавьте включение этого файла в конфигурации виртуальных хостов:
 876: ```bash
 877: # Редактирование конфигурации для staging
 878: sudo nano /etc/nginx/sites-available/s.quer.us.conf
 879: ```
 880: 
 881: Добавьте строку перед блоком `location /`:
 882: ```nginx
 883: # Включаем настройки Cache-Control
 884: include /etc/nginx/conf.d/includes/cache-control.conf;
 885: ```
 886: 
 887: ```bash
 888: # То же самое для production
 889: sudo nano /etc/nginx/sites-available/quer.us.conf
 890: ```
 891: 
 892: ### 3.5 Настройка микрокэширования для динамического контента
 893: 
 894: ```bash
 895: # Создание конфигурации микрокэширования
 896: sudo nano /etc/nginx/conf.d/includes/microcaching.conf
 897: ```
 898: 
 899: Содержимое файла `microcaching.conf`:
 900: ```nginx
 901: # Настройки микрокэширования динамического контента (за исключением админ-панели)
 902: 
 903: # Настройка кэша для proxy
 904: proxy_cache proxy_cache;
 905: proxy_cache_valid 200 302 10m;
 906: proxy_cache_valid 404 1m;
 907: 
 908: # Исключения для кэша (не кэшировать API и запросы, требующие авторизации)
 909: map $request_uri $skip_cache {
 910:     default              0;
 911:     ~/api/               1;
 912:     ~/subscribe          1;
 913:     ~/sign-in            1;
 914:     ~/sign-up            1;
 915:     ~/profile            1;
 916: }
 917: 
 918: # Установка ключа кэша
 919: proxy_cache_key "$scheme$host$request_uri$cookie_user";
 920: 
 921: # Настройка bypass и использования устаревшего кэша при ошибках
 922: proxy_cache_bypass $skip_cache;
 923: proxy_no_cache $skip_cache;
 924: proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
 925: ```
 926: 
 927: ```bash
 928: # Включение микрокэширования в конфигурациях виртуальных хостов
 929: sudo nano /etc/nginx/sites-available/s.quer.us.conf
 930: ```
 931: 
 932: Добавьте строку перед блоком настроек проксирования фронтенда:
 933: ```nginx
 934: # Включение микрокэширования
 935: include /etc/nginx/conf.d/includes/microcaching.conf;
 936: ```
 937: 
 938: ```bash
 939: # То же самое для production
 940: sudo nano /etc/nginx/sites-available/quer.us.conf
 941: ```
 942: 
 943: ### 3.6 Применение конфигурации и тестирование
 944: 
 945: ```bash
 946: # Проверка конфигурации
 947: sudo nginx -t
 948: 
 949: # Применение настроек
 950: sudo systemctl reload nginx
 951: 
 952: # Проверка статуса Nginx
 953: sudo systemctl status nginx
 954: ```
 955: ########################################## До сюда выполнено ##########################################
 956: ## 4. Проверка и тестирование конфигурации
 957: 
 958: ### 4.1 Проверка SSL сертификатов
 959: 
 960: ```bash
 961: # Проверка сертификатов через OpenSSL
 962: sudo openssl x509 -in /etc/ssl/cloudflare/quer.us.pem -text -noout | grep -A2 Validity
 963: sudo openssl x509 -in /etc/ssl/cloudflare/s.quer.us.pem -text -noout | grep -A2 Validity
 964: ```
 965: 
 966: ### 4.2 Проверка настроек и заголовков
 967: 
 968: С помощью внешних инструментов можно проверить конфигурацию: [SSL Labs](https://www.ssllabs.com/ssltest/) и [Security Headers](https://securityheaders.com/)
 969: 
 970: ```bash
 971: # Локальная проверка заголовков
 972: curl -s -I https://quer.us | grep -E '(^HTTP|Cache-Control|Content-Encoding|Strict-Transport-Security|X-Content|X-Frame|X-XSS)'
 973: ```
 974: 
 975: ### 4.3 Настройка мониторинга для SSL сертификатов
 976: 
 977: ```bash
 978: # Запуск ручной проверки SSL сертификатов
 979: sudo /usr/local/bin/check-ssl-expiry
 980: ```
 981: 
 982: ## 5. Доработка дополнительных настроек безопасности
 983: 
 984: ### 5.1 Настройка общих параметров безопасности Nginx (опционально)
 985: 
 986: ```bash
 987: # Создание файла с настройками безопасности
 988: sudo nano /etc/nginx/conf.d/includes/security.conf
 989: ```
 990: 
 991: Содержимое файла `security.conf`:
 992: ```nginx
 993: # Блокировка доступа к файлам .htaccess и конфигурациям
 994: location ~ /\.(?!well-known\/) {
 995:     deny all;
 996: }
 997: 
 998: # Блокировка доступа к .git и другим служебным директориям
 999: location ~ /(\.git|\.svn|\.hg|\.DS_Store) {
1000:     deny all;
1001: }
1002: 
1003: # Блокировка выполнения PHP в директориях загрузок
1004: location ~* ^/(uploads|files)/.*\.php$ {
1005:     deny all;
1006: }
1007: 
1008: # Дополнительные заголовки безопасности
1009: add_header X-XSS-Protection "1; mode=block" always;
1010: add_header X-Content-Type-Options "nosniff" always;
1011: add_header Referrer-Policy "strict-origin-when-cross-origin" always;
1012: add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;
1013: ```
1014: 
1015: ```bash
1016: # Включение файла безопасности в конфигурацию
1017: sudo nano /etc/nginx/sites-available/s.quer.us.conf
1018: sudo nano /etc/nginx/sites-available/quer.us.conf
1019: ```
1020: 
1021: Добавьте строку в раздел server:
1022: ```nginx
1023: # Настройки безопасности
1024: include /etc/nginx/conf.d/includes/security.conf;
1025: ```
1026: 
1027: ### 5.2 Настройка защиты от атак с перебором паролей (Rate Limiting)
1028: 
1029: ```bash
1030: # Создание конфигурации для ограничения частоты запросов
1031: sudo nano /etc/nginx/conf.d/includes/rate-limits.conf
1032: ```
1033: 
1034: Содержимое файла `rate-limits.conf`:
1035: ```nginx
1036: # Зоны для хранения состояний ограничений
1037: limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
1038: limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;
1039: limit_req_zone $binary_remote_addr zone=global:10m rate=10r/s;
1040: 
1041: # Общее ограничение для IP-адресов
1042: limit_conn_zone $binary_remote_addr zone=addr:10m;
1043: limit_conn addr 100;
1044: 
1045: # Настройка лимитов для конкретных URL
1046: # API ограничения
1047: location ~* ^/api/(?!webhook) {
1048:     limit_req zone=api burst=10 nodelay;
1049: }
1050: 
1051: # Аутентификация ограничения
1052: location ~* ^/(sign-in|sign-up|subscribe) {
1053:     limit_req zone=auth burst=5 nodelay;
1054: }
1055: 
1056: # Не применять ограничения для API webhook
1057: location ~* ^/api/webhook {
1058:     limit_req_status 429;
1059:     limit_req zone=global burst=20 nodelay;
1060: }
1061: ```
1062: 
1063: Добавьте включение этого файла в конфигурации виртуальных хостов:
1064: ```bash
1065: # Редактирование конфигурации для staging и production
1066: sudo nano /etc/nginx/sites-available/s.quer.us.conf
1067: sudo nano /etc/nginx/sites-available/quer.us.conf
1068: ```
1069: 
1070: Добавьте строку перед настройками локаций:
1071: ```nginx
1072: # Включение ограничения частоты запросов
1073: include /etc/nginx/conf.d/includes/rate-limits.conf;
1074: ```
1075: 
1076: ## 6. Заключительная проверка и применение конфигурации
1077: 
1078: ```bash
1079: # Проверка конфигурации
1080: sudo nginx -t
1081: 
1082: # Применение настроек
1083: sudo systemctl reload nginx
1084: 
1085: # Проверка статуса Nginx
1086: sudo systemctl status nginx
1087: ```
1088: 
1089: ## Заключение
1090: 
1091: Теперь у вас настроена оптимизированная инфраструктура Nginx с поддержкой SSL через Cloudflare для проекта quer-calc. 
1092: 
1093: Основные компоненты настройки:
1094: 1. Оптимизированные виртуальные хосты для staging и production окружений
1095: 2. Настроенные SSL-сертификаты от Cloudflare для безопасного соединения
1096: 3. Оптимизация производительности с помощью:
1097:    - Сжатия контента (Gzip и Brotli)
1098:    - HTTP/2 для более быстрой загрузки
1099:    - Интеллектуального кэширования разных типов ресурсов
1100:    - Микрокэширования для динамического контента
1101: 4. Дополнительные настройки безопасности и ограничения
1102: 
1103: Регулярно проверяйте срок действия сертификатов с помощью настроенного скрипта и обновляйте их по мере необходимости. Также рекомендуется периодически проверять настройки и производительность с помощью инструментов как SSL Labs, Security Headers и PageSpeed Insights.
1104: 
1105: ## 7. Временная защита
1106: 
1107: Добавление HTTP Basic Authentication (запрос логина и пароля в браузере) позволит вам протестировать SSL-сертификаты и заголовки безопасности, но при этом ограничит доступ к сайту. Это хорошее временное решение, пока вы настраиваете DNS в Cloudflare.
1108: 
1109: Вот как настроить базовую аутентификацию в Nginx:
1110: 
1111: ### 1. Создание файла с паролем
1112: 
1113: ```bash
1114: # Установка пакета apache2-utils для команды htpasswd
1115: sudo apt install apache2-utils
1116: 
1117: # Создание директории для хранения файлов с паролями
1118: sudo mkdir -p /etc/nginx/auth
1119: 
1120: # Создание пользователя (замените "admin" на желаемое имя пользователя)
1121: sudo htpasswd -c /etc/nginx/auth/.htpasswd xxx
1122: # Система попросит ввести и подтвердить пароль (eBqN28tyP)
1123: ```
1124: 
1125: ### 2. Настройка аутентификации в конфигурации Nginx
1126: 
1127: Отредактируйте оба файла конфигурации:
1128: 
1129: ```bash
1130: sudo nano /etc/nginx/sites-available/quer.us.conf
1131: sudo nano /etc/nginx/sites-available/s.quer.us.conf
1132: ```
1133: 
1134: В каждом файле добавьте настройки аутентификации в блок `location /`:
1135: 
1136: ```nginx
1137: location / {
1138:     # Добавьте эти строки для аутентификации
1139:     auth_basic "Restricted Area";
1140:     auth_basic_user_file /etc/nginx/auth/.htpasswd;
1141:     
1142:     # Существующие настройки прокси
1143:     proxy_pass http://localhost:3000;  # или 3100 для production
1144:     proxy_http_version 1.1;
1145:     # ... остальные настройки прокси ...
1146: }
1147: ```
1148: 
1149: ### 3. Применение изменений
1150: 
1151: ```bash
1152: # Проверка конфигурации
1153: sudo nginx -t
1154: 
1155: # Если всё в порядке, перезагрузите Nginx
1156: sudo systemctl reload nginx
1157: ```
1158: 
1159: Теперь при попытке доступа к вашему сайту браузер будет запрашивать имя пользователя и пароль. Это не помешает тестированию SSL-сертификатов и заголовков безопасности, так как аутентификация происходит уже после установления защищенного соединения.
1160: 
1161: Когда вы будете готовы открыть сайт для публичного доступа, просто удалите строки `auth_basic` и `auth_basic_user_file` из конфигурации.
````

## File: План развёртывания приложения и CI:CD/4. cloudflare-setup.md
````markdown
  1: # Подробный план настройки Cloudflare для проекта quer-calc
  2: 
  3: В этом документе представлен детальный план настройки Cloudflare для проекта quer-calc, включая настройку DNS, SSL и функций безопасности.
  4: 
  5: ## 1. Регистрация и начальная настройка аккаунта Cloudflare
  6: 
  7: ### 1.1 Создание аккаунта Cloudflare (если еще нет)
  8: 
  9: 1. Перейдите на [cloudflare.com](https://www.cloudflare.com/)
 10: 2. Нажмите "Sign Up" в правом верхнем углу
 11: 3. Заполните форму регистрации с вашим email и паролем
 12: 4. Подтвердите регистрацию через письмо, отправленное на ваш email
 13: 
 14: ### 1.2 Добавление домена в Cloudflare
 15: 
 16: 1. После входа в панель управления нажмите кнопку "+ Add a Site"
 17: 2. Введите корневой домен `quer.us` и нажмите "Add Site"
 18: 3. Выберите подходящий план (Free план подойдет для начала, с возможностью апгрейда)
 19: 4. Cloudflare просканирует существующие DNS-записи вашего домена
 20: 
 21: ### 1.3 Изменение серверов имен (Nameservers)
 22: 
 23: После сканирования Cloudflare предоставит вам новые серверы имен для делегирования домена:
 24: 
 25: 1. Запишите предоставленные Cloudflare NS-серверы (обычно это что-то вроде `aida.ns.cloudflare.com` и `kip.ns.cloudflare.com`)
 26: 2. Перейдите к вашему регистратору доменов (где вы регистрировали домен quer.us)
 27: 3. Найдите раздел управления DNS или серверами имен
 28: 4. Замените текущие серверы имен на серверы имен Cloudflare
 29: 5. Сохраните изменения
 30: 
 31: > **Примечание**: Изменения в серверах имен могут занять от нескольких минут до 48 часов для полного распространения.
 32: 
 33: ## 2. Базовая настройка DNS
 34: 
 35: ### 2.1 Настройка корневого домена (quer.us)
 36: 
 37: 1. В панели управления Cloudflare выберите добавленный домен
 38: 2. Перейдите на вкладку "DNS"
 39: 3. Создайте A-запись для корневого домена:
 40:    ```
 41:    Type: A
 42:    Name: @
 43:    IPv4 address: ************* (IP вашего сервера Hetzner)
 44:    Proxy status: Proxied (оранжевое облако)
 45:    TTL: Auto
 46:    ```
 47: 4. Нажмите "Save"
 48: 
 49: ### 2.2 Настройка поддомена для staging (s.quer.us)
 50: 
 51: 1. Оставаясь на вкладке "DNS", нажмите "Add record"
 52: 2. Создайте A-запись для поддомена staging:
 53:    ```
 54:    Type: A
 55:    Name: s
 56:    IPv4 address: ************* (IP вашего сервера Hetzner)
 57:    Proxy status: Proxied (оранжевое облако)
 58:    TTL: Auto
 59:    ```
 60: 3. Нажмите "Save"
 61: 
 62: ### 2.3 Настройка www-поддомена
 63: 
 64: 1. Оставаясь на вкладке "DNS", нажмите "Add record"
 65: 2. Создайте CNAME-запись для www:
 66:    ```
 67:    Type: CNAME
 68:    Name: www
 69:    Target: quer.us
 70:    Proxy status: Proxied (оранжевое облако)
 71:    TTL: Auto
 72:    ```
 73: 3. Нажмите "Save"
 74: 
 75: ### 2.4 Настройка записи для проверки владения доменом (если требуется)
 76: 
 77: Если вы планируете использовать службы, требующие подтверждения владения доменом (например, Google Search Console, Microsoft 365 и т.д.):
 78: 
 79: 1. Оставаясь на вкладке "DNS", нажмите "Add record"
 80: 2. Создайте TXT-запись согласно требованиям сервиса:
 81:    ```
 82:    Type: TXT
 83:    Name: @
 84:    Content: [код подтверждения, предоставленный сервисом]
 85:    Proxy status: DNS only (серое облако)
 86:    TTL: Auto
 87:    ```
 88: 3. Нажмите "Save"
 89: 
 90: ### 2.5 Настройка SPF и DKIM для email (если планируете отправлять email)
 91: 
 92: 1. Оставаясь на вкладке "DNS", нажмите "Add record"
 93: 2. Создайте TXT-запись для SPF:
 94:    ```
 95:    Type: TXT
 96:    Name: @
 97:    Content: v=spf1 include:_spf.google.com include:sendgrid.net -all
 98:    Proxy status: DNS only (серое облако)
 99:    TTL: Auto
100:    ```
101: 3. Нажмите "Save"
102: 
103: 4. Если вы используете SendGrid, Mailgun или другой сервис для отправки email, добавьте необходимые CNAME или TXT записи согласно документации сервиса.
104: 
105: ### 2.6 Проверка DNS-записей
106: 
107: 1. После создания всех необходимых записей, перейдите на вкладку "DNS"
108: 2. Убедитесь, что все записи созданы корректно и имеют правильное значение "Proxy status"
109: 3. Используйте инструмент `dig` или онлайн-сервисы для проверки DNS-записей:
110:    ```bash
111:    dig quer.us
112:    dig s.quer.us
113:    dig www.quer.us
114:    ```
115: 
116: ## 3. Настройка SSL и шифрования
117: 
118: ### 3.1 Активация SSL в режиме Full (Strict)
119: 
120: 1. В панели управления Cloudflare выберите ваш домен
121: 2. Перейдите на вкладку "SSL/TLS"
122: 3. На странице выберите "Configure encryption mode" (или нажмите "Configure" если вы видите такую кнопку)
123: 4. В открывшемся меню у вас есть два варианта:
124:    - **Automatic SSL/TLS (default)**: Cloudflare автоматически определяет и настраивает оптимальный режим SSL
125:    - **Custom SSL/TLS**: Позволяет вручную выбрать режим шифрования
126: 
127: 5. Если вы хотите точно использовать Full (Strict) режим:
128:    - Выберите "Custom SSL/TLS"
129:    - Нажмите кнопку "Select" под Custom SSL/TLS
130:    - В появившихся опциях выберите "Full (strict)"
131:    - Нажмите "Save"
132: 
133: 6. Для работы режима Full (Strict) ваш Origin-сервер должен соответствовать следующим требованиям:
134:    - Принимать HTTPS-соединения на порту 443
135:    - Использовать действующий SSL-сертификат, который:
136:      - Не просрочен
137:      - Выдан публично доверенным центром сертификации или Cloudflare Origin CA
138:      - Содержит Common Name (CN) или Subject Alternative Name (SAN), соответствующий запрашиваемому хосту
139: 
140: 7. Если у вас уже есть Cloudflare Origin сертификат (как видно на вашем третьем скриншоте), вы готовы к использованию Full (Strict) режима.
141: 
142: > **Примечание**: Full (Strict) - самый безопасный режим, так как он шифрует трафик на всем пути и проверяет подлинность сертификата вашего сервера. Он настоятельно рекомендуется для любых сайтов с чувствительной информацией.
143: 
144: ### 3.2 Настройка Edge-сертификатов
145: 
146: 1. Перейдите на подвкладку "Edge Certificates"
147: 2. Включите "Always Use HTTPS" (установите переключатель в положение "On")
148: 3. Включите "Automatic HTTPS Rewrites" (установите переключатель в положение "On")
149: 4. В разделе "Certificate Validity" выберите "15 years" для максимального срока действия
150: 
151: ### 3.3 Настройка HSTS (HTTP Strict Transport Security)
152: 
153: 1. Оставаясь на подвкладке "Edge Certificates", найдите раздел "HTTP Strict Transport Security (HSTS)"
154: 2. Нажмите "Enable HSTS"
155: 3. Настройте параметры HSTS:
156:    ```
157:    Max Age: 6 months (для начала, можно увеличить позже)
158:    Apply HSTS policy to subdomains: Checked
159:    Preload: Checked
160:    No-Sniff Header: Checked
161:    ```
162: 4. Нажмите "Save"
163: 
164: > **Предупреждение**: Включение HSTS — это долгосрочное обязательство использовать HTTPS. После включения HSTS браузеры будут отказываться подключаться к вашему сайту по незащищенному HTTP. Убедитесь, что HTTPS работает корректно на всех поддоменах перед включением HSTS.
165: 
166: ### 3.4 Создание Origin-сертификатов (если еще не настроено)
167: 
168: Если вы еще не создали Origin-сертификаты (как описано в предыдущей части про настройку Nginx):
169: 
170: 1. Перейдите на подвкладку "Origin Server"
171: 2. Нажмите "Create Certificate"
172: 3. Выберите настройки:
173:    ```
174:    Hostnames: quer.us, *.quer.us, s.quer.us
175:    Private key type: RSA (2048)
176:    Certificate validity: 15 years
177:    ```
178: 4. Нажмите "Create"
179: 5. Сохраните созданный сертификат и приватный ключ в безопасном месте
180: 6. Следуйте инструкциям из предыдущего раздела по настройке Nginx для установки этих сертификатов на ваш сервер
181: 
182: ### 3.5 Настройка TLS версий
183: 
184: 1. Перейдите на вкладку "SSL/TLS" и выберите подраздел "Edge Certificates"
185: 
186: 2. Прокрутите страницу вниз до раздела "Minimum TLS Version"
187: 
188: 3. Выберите соответствующую минимальную версию TLS:
189:    - **TLS 1.0 (default)**: Максимальная совместимость со старыми устройствами и браузерами
190:    - **TLS 1.1**: Немного более безопасный вариант, чем 1.0
191:    - **TLS 1.2**: Рекомендуется для соответствия требованиям PCI DSS и для большинства современных сайтов
192:    - **TLS 1.3**: Максимальная безопасность и производительность, но может вызвать проблемы с некоторыми поисковыми роботами и старыми браузерами
193: 
194: 4. Нажмите на нужную версию, чтобы применить изменения
195: 
196: > **Рекомендация**: Для большинства сайтов оптимальным выбором будет TLS 1.2, так как он обеспечивает хороший баланс между безопасностью и совместимостью. TLS 1.3 следует использовать только для сайтов с особыми требованиями к безопасности и при уверенности, что ваша аудитория использует современные браузеры.
197: 
198: ### 3.6 Дополнительные настройки безопасности SSL/TLS
199: 
200: После настройки минимальной версии TLS, рекомендуется также включить следующие параметры, которые доступны на той же странице:
201: 
202: 1. **Always Use HTTPS**: Включите эту опцию, чтобы автоматически перенаправлять весь HTTP-трафик на HTTPS
203: 
204: 2. **HTTP Strict Transport Security (HSTS)**: Включите эту функцию для усиления безопасности:
205:    - Max-Age: 6 months (рекомендуется)
206:    - Include subdomains: On
207:    - Preload: On
208: 
209: 3. **TLS 1.3**: Убедитесь, что эта опция включена для поддержки новейшей версии протокола TLS (это не мешает поддержке более старых версий, если вы не установили более высокую минимальную версию)
210: 
211: 4. **Automatic HTTPS Rewrites**: Включите для автоматического исправления смешанного контента на вашем сайте
212: 
213: > **Примечание**: Если у вас включен Advanced Certificate Manager, вы также получаете доступ к настройке Cipher Suites (наборов шифров), что позволяет более точно контролировать, какие криптографические алгоритмы могут использоваться для шифрования соединений с вашим сайтом.
214: ######################################################## До сюда выполнено #################################################
215: ## 4. Настройка безопасности
216: 
217: ### 4.1 Настройка Web Application Firewall (WAF)
218: 
219: 1. Перейдите на вкладку "Security" в панели управления вашего домена
220: 2. Выберите подраздел "WAF"
221: 3. Настройка базовой защиты:
222:    - Переключите "Web Application Firewall" в положение "On"
223:    - В разделе "WAF Managed Rules" выберите режим "High" для максимальной защиты
224:    - Включите группы правил:
225:      - Cloudflare Managed Rules
226:      - OWASP Core Rule Set
227:      - Cloudflare Specials
228: 
229: 4. Создание пользовательского правила для защиты админ-панели (опционально):
230:    - Нажмите "Create Rule"
231:    - Настройте следующее правило:
232:      ```
233:      Rule name: Protect Admin Pages
234:      If incoming requests match: URI Path starts with /profile
235:      Then: Challenge
236:      ```
237:    - Это правило будет отображать CAPTCHA-проверку для попыток доступа к панели профиля
238: 
239: ### 4.2 Настройка защиты от DDoS-атак
240: 
241: 1. Перейдите на вкладку "DDoS" в панели управления
242: 2. Убедитесь, что опция "DDoS Protection" включена (она должна быть включена по умолчанию)
243: 3. Установите уровень чувствительности:
244:    - Для начала выберите "Medium"
245:    - При необходимости можно изменить на "High" в случае атак
246: 
247: ### 4.3 Включение дополнительных функций безопасности
248: 
249: 1. Перейдите на вкладку "Security" и выберите подраздел "Settings"
250: 
251: 2. Настройка Challenge Passage:
252:    - Установите "Challenge Passage" на 30 минут (время, в течение которого пользователь не будет повторно проходить проверку)
253: 
254: 3. Настройка Browser Integrity Check:
255:    - Включите "Browser Integrity Check" (это помогает блокировать вредоносные запросы)
256: 
257: 4. Настройка Bot Fight Mode:
258:    - Перейдите на вкладку "Bot Management"
259:    - Включите "Bot Fight Mode" (это поможет защититься от вредоносных ботов)
260: 
261: 5. Настройка JavaScript Challenge:
262:    - Вернитесь на вкладку "Security" > "Settings"
263:    - В разделе "Security Level" выберите "Medium" (это активирует JavaScript Challenge для подозрительного трафика)
264: 
265: ### 4.4 Настройка защиты от брутфорс-атак
266: 
267: 1. Перейдите на вкладку "Security" > "WAF" > "Custom Rules"
268: 2. Нажмите "Create Rule"
269: 3. Создайте правило для защиты страниц входа:
270:    ```
271:    Rule name: Protect Login Pages
272:    If incoming requests match: (URI Path contains "/sign-in" OR URI Path contains "/sign-up") AND HTTP method equals "POST"
273:    Then: Rate limit (выберите 5 запросов в минуту)
274:    Rate limit duration: 1 minute
275:    ```
276: 4. Нажмите "Deploy"
277: 
278: ### 4.5 Настройка IP Firewall (опционально)
279: 
280: Если у вас есть IP-адреса, которые вы хотите заблокировать или разрешить:
281: 
282: 1. Перейдите на вкладку "Security" > "WAF" > "Tools"
283: 2. В разделе "IP Access Rules" нажмите "Create Rule"
284: 3. Добавьте IP-адреса и выберите соответствующее действие (Allow, Challenge, JS Challenge, Block)
285: 
286: ## 5. Оптимизация производительности с Cloudflare
287: 
288: ### 5.1 Настройка кэширования
289: 
290: 1. Перейдите на вкладку "Caching"
291: 2. На подвкладке "Configuration" настройте следующие параметры:
292:    - Включите "Caching Level": Standard
293:    - Browser Cache TTL: Рекомендуется 4 часа для начала
294:    - "Origin Cache Control": Включено (чтобы респектировать заголовки Cache-Control с вашего сервера)
295: 
296: ### 5.2 Настройка правил кэширования для статического контента
297: 
298: 1. Перейдите на вкладку "Rules" > "Cache Rules"
299: 2. Нажмите "Create rule"
300: 3. Настройте правило для статических активов:
301:    ```
302:    Rule name: Static Assets Caching
303:    If: (URL file extension in ["js", "css", "jpg", "jpeg", "png", "gif", "ico", "svg", "woff", "woff2", "ttf"])
304:    Then: Cache level: Standard, Edge TTL: 1 week
305:    ```
306: 
307: 4. Создайте другое правило для Next.js статических ресурсов:
308:    ```
309:    Rule name: Next.js Static
310:    If: URL path starts with "/_next/static/"
311:    Then: Cache level: Standard, Edge TTL: 1 year, Browser TTL: 1 year
312:    ```
313: ######################################################## До сюда выполнено #################################################
314: ### 5.3 Настройка Minification и оптимизации
315: 
316: 1. Перейдите на вкладку "Speed" > "Optimization"
317: 2. В разделе "Optimizations" настройте:
318:    - Auto Minify: Включите для JavaScript, CSS и HTML
319:    - Brotli: Включите
320:    - Early Hints: Включите
321:    - Rocket Loader: Выключено (может конфликтовать с Next.js)
322:    - Automatic Platform Optimization: Включите, если доступно
323: 
324: ### 5.4 Настройка Argo (опционально, требует дополнительной оплаты)
325: 
326: Если вы хотите улучшить маршрутизацию и снизить время ответа:
327: 
328: 1. Перейдите на вкладку "Speed" > "Argo Smart Routing"
329: 2. Нажмите "Enable Argo Smart Routing"
330: 3. Это дополнительная платная услуга, но она может значительно улучшить производительность
331: 
332: ## 6. Мониторинг и анализ
333: 
334: ### 6.1 Настройка Analytics
335: 
336: 1. Перейдите на вкладку "Analytics" > "Traffic"
337: 2. Изучите доступные графики и данные о посещаемости
338: 3. Настройте периоды анализа (за последние 24 часа, 7 дней и т.д.)
339: 
340: ### 6.2 Настройка оповещений
341: 
342: 1. Перейдите на вкладку "Notifications"
343: 2. Нажмите "Add notification"
344: 3. Настройте оповещения для важных событий:
345:    - DDoS-атаки
346:    - Падение origin-сервера
347:    - SSL-сертификат близок к истечению срока действия
348:    - Необычная активность
349: 
350: ### 6.3 Настройка логирования (требуется Enterprise план или Workers)
351: 
352: Если у вас есть доступ к Enterprise плану:
353: 
354: 1. Перейдите на вкладку "Logs" > "Logpush"
355: 2. Настройте отправку логов в выбранный сервис (Google Cloud Storage, AWS S3, и т.д.)
356: 
357: Альтернативно, вы можете использовать Cloudflare Workers для базового логирования:
358: 
359: 1. Перейдите на вкладку "Workers"
360: 2. Создайте новый Worker для логирования важных событий
361: 
362: ## 7. Заключительные проверки и тесты
363: 
364: ### 7.1 Проверка правильности DNS-настроек
365: 
366: Убедитесь, что ваши домены правильно разрешаются и проксируются через Cloudflare:
367: 
368: ```bash
369: # Проверка A-записей
370: dig quer.us
371: dig s.quer.us
372: 
373: # Проверка CNAME
374: dig www.quer.us
375: ```
376: 
377: ### 7.2 Проверка SSL
378: 
379: Убедитесь, что SSL работает корректно:
380: 
381: 1. Посетите https://quer.us и https://s.quer.us
382: 2. Проверьте, что соединение защищено (зеленый замок в браузере)
383: 3. Используйте инструменты проверки SSL:
384:    - [SSL Labs](https://www.ssllabs.com/ssltest/)
385:    - [Why No Padlock](https://www.whynopadlock.com/)
386: 
387: ### 7.3 Проверка WAF и безопасности
388: 
389: 1. Попробуйте выполнить простой тест на безопасность, например, добавив `?'<script>` к URL
390: 2. Проверьте, что WAF блокирует такие попытки
391: 3. Убедитесь, что IP Firewall работает правильно (если настроен)
392: 
393: ### 7.4 Тестирование производительности
394: 
395: 1. Используйте инструменты для проверки производительности:
396:    - [Google PageSpeed Insights](https://pagespeed.web.dev/)
397:    - [WebPageTest](https://www.webpagetest.org/)
398: 2. Сравните результаты до и после настройки Cloudflare
399: 
400: ## 8. Дополнительные настройки (опционально)
401: 
402: ### 8.1 Настройка Page Rules
403: 
404: Page Rules позволяют создавать специфические правила для конкретных URL:
405: 
406: 1. Перейдите на вкладку "Rules" > "Page Rules"
407: 2. Нажмите "Create Page Rule"
408: 3. Пример Page Rule для обхода кэша на API:
409:    ```
410:    URL pattern: quer.us/api/*
411:    Setting: Cache Level: Bypass
412:    ```
413: 4. Пример Page Rule для перенаправления HTTP на HTTPS:
414:    ```
415:    URL pattern: http://*quer.us/*
416:    Setting: Always Use HTTPS
417:    ```
418: 
419: ### 8.2 Настройка Workers (опционально)
420: 
421: Cloudflare Workers позволяют запускать JavaScript-код на edge-серверах Cloudflare для дополнительной функциональности:
422: 
423: 1. Перейдите на вкладку "Workers"
424: 2. Нажмите "Create a Service"
425: 3. Назовите сервис (например, "quer-edge-handler")
426: 4. Напишите Worker-скрипт для дополнительной функциональности (например, географическая маршрутизация, A/B-тестирование, кастомная безопасность)
427: 
428: ### 8.3 Настройка загрузки изображений (опционально)
429: 
430: Если ваш сайт использует много изображений:
431: 
432: 1. Перейдите на вкладку "Speed" > "Optimization"
433: 2. В разделе "Images" включите:
434:    - Mirage: для автоматической оптимизации загрузки изображений
435:    - Polish: для автоматической оптимизации качества изображений
436:    - Lazy Loading: для отложенной загрузки изображений
437: 
438: ## 9. Техническое обслуживание и мониторинг
439: 
440: ### 9.1 Регулярная проверка доступности
441: 
442: Настройте проверку доступности вашего сайта:
443: 
444: 1. Используйте внешние сервисы мониторинга, такие как [UptimeRobot](https://uptimerobot.com/) или [Pingdom](https://www.pingdom.com/)
445: 2. Настройте оповещения о недоступности сайта
446: 
447: ### 9.2 Регулярный анализ аналитики Cloudflare
448: 
449: 1. Еженедельно проверяйте аналитику Cloudflare для отслеживания трендов и потенциальных проблем
450: 2. Обращайте внимание на странные пики трафика или увеличение количества блокировок, что может указывать на атаки
451: 
452: ### 9.3 Планирование обновлений и улучшений
453: 
454: 1. Следите за новыми функциями Cloudflare
455: 2. Периодически пересматривайте настройки безопасности и производительности
456: 
457: ## Заключение
458: 
459: После завершения всех настроек у вас будет полностью защищенный и оптимизированный сайт с:
460: - Правильно настроенным DNS
461: - Безопасным SSL-соединением
462: - Защитой от различных типов атак, включая DDoS и брутфорс
463: - Оптимизированной производительностью за счет кэширования и минификации
464: 
465: Cloudflare также будет скрывать ваш реальный IP-адрес сервера, что является дополнительным уровнем защиты. Регулярно проверяйте аналитику и логи, чтобы своевременно обнаруживать и решать возникающие проблемы.
466: 
467: ###### Обновлённый с пункта 4.
468: 
469: # 4. Настройка безопасности в Cloudflare (2024) - для бесплатного плана
470: 
471: ## 4.1 Настройка защиты от веб-атак
472: 
473: ### 4.1.1 Настройка Cloudflare Managed Free Ruleset
474: 
475: 1. Перейдите на вкладку "Security" > "Web application exploits" в панели управления вашего домена
476: 2. Найдите раздел "Cloudflare Managed Free Ruleset"
477: 3. Включите эти правила, выбрав "On" для защиты от базовых веб-атак
478: 4. Обратите внимание, что в бесплатном плане доступно только 1 правило детектирования
479: 
480: ### 4.1.2 Создание исключений (при необходимости)
481: 
482: Если вы обнаружите, что некоторые законные запросы блокируются:
483: 
484: 1. Нажмите "Create exception rule"
485: 2. Укажите условия, при которых правила защиты не должны применяться
486: 3. Это поможет избежать ложных срабатываний для легитимного трафика
487: 
488: ## 4.2 Настройка защиты от ботов
489: 
490: ### 4.2.1 Включение Bot Fight Mode
491: 
492: 1. Перейдите на вкладку "Security" > "Bots"
493: 2. Для "Bot Fight Mode" выберите "On"
494: 3. Нажмите "Configure Bot Fight Mode"
495: 
496: ### 4.2.2 Настройка блокировки AI-ботов
497: 
498: 1. В том же разделе "Bot Fight Mode" включите опцию "Block AI bots"
499: 2. Это автоматически блокирует известные боты ИИ, такие как GPTBot, Bytespider и другие, которые могут скрапить контент вашего сайта для обучения моделей ИИ
500: 
501: ### 4.2.3 Активация AI Labyrinth (для дополнительной защиты от ИИ-ботов)
502: 
503: 1. В разделе конфигурации Bot Fight Mode включите опцию "AI Labyrinth" 
504: 2. Эта функция добавляет на страницы невидимые ссылки с тегами Nofollow, которые блокируют ИИ-краулеры, не соблюдающие рекомендуемые правила
505: 
506: ## 4.3 Настройка защиты от DDoS-атак
507: 
508: 1. Перейдите на вкладку "Security" > "DDoS"
509: 2. Убедитесь, что опция "DDoS Protection" включена (должна быть включена по умолчанию)
510: 3. Настройте уровень чувствительности:
511:    - Для начала выберите "Medium"
512:    - При необходимости можно изменить на "High" в случае атак
513: 
514: ## 4.4 Настройка защиты от ботов с помощью Bot Fight Mode
515: 
516: В бесплатном плане Cloudflare доступен базовый инструмент для защиты от ботов - Bot Fight Mode.
517: 
518: ### 4.4.1 Включение Bot Fight Mode
519: 
520: 1. Перейдите на вкладку "Security" > "Bots"
521: 2. Для "Bot Fight Mode" выберите "On"
522: 3. Нажмите "Configure Bot Fight Mode"
523: 
524: ### 4.4.2 Настройка блокировки AI-ботов
525: 
526: 1. В том же разделе "Bot Fight Mode" включите опцию "Block AI bots"
527: 2. Это автоматически блокирует известные боты ИИ, такие как GPTBot, Bytespider и другие, которые могут скрапить контент вашего сайта для обучения моделей ИИ
528: 
529: ### 4.4.3 Активация AI Labyrinth (для дополнительной защиты от ИИ-ботов)
530: 
531: 1. В разделе конфигурации Bot Fight Mode включите опцию "AI Labyrinth" 
532: 2. Эта функция добавляет на страницы невидимые ссылки с тегами Nofollow, которые блокируют ИИ-краулеры, не соблюдающие рекомендуемые правила
533: 
534: > **Примечание:** Page Shield (защита от атак на клиентской стороне) доступен только в платных тарифных планах Enterprise, поэтому эта функция не включена в данную инструкцию для бесплатного плана.
535: 
536: ## 4.5 Настройка базовых функций безопасности (доступных в бесплатном плане)
537: 
538: ### 4.5.1 Настройка основных параметров безопасности
539: 
540: 1. Перейдите на вкладку "Security" > "Settings"
541: 2. Настройте следующие параметры:
542:    - Security Level: Рекомендуется "Medium" для баланса между безопасностью и пользовательским опытом
543:    - Challenge Passage: 30 минут (время, на которое пользователь запоминается после прохождения проверки)
544:    - Browser Integrity Check: Включен
545: 
546: ### 4.5.2 Настройка базовой защиты от ботов
547: 
548: 1. Перейдите на вкладку "Security" > "Settings"
549: 2. Включите "Bot Fight Mode" для базовой защиты от вредоносных ботов
550: 
551: ### 4.5.3 Настройка базовых правил доступа по IP
552: 
553: 1. Перейдите на вкладку "Security" > "Tools"
554: 2. В разделе "IP Access Rules" создайте правила для блокировки доступа с проблемных IP-адресов:
555:    - Нажмите "Add rule"
556:    - Введите IP-адрес или диапазон (например, ***********/24)
557:    - Выберите действие: Block, Challenge, JavaScript Challenge, или Allow
558:    - Нажмите "Save"
559: 
560: ## 5. Оптимизация производительности с Cloudflare
561: 
562: ### 5.1 Настройка кэширования
563: 
564: 1. Перейдите на вкладку "Caching" > "Configuration"
565: 2. Настройте следующие параметры:
566:    - Включите "Caching Level": Standard
567:    - Browser Cache TTL: 4 часа
568:    - "Origin Cache Control": Включено
569: 
570: ### 5.2 Настройка правил кэширования для статического контента
571: 
572: 1. Перейдите на вкладку "Rules" > "Cache Rules"
573: 2. Нажмите "Create rule"
574: 3. Настройте правило для статических активов:
575:    ```
576:    Имя правила: Static Assets Caching
577:    Если: (расширение файла URL в ["js", "css", "jpg", "jpeg", "png", "gif", "ico", "svg", "woff", "woff2", "ttf"])
578:    Тогда: Cache level: Standard, Edge TTL: 1 неделя
579:    ```
580: 
581: 4. Создайте правило для статических ресурсов Next.js:
582:    ```
583:    Имя правила: Next.js Static
584:    Если: URL path начинается с "/_next/static/"
585:    Тогда: Cache level: Standard, Edge TTL: 1 год, Browser TTL: 1 год
586:    ```
587: 
588: ### 5.3 Настройка оптимизации
589: 
590: 1. Перейдите на вкладку "Speed" > "Optimization"
591: 2. В разделе "Optimizations" настройте:
592:    - Auto Minify: Включите для JavaScript, CSS и HTML
593:    - Brotli: Включите
594:    - Early Hints: Включите
595:    - Rocket Loader: Выключено (может конфликтовать с Next.js)
596:    - Automatic Platform Optimization: Включите, если доступно
597: 
598: ## 6. Мониторинг и аналитика в бесплатном плане
599: 
600: ### 6.1 Просмотр событий безопасности
601: 
602: 1. Перейдите на вкладку "Security" > "Events"
603: 2. Здесь вы можете увидеть основные события безопасности, включая:
604:    - Случаи блокировки ботов через Bot Fight Mode
605:    - Срабатывания Managed Free Ruleset
606:    - Заблокированные IP через IP Access Rules
607: 
608: ### 6.2 Использование Security Overview
609: 
610: 1. Перейдите на вкладку "Security" > "Overview"
611: 2. Изучите основные показатели безопасности:
612:    - Общий трафик за последние 24 часа
613:    - Предотвращенные угрозы
614:    - Трафик, обслуженный Cloudflare vs трафик, направленный на ваш origin-сервер
615: 
616: ### 6.3 Мониторинг трафика через Analytics
617: 
618: 1. Перейдите на вкладку "Analytics"
619: 2. Изучите доступные данные о:
620:    - Трафике и пользователях
621:    - Распределении по странам
622:    - Браузерах и устройствах
623: 3. Эти данные могут помочь выявить аномалии, которые могут указывать на проблемы безопасности
624: 
625: ## 7. Заключительные проверки и тесты
626: 
627: ### 7.1 Проверка WAF и безопасности
628: 
629: 1. Используйте инструменты для проверки безопасности, например, OWASP ZAP или ручное тестирование
630: 2. Проверьте, блокирует ли WAF простые атаки, такие как SQL-инъекции или XSS
631: 3. Убедитесь, что IP Firewall работает правильно (если настроен)
632: 
633: ### 7.2 Тестирование производительности
634: 
635: 1. Используйте инструменты для проверки производительности:
636:    - [Google PageSpeed Insights](https://pagespeed.web.dev/)
637:    - [WebPageTest](https://www.webpagetest.org/)
638: 2. Сравните результаты до и после настройки Cloudflare
639: 
640: ### 7.3 Проверка защиты от ботов
641: 
642: 1. Проверьте работу Bot Fight Mode, попытавшись имитировать автоматизированный трафик
643: 2. Убедитесь, что блокируются известные боты, но разрешены полезные боты (например, поисковые системы)
644: 
645: ## 8. Регулярное обслуживание
646: 
647: ### 8.1 Регулярные обновления настроек безопасности
648: 
649: 1. Следите за обновлениями WAF Managed Rules от Cloudflare
650: 2. Регулярно проверяйте логи безопасности и оповещения
651: 3. Обновляйте правила кэширования и оптимизации по мере изменения вашего сайта
652: 
653: ### 8.2 Мониторинг возникающих угроз
654: 
655: 1. Подпишитесь на блог Cloudflare и информационные бюллетени по безопасности
656: 2. Регулярно проверяйте аналитику безопасности для выявления новых типов атак
657: 3. Адаптируйте настройки безопасности для защиты от новых угроз
658: 
659: ## Заключение
660: 
661: После выполнения всех вышеуказанных шагов ваш сайт будет защищен от большинства современных веб-угроз, включая:
662: - DDoS-атаки и вредоносный трафик
663: - Боты, в том числе ИИ-боты для скрапинга
664: - Уязвимости веб-приложений через WAF
665: - Угрозы на стороне клиента через Page Shield
666: - Проблемы производительности благодаря оптимизациям Cloudflare
667: 
668: Регулярно проверяйте настройки и аналитику, чтобы адаптировать защиту к меняющемуся ландшафту угроз.
669: 
670: Да, определённо стоит настроить "Client-side abuse" (мониторинг скриптов на стороне клиента), даже в бесплатном плане Cloudflare. Это базовая, но полезная функция для отслеживания JavaScript-зависимостей и обнаружения потенциально вредоносных скриптов.
671: 
672: Вот как это настроить:
673: 
674: 1. Перейдите в раздел "Security" > "Client-side abuse"
675: 
676: 2. Для "Continuous script monitoring" (непрерывный мониторинг скриптов) нажмите "Configure" и активируйте эту функцию, она позволит отслеживать JavaScript-ресурсы, загружаемые на вашем сайте
677: 
678: 3. Настройте уведомления:
679:    - Нажмите "Go to notifications" 
680:    - Выберите "Add notification"
681:    - В типе уведомления выберите "Page Shield"
682:    - Настройте условия, при которых вы хотите получать оповещения:
683:      - Обнаружение нового скрипта
684:      - Обнаружение вредоносного скрипта
685:      - Изменения в существующем скрипте
686:    - Выберите способ получения уведомлений (Email, PagerDuty и т.д.)
687: 
688: 4. Вернитесь в раздел "Client-side abuse" и настройте "Data processing" (обработка данных):
689:    - Нажмите "Edit"
690:    - Выберите, какие данные будут записываться в отчеты мониторинга
691:    - Рекомендуется включить все доступные опции для максимальной заметности
692: 
693: Эта функция особенно важна для веб-приложений, которые загружают различные JavaScript-библиотеки или имеют интеграции со сторонними сервисами. Она поможет вам быстро обнаружить, если на ваш сайт внедрят вредоносный код или если произойдут подозрительные изменения в существующих скриптах.
694: 
695: Хотя возможности ограничены в бесплатном плане, даже базовый мониторинг существенно повышает уровень безопасности вашего приложения.
````

## File: План развёртывания приложения и CI:CD/5. deployment-plan.md
````markdown
   1: # План настройки процесса деплоя
   2: 
   3: ## 1. Создание скриптов деплоя
   4: 
   5: ### 1.1. Скрипт для сборки, тестирования и деплоя Next.js приложения
   6: 
   7: ```bash
   8: #!/bin/bash
   9: # deploy-frontend.sh
  10: 
  11: set -e  # Останавливаться при любой ошибке
  12: 
  13: # Переменные окружения
  14: ENV=${1:-production}
  15: DEPLOY_ID=$(date +%Y%m%d%H%M%S)
  16: APP_NAME="quer-calc-next"
  17: DEPLOY_PATH="/var/www/$APP_NAME"
  18: RELEASE_PATH="$DEPLOY_PATH/releases/$DEPLOY_ID"
  19: CURRENT_PATH="$DEPLOY_PATH/current"
  20: PREVIOUS_PATH="$DEPLOY_PATH/previous"
  21: 
  22: echo "🚀 Начинаем деплой фронтенда ($ENV) с ID: $DEPLOY_ID"
  23: 
  24: # Шаг 1: Подготовка директорий
  25: echo "📁 Подготовка директорий"
  26: mkdir -p $RELEASE_PATH
  27: 
  28: # Шаг 2: Установка зависимостей и сборка
  29: echo "📦 Установка зависимостей"
  30: npm ci
  31: 
  32: echo "🔍 Запуск линтера"
  33: npm run lint
  34: 
  35: echo "🧪 Запуск тестов"
  36: npm test
  37: 
  38: echo "🔨 Сборка приложения"
  39: NODE_ENV=$ENV npm run build
  40: 
  41: # Шаг 3: Копирование файлов в директорию релиза
  42: echo "📋 Копирование файлов для деплоя"
  43: cp -R .next $RELEASE_PATH/
  44: cp -R public $RELEASE_PATH/
  45: cp -R node_modules $RELEASE_PATH/
  46: cp package.json $RELEASE_PATH/
  47: cp next.config.js $RELEASE_PATH/
  48: 
  49: # Шаг 4: Создание .env файла
  50: echo "🔧 Создание .env файла для $ENV"
  51: envsubst < .env.$ENV > $RELEASE_PATH/.env
  52: 
  53: # Шаг 5: Переключение на новую версию (атомарно)
  54: echo "🔄 Переключение на новую версию"
  55: if [ -L $CURRENT_PATH ]; then
  56:   if [ -L $PREVIOUS_PATH ]; then
  57:     rm $PREVIOUS_PATH
  58:   fi
  59:   mv $CURRENT_PATH $PREVIOUS_PATH
  60: fi
  61: ln -s $RELEASE_PATH $CURRENT_PATH
  62: 
  63: # Шаг 6: Перезапуск сервиса
  64: echo "🔄 Перезапуск сервиса"
  65: sudo systemctl restart $APP_NAME
  66: 
  67: # Шаг 7: Проверка работоспособности
  68: echo "🔍 Проверка работоспособности"
  69: sleep 5  # Даем сервису время на запуск
  70: 
  71: health_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/healthcheck)
  72: if [ $health_status -ne 200 ]; then
  73:   echo "❌ Деплой не прошел проверку работоспособности. Выполняем откат."
  74:   rm $CURRENT_PATH
  75:   ln -s $PREVIOUS_PATH $CURRENT_PATH
  76:   sudo systemctl restart $APP_NAME
  77:   exit 1
  78: fi
  79: 
  80: # Шаг 8: Очистка старых релизов (оставляем последние 5)
  81: echo "🧹 Очистка старых релизов"
  82: ls -dt $DEPLOY_PATH/releases/* | tail -n +6 | xargs -r rm -rf
  83: 
  84: echo "✅ Деплой фронтенда успешно завершен"
  85: ```
  86: 
  87: ### 1.2. Скрипт для компиляции и деплоя Rust бэкенда
  88: 
  89: ```bash
  90: #!/bin/bash
  91: # deploy-backend.sh
  92: 
  93: set -e  # Останавливаться при любой ошибке
  94: 
  95: # Переменные окружения
  96: ENV=${1:-production}
  97: DEPLOY_ID=$(date +%Y%m%d%H%M%S)
  98: APP_NAME="quer-calc-backend"
  99: DEPLOY_PATH="/var/www/$APP_NAME"
 100: RELEASE_PATH="$DEPLOY_PATH/releases/$DEPLOY_ID"
 101: CURRENT_PATH="$DEPLOY_PATH/current"
 102: PREVIOUS_PATH="$DEPLOY_PATH/previous"
 103: CONFIG_PATH="/etc/$APP_NAME"
 104: 
 105: echo "🚀 Начинаем деплой бэкенда ($ENV) с ID: $DEPLOY_ID"
 106: 
 107: # Шаг 1: Подготовка директорий
 108: echo "📁 Подготовка директорий"
 109: mkdir -p $RELEASE_PATH
 110: 
 111: # Шаг 2: Компиляция и тестирование
 112: echo "🧪 Запуск тестов"
 113: cargo test
 114: 
 115: echo "🔨 Сборка приложения в режиме release"
 116: cargo build --release
 117: 
 118: # Шаг 3: Копирование бинарного файла в директорию релиза
 119: echo "📋 Копирование файлов для деплоя"
 120: cp target/release/$APP_NAME $RELEASE_PATH/
 121: mkdir -p $RELEASE_PATH/config
 122: 
 123: # Шаг 4: Создание конфигурационного файла
 124: echo "🔧 Создание конфигурационного файла для $ENV"
 125: envsubst < config.$ENV.toml > $RELEASE_PATH/config/config.toml
 126: 
 127: # Шаг 5: Переключение на новую версию (атомарно)
 128: echo "🔄 Переключение на новую версию"
 129: if [ -L $CURRENT_PATH ]; then
 130:   if [ -L $PREVIOUS_PATH ]; then
 131:     rm $PREVIOUS_PATH
 132:   fi
 133:   mv $CURRENT_PATH $PREVIOUS_PATH
 134: fi
 135: ln -s $RELEASE_PATH $CURRENT_PATH
 136: 
 137: # Шаг 6: Запуск новой версии сервиса
 138: echo "🔄 Перезапуск сервиса с использованием systemd socket activation для zero-downtime"
 139: sudo systemctl reload $APP_NAME
 140: 
 141: # Шаг 7: Проверка работоспособности
 142: echo "🔍 Проверка работоспособности"
 143: sleep 5  # Даем сервису время на запуск
 144: 
 145: health_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/healthcheck)
 146: if [ $health_status -ne 200 ]; then
 147:   echo "❌ Деплой не прошел проверку работоспособности. Выполняем откат."
 148:   rm $CURRENT_PATH
 149:   ln -s $PREVIOUS_PATH $CURRENT_PATH
 150:   sudo systemctl restart $APP_NAME
 151:   exit 1
 152: fi
 153: 
 154: # Шаг 8: Очистка старых релизов (оставляем последние 5)
 155: echo "🧹 Очистка старых релизов"
 156: ls -dt $DEPLOY_PATH/releases/* | tail -n +6 | xargs -r rm -rf
 157: 
 158: echo "✅ Деплой бэкенда успешно завершен"
 159: ```
 160: 
 161: ### 1.3. Скрипт для выполнения миграций базы данных
 162: 
 163: ```bash
 164: #!/bin/bash
 165: # database-migrate.sh
 166: 
 167: set -e  # Останавливаться при любой ошибке
 168: 
 169: # Переменные окружения
 170: ENV=${1:-production}
 171: MIGRATIONS_DIR="./migrations"
 172: BACKUP_DIR="./backups"
 173: TIMESTAMP=$(date +%Y%m%d%H%M%S)
 174: 
 175: echo "🚀 Начинаем миграцию базы данных ($ENV)"
 176: 
 177: # Шаг 1: Загрузка переменных окружения
 178: echo "📋 Загрузка переменных окружения"
 179: if [ -f .env.$ENV ]; then
 180:   source .env.$ENV
 181: else
 182:   echo "❌ Файл .env.$ENV не найден"
 183:   exit 1
 184: fi
 185: 
 186: # Шаг 2: Создание резервной копии перед миграцией
 187: echo "💾 Создание резервной копии базы данных"
 188: mkdir -p $BACKUP_DIR
 189: pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME -F c -f $BACKUP_DIR/backup_$TIMESTAMP.dump
 190: 
 191: # Шаг 3: Проверка состояния миграций
 192: echo "🔍 Проверка состояния миграций"
 193: # Используем инструмент sqlx для миграций (или другой по вашему выбору)
 194: sqlx migrate info --database-url $DATABASE_URL
 195: 
 196: # Шаг 4: Применение миграций
 197: echo "🔄 Применение миграций"
 198: sqlx migrate run --database-url $DATABASE_URL
 199: 
 200: # Шаг 5: Проверка после миграции
 201: echo "✅ Проверка состояния после миграции"
 202: sqlx migrate info --database-url $DATABASE_URL
 203: 
 204: echo "✅ Миграция базы данных успешно завершена"
 205: ```
 206: 
 207: ### 1.4. Скрипт для отката миграций
 208: 
 209: ```bash
 210: #!/bin/bash
 211: # database-rollback.sh
 212: 
 213: set -e  # Останавливаться при любой ошибке
 214: 
 215: # Переменные окружения
 216: ENV=${1:-production}
 217: BACKUP_DIR="./backups"
 218: TIMESTAMP=${2:-$(ls -t $BACKUP_DIR | head -1 | sed 's/backup_\(.*\).dump/\1/')}
 219: 
 220: echo "🔄 Начинаем откат базы данных ($ENV) к версии $TIMESTAMP"
 221: 
 222: # Шаг 1: Загрузка переменных окружения
 223: echo "📋 Загрузка переменных окружения"
 224: if [ -f .env.$ENV ]; then
 225:   source .env.$ENV
 226: else
 227:   echo "❌ Файл .env.$ENV не найден"
 228:   exit 1
 229: fi
 230: 
 231: # Шаг 2: Проверка наличия резервной копии
 232: BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.dump"
 233: if [ ! -f $BACKUP_FILE ]; then
 234:   echo "❌ Резервная копия $BACKUP_FILE не найдена"
 235:   exit 1
 236: fi
 237: 
 238: # Шаг 3: Восстановление из резервной копии
 239: echo "🔄 Восстановление из резервной копии"
 240: pg_restore -h $DB_HOST -U $DB_USER -d $DB_NAME -c -F c $BACKUP_FILE
 241: 
 242: echo "✅ Откат базы данных успешно завершен"
 243: ```
 244: 
 245: ## 2. Настройка CI/CD с GitHub Actions
 246: 
 247: ### 2.1. Пайплайн для тестирования при Pull Request (`.github/workflows/pull-request.yml`)
 248: 
 249: ```yaml
 250: name: Pull Request Checks
 251: 
 252: on:
 253:   pull_request:
 254:     branches: [ main, staging, develop ]
 255: 
 256: jobs:
 257:   frontend-test:
 258:     name: Frontend Tests
 259:     runs-on: ubuntu-latest
 260:     
 261:     steps:
 262:       - uses: actions/checkout@v3
 263:       
 264:       - name: Set up Node.js
 265:         uses: actions/setup-node@v3
 266:         with:
 267:           node-version: '18'
 268:           cache: 'npm'
 269:       
 270:       - name: Install dependencies
 271:         run: npm ci
 272:       
 273:       - name: Run linting
 274:         run: npm run lint
 275:       
 276:       - name: Run tests
 277:         run: npm test
 278:       
 279:       - name: Build check
 280:         run: npm run build
 281: 
 282:   backend-test:
 283:     name: Backend Tests
 284:     runs-on: ubuntu-latest
 285:     
 286:     steps:
 287:       - uses: actions/checkout@v3
 288:       
 289:       - name: Set up Rust
 290:         uses: actions-rs/toolchain@v1
 291:         with:
 292:           profile: minimal
 293:           toolchain: stable
 294:           override: true
 295:           components: rustfmt, clippy
 296:       
 297:       - name: Rust cache
 298:         uses: Swatinem/rust-cache@v2
 299:       
 300:       - name: Run cargo fmt
 301:         uses: actions-rs/cargo@v1
 302:         with:
 303:           command: fmt
 304:           args: --all -- --check
 305:       
 306:       - name: Run cargo clippy
 307:         uses: actions-rs/cargo@v1
 308:         with:
 309:           command: clippy
 310:           args: -- -D warnings
 311:       
 312:       - name: Run tests
 313:         uses: actions-rs/cargo@v1
 314:         with:
 315:           command: test
 316:       
 317:       - name: Build check
 318:         uses: actions-rs/cargo@v1
 319:         with:
 320:           command: build
 321:           args: --release
 322: ```
 323: 
 324: ### 2.2. Пайплайн для автоматического деплоя на staging (`.github/workflows/staging-deploy.yml`)
 325: 
 326: ```yaml
 327: name: Staging Deployment
 328: 
 329: on:
 330:   push:
 331:     branches: [ staging ]
 332: 
 333: jobs:
 334:   deploy-staging:
 335:     name: Deploy to Staging
 336:     runs-on: ubuntu-latest
 337:     environment: staging
 338:     
 339:     steps:
 340:       - uses: actions/checkout@v3
 341:       
 342:       # Общие шаги сборки и тестирования для фронтенда и бэкенда
 343:       - name: Set up Node.js
 344:         uses: actions/setup-node@v3
 345:         with:
 346:           node-version: '18'
 347:           cache: 'npm'
 348:       
 349:       - name: Set up Rust
 350:         uses: actions-rs/toolchain@v1
 351:         with:
 352:           profile: minimal
 353:           toolchain: stable
 354:           override: true
 355:       
 356:       - name: Build and test Frontend
 357:         run: |
 358:           npm ci
 359:           npm run lint
 360:           npm test
 361:           npm run build
 362:       
 363:       - name: Build and test Backend
 364:         run: |
 365:           cd backend-rust
 366:           cargo test
 367:           cargo build --release
 368:       
 369:       # Деплой на Staging-сервер с использованием rolling release
 370:       - name: Deploy to Staging
 371:         uses: appleboy/ssh-action@master
 372:         with:
 373:           host: ${{ secrets.STAGING_HOST }}
 374:           username: ${{ secrets.STAGING_USERNAME }}
 375:           key: ${{ secrets.STAGING_SSH_KEY }}
 376:           script_stop: true
 377:           script: |
 378:             # Создаем уникальный ID для деплоя
 379:             DEPLOY_ID=$(date +%Y%m%d%H%M%S)
 380:             
 381:             # Клонируем репозиторий во временную директорию
 382:             git clone --depth 1 -b staging ${{ github.repository }} /tmp/deployment-$DEPLOY_ID
 383:             cd /tmp/deployment-$DEPLOY_ID
 384:             
 385:             # Деплоим бэкенд с zero-downtime
 386:             cd backend-rust
 387:             ./deploy-backend.sh staging
 388:             
 389:             # Проверяем работоспособность бэкенда
 390:             backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/healthcheck)
 391:             if [ $backend_status -ne 200 ]; then
 392:               echo "Backend deployment failed health check. Rolling back."
 393:               ./rollback.sh
 394:               exit 1
 395:             fi
 396:             
 397:             # Деплоим фронтенд с zero-downtime
 398:             cd ../
 399:             ./deploy-frontend.sh staging
 400:             
 401:             # Проверяем работоспособность фронтенда
 402:             frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/healthcheck)
 403:             if [ $frontend_status -ne 200 ]; then
 404:               echo "Frontend deployment failed health check. Rolling back."
 405:               ./rollback.sh
 406:               exit 1
 407:             fi
 408:             
 409:             # Очистка
 410:             cd /
 411:             rm -rf /tmp/deployment-$DEPLOY_ID
 412:       
 413:       # Автоматические интеграционные тесты после деплоя
 414:       - name: Run Integration Tests
 415:         run: |
 416:           npm run test:integration:staging
 417: ```
 418: 
 419: ### 2.3. Пайплайн для деплоя на production (`.github/workflows/production-deploy.yml`)
 420: 
 421: ```yaml
 422: name: Production Deployment
 423: 
 424: on:
 425:   release:
 426:     types: [published]
 427: 
 428: jobs:
 429:   deploy-production:
 430:     name: Deploy to Production
 431:     runs-on: ubuntu-latest
 432:     environment: production
 433:     
 434:     steps:
 435:       - uses: actions/checkout@v3
 436:       
 437:       # Общие шаги сборки и тестирования для фронтенда и бэкенда
 438:       - name: Set up Node.js
 439:         uses: actions/setup-node@v3
 440:         with:
 441:           node-version: '18'
 442:           cache: 'npm'
 443:       
 444:       - name: Set up Rust
 445:         uses: actions-rs/toolchain@v1
 446:         with:
 447:           profile: minimal
 448:           toolchain: stable
 449:           override: true
 450:       
 451:       - name: Build and test Frontend
 452:         run: |
 453:           npm ci
 454:           npm run lint
 455:           npm test
 456:           npm run build
 457:       
 458:       - name: Build and test Backend
 459:         run: |
 460:           cd backend-rust
 461:           cargo test
 462:           cargo build --release
 463:       
 464:       # Деплой на Production-сервер с использованием rolling release
 465:       - name: Deploy to Production
 466:         uses: appleboy/ssh-action@master
 467:         with:
 468:           host: ${{ secrets.PRODUCTION_HOST }}
 469:           username: ${{ secrets.PRODUCTION_USERNAME }}
 470:           key: ${{ secrets.PRODUCTION_SSH_KEY }}
 471:           script_stop: true
 472:           script: |
 473:             # Создаем уникальный ID для деплоя
 474:             DEPLOY_ID=$(date +%Y%m%d%H%M%S)
 475:             
 476:             # Клонируем репозиторий во временную директорию
 477:             git clone --depth 1 -b main ${{ github.repository }} /tmp/deployment-$DEPLOY_ID
 478:             cd /tmp/deployment-$DEPLOY_ID
 479:             
 480:             # Выполняем миграцию базы данных (с резервным копированием)
 481:             ./database-migrate.sh production
 482:             
 483:             # Деплоим бэкенд с zero-downtime на первый сервер
 484:             cd backend-rust
 485:             
 486:             # Rolling release - деплой на 50% серверов
 487:             for server in $(cat /etc/quer-calc/backend-servers-group1.txt); do
 488:               ssh $server "./deploy-backend.sh production"
 489:               
 490:               # Проверяем работоспособность
 491:               backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:8080/api/healthcheck)
 492:               if [ $backend_status -ne 200 ]; then
 493:                 echo "Backend deployment failed health check on $server. Rolling back."
 494:                 ssh $server "./rollback.sh backend"
 495:                 exit 1
 496:               fi
 497:             done
 498:             
 499:             # Деплоим фронтенд с zero-downtime на первую группу серверов
 500:             cd ../
 501:             
 502:             # Rolling release - деплой на 50% серверов
 503:             for server in $(cat /etc/quer-calc/frontend-servers-group1.txt); do
 504:               ssh $server "./deploy-frontend.sh production"
 505:               
 506:               # Проверяем работоспособность
 507:               frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:3000/api/healthcheck)
 508:               if [ $frontend_status -ne 200 ]; then
 509:                 echo "Frontend deployment failed health check on $server. Rolling back."
 510:                 ssh $server "./rollback.sh frontend"
 511:                 exit 1
 512:               fi
 513:             done
 514:             
 515:             # Если первая группа успешно обновлена, переходим ко второй группе
 516:             for server in $(cat /etc/quer-calc/backend-servers-group2.txt); do
 517:               ssh $server "./deploy-backend.sh production"
 518:               backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:8080/api/healthcheck)
 519:               if [ $backend_status -ne 200 ]; then
 520:                 echo "Backend deployment failed health check on $server. Rolling back all servers."
 521:                 for rollback_server in $(cat /etc/quer-calc/backend-servers-all.txt); do
 522:                   ssh $rollback_server "./rollback.sh backend"
 523:                 done
 524:                 exit 1
 525:               fi
 526:             done
 527:             
 528:             for server in $(cat /etc/quer-calc/frontend-servers-group2.txt); do
 529:               ssh $server "./deploy-frontend.sh production"
 530:               frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:3000/api/healthcheck)
 531:               if [ $frontend_status -ne 200 ]; then
 532:                 echo "Frontend deployment failed health check on $server. Rolling back all servers."
 533:                 for rollback_server in $(cat /etc/quer-calc/frontend-servers-all.txt); do
 534:                   ssh $rollback_server "./rollback.sh frontend"
 535:                 done
 536:                 exit 1
 537:               fi
 538:             done
 539:             
 540:             # Очистка
 541:             cd /
 542:             rm -rf /tmp/deployment-$DEPLOY_ID
 543:       
 544:       # Запуск smoke-тестов после полного деплоя
 545:       - name: Run Smoke Tests
 546:         run: |
 547:           npm run test:smoke:production
 548:       
 549:       # Мониторинг производительности после деплоя
 550:       - name: Performance Monitoring
 551:         run: |
 552:           npm run monitor:performance
 553: ```
 554: 
 555: ## 3. Скрипты для post-deploy проверок
 556: 
 557: ### 3.1. Скрипт для проверки доступности (`health-check.sh`)
 558: 
 559: ```bash
 560: #!/bin/bash
 561: # health-check.sh
 562: 
 563: set -e  # Останавливаться при любой ошибке
 564: 
 565: # Переменные окружения
 566: ENV=${1:-production}
 567: FRONTEND_URL=${2:-"http://localhost:3000"}
 568: BACKEND_URL=${3:-"http://localhost:8080"}
 569: TIMEOUT=5
 570: 
 571: echo "🔍 Начинаем проверку доступности ($ENV)"
 572: 
 573: # Шаг 1: Проверка Frontend health endpoint
 574: echo "🌐 Проверка доступности фронтенда"
 575: frontend_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $FRONTEND_URL/api/healthcheck)
 576: if [ $frontend_status -ne 200 ]; then
 577:   echo "❌ Фронтенд недоступен (статус: $frontend_status)"
 578:   exit 1
 579: else
 580:   echo "✅ Фронтенд доступен"
 581: fi
 582: 
 583: # Шаг 2: Проверка Backend health endpoint
 584: echo "🌐 Проверка доступности бэкенда"
 585: backend_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $BACKEND_URL/api/healthcheck)
 586: if [ $backend_status -ne 200 ]; then
 587:   echo "❌ Бэкенд недоступен (статус: $backend_status)"
 588:   exit 1
 589: else
 590:   echo "✅ Бэкенд доступен"
 591: fi
 592: 
 593: # Шаг 3: Проверка основных маршрутов фронтенда
 594: echo "🌐 Проверка основных маршрутов фронтенда"
 595: routes=("/" "/calculator" "/sign-in" "/sign-up")
 596: for route in "${routes[@]}"; do
 597:   route_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $FRONTEND_URL$route)
 598:   if [ $route_status -ne 200 ]; then
 599:     echo "❌ Маршрут $route недоступен (статус: $route_status)"
 600:     exit 1
 601:   else
 602:     echo "✅ Маршрут $route доступен"
 603:   fi
 604: done
 605: 
 606: # Шаг 4: Проверка API эндпоинтов
 607: echo "🌐 Проверка API эндпоинтов"
 608: api_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $BACKEND_URL/api/calculate -X POST -H "Content-Type: application/json" -d '{"entryPrice":"100","stopLoss":"90","risk":"1","riskUnit":"percent","accountBalance":"10000","nominalRR":"2","maxLeverage":"10","entryFee":"0.1","exitFee":"0.1","maintenanceMargin":"0.5","safetyBuffer":"2"}')
 609: if [ $api_status -ne 200 ]; then
 610:   echo "❌ API calculate недоступен (статус: $api_status)"
 611:   exit 1
 612: else
 613:   echo "✅ API calculate доступен"
 614: fi
 615: 
 616: echo "✅ Все проверки доступности успешно пройдены"
 617: ```
 618: 
 619: ### 3.2. Скрипт для автоматических smoke-тестов (`smoke-tests.js`)
 620: 
 621: ```javascript
 622: // smoke-tests.js
 623: const puppeteer = require('puppeteer');
 624: const axios = require('axios');
 625: 
 626: const BASE_URL = process.env.BASE_URL || 'https://app.quercalc.com';
 627: const API_URL = process.env.API_URL || 'https://api.quercalc.com';
 628: const TIMEOUT = 30000;
 629: 
 630: (async () => {
 631:   console.log('🔍 Запуск smoke-тестов');
 632:   
 633:   // Тест 1: Проверка API calculate
 634:   console.log('🧪 Тест 1: Проверка API calculate');
 635:   try {
 636:     const apiResponse = await axios.post(`${API_URL}/api/calculate`, {
 637:       entryPrice: '100',
 638:       stopLoss: '90',
 639:       risk: '1',
 640:       riskUnit: 'percent',
 641:       accountBalance: '10000',
 642:       nominalRR: '2',
 643:       maxLeverage: '10',
 644:       entryFee: '0.1',
 645:       exitFee: '0.1',
 646:       maintenanceMargin: '0.5',
 647:       safetyBuffer: '2'
 648:     }, {
 649:       timeout: TIMEOUT
 650:     });
 651:     
 652:     if (apiResponse.status !== 200 || !apiResponse.data.positionSize) {
 653:       throw new Error(`Некорректный ответ API: ${JSON.stringify(apiResponse.data)}`);
 654:     }
 655:     console.log('✅ Тест 1 пройден успешно');
 656:   } catch (error) {
 657:     console.error(`❌ Тест 1 не пройден: ${error.message}`);
 658:     process.exit(1);
 659:   }
 660:   
 661:   // Тест 2: Проверка загрузки страниц через Puppeteer
 662:   console.log('🧪 Тест 2: Проверка загрузки страниц');
 663:   const browser = await puppeteer.launch({
 664:     headless: 'new',
 665:     args: ['--no-sandbox', '--disable-setuid-sandbox']
 666:   });
 667:   
 668:   try {
 669:     const page = await browser.newPage();
 670:     
 671:     // Проверка главной страницы
 672:     await page.goto(`${BASE_URL}`, { waitUntil: 'networkidle2', timeout: TIMEOUT });
 673:     console.log('✅ Главная страница загружена');
 674:     
 675:     // Проверка страницы калькулятора
 676:     await page.goto(`${BASE_URL}/calculator`, { waitUntil: 'networkidle2', timeout: TIMEOUT });
 677:     const calculatorVisible = await page.evaluate(() => {
 678:       return document.querySelector('.calculator-container') !== null;
 679:     });
 680:     
 681:     if (!calculatorVisible) {
 682:       throw new Error('Калькулятор не найден на странице');
 683:     }
 684:     console.log('✅ Страница калькулятора загружена');
 685:     
 686:     // Проверка форм ввода калькулятора
 687:     await page.type('input[name="entryPrice"]', '100');
 688:     await page.type('input[name="stopLoss"]', '90');
 689:     
 690:     // Дожидаемся результатов расчета
 691:     await page.waitForSelector('.metrics-card', { timeout: TIMEOUT });
 692:     const metricsVisible = await page.evaluate(() => {
 693:       return document.querySelector('.metrics-card') !== null;
 694:     });
 695:     
 696:     if (!metricsVisible) {
 697:       throw new Error('Результаты расчета не отображаются');
 698:     }
 699:     console.log('✅ Калькулятор работает корректно');
 700:     
 701:     console.log('✅ Тест 2 пройден успешно');
 702:   } catch (error) {
 703:     console.error(`❌ Тест 2 не пройден: ${error.message}`);
 704:     await browser.close();
 705:     process.exit(1);
 706:   }
 707:   
 708:   await browser.close();
 709:   console.log('✅ Все smoke-тесты пройдены успешно');
 710: })();
 711: ```
 712: 
 713: ### 3.3. Скрипт для системы отката при неудачном деплое (`rollback.sh`)
 714: 
 715: ```bash
 716: #!/bin/bash
 717: # rollback.sh
 718: 
 719: set -e  # Останавливаться при любой ошибке
 720: 
 721: # Переменные окружения
 722: SERVICE=${1:-"all"}  # frontend, backend или all
 723: ENV=${2:-production}
 724: 
 725: echo "🔄 Начинаем откат деплоя ($SERVICE в $ENV)"
 726: 
 727: # Шаг 1: Определяем пути
 728: FRONTEND_APP="quer-calc-next"
 729: BACKEND_APP="quer-calc-backend"
 730: FRONTEND_PATH="/var/www/$FRONTEND_APP"
 731: BACKEND_PATH="/var/www/$BACKEND_APP"
 732: 
 733: # Шаг 2: Откат фронтенда, если требуется
 734: if [ "$SERVICE" = "frontend" ] || [ "$SERVICE" = "all" ]; then
 735:   echo "🔄 Откат фронтенда"
 736:   
 737:   if [ ! -L "$FRONTEND_PATH/previous" ]; then
 738:     echo "❌ Предыдущая версия фронтенда не найдена"
 739:   else
 740:     if [ -L "$FRONTEND_PATH/current" ]; then
 741:       rm "$FRONTEND_PATH/current"
 742:     fi
 743:     ln -s "$(readlink "$FRONTEND_PATH/previous")" "$FRONTEND_PATH/current"
 744:     sudo systemctl restart $FRONTEND_APP
 745:     echo "✅ Откат фронтенда выполнен успешно"
 746:   fi
 747: fi
 748: 
 749: # Шаг 3: Откат бэкенда, если требуется
 750: if [ "$SERVICE" = "backend" ] || [ "$SERVICE" = "all" ]; then
 751:   echo "🔄 Откат бэкенда"
 752:   
 753:   if [ ! -L "$BACKEND_PATH/previous" ]; then
 754:     echo "❌ Предыдущая версия бэкенда не найдена"
 755:   else
 756:     if [ -L "$BACKEND_PATH/current" ]; then
 757:       rm "$BACKEND_PATH/current"
 758:     fi
 759:     ln -s "$(readlink "$BACKEND_PATH/previous")" "$BACKEND_PATH/current"
 760:     sudo systemctl restart $BACKEND_APP
 761:     echo "✅ Откат бэкенда выполнен успешно"
 762:   fi
 763: fi
 764: 
 765: # Шаг 4: Проверка после отката
 766: echo "🔍 Проверка после отката"
 767: 
 768: # Проверяем фронтенд, если он был откачен
 769: if [ "$SERVICE" = "frontend" ] || [ "$SERVICE" = "all" ]; then
 770:   sleep 5  # Даем время на запуск
 771:   frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/healthcheck)
 772:   if [ $frontend_status -ne 200 ]; then
 773:     echo "❌ Фронтенд недоступен после отката (статус: $frontend_status)"
 774:     exit 1
 775:   else
 776:     echo "✅ Фронтенд доступен после отката"
 777:   fi
 778: fi
 779: 
 780: # Проверяем бэкенд, если он был откачен
 781: if [ "$SERVICE" = "backend" ] || [ "$SERVICE" = "all" ]; then
 782:   sleep 5  # Даем время на запуск
 783:   backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/healthcheck)
 784:   if [ $backend_status -ne 200 ]; then
 785:     echo "❌ Бэкенд недоступен после отката (статус: $backend_status)"
 786:     exit 1
 787:   else
 788:     echo "✅ Бэкенд доступен после отката"
 789:   fi
 790: fi
 791: 
 792: echo "✅ Откат деплоя успешно завершен"
 793: ```
 794: 
 795: ### 3.4. Скрипт для мониторинга производительности после деплоя (`performance-monitor.js`)
 796: 
 797: ```javascript
 798: // performance-monitor.js
 799: const axios = require('axios');
 800: const fs = require('fs');
 801: const path = require('path');
 802: 
 803: const API_URL = process.env.API_URL || 'https://api.quercalc.com';
 804: const FRONTEND_URL = process.env.FRONTEND_URL || 'https://app.quercalc.com';
 805: const THRESHOLD_MULTIPLIER = 1.5; // Допустимое увеличение времени ответа в 1.5 раза
 806: const MONITORING_DURATION = 10 * 60 * 1000; // 10 минут мониторинга
 807: const CHECK_INTERVAL = 30 * 1000; // Проверка каждые 30 секунд
 808: const METRICS_FILE = path.join(__dirname, 'baseline_metrics.json');
 809: 
 810: // Тестовый запрос для API
 811: const TEST_PAYLOAD = {
 812:   entryPrice: '100',
 813:   stopLoss: '90',
 814:   risk: '1',
 815:   riskUnit: 'percent',
 816:   accountBalance: '10000',
 817:   nominalRR: '2',
 818:   maxLeverage: '10',
 819:   entryFee: '0.1',
 820:   exitFee: '0.1',
 821:   maintenanceMargin: '0.5',
 822:   safetyBuffer: '2'
 823: };
 824: 
 825: // Функция для замера времени ответа API
 826: async function measureApiResponseTime() {
 827:   const start = Date.now();
 828:   try {
 829:     await axios.post(`${API_URL}/api/calculate`, TEST_PAYLOAD, { timeout: 10000 });
 830:     return Date.now() - start;
 831:   } catch (error) {
 832:     console.error(`API Error: ${error.message}`);
 833:     return -1; // Ошибка
 834:   }
 835: }
 836: 
 837: // Функция для замера времени загрузки фронтенда
 838: async function measureFrontendLoadTime() {
 839:   const start = Date.now();
 840:   try {
 841:     await axios.get(FRONTEND_URL, { timeout: 10000 });
 842:     return Date.now() - start;
 843:   } catch (error) {
 844:     console.error(`Frontend Error: ${error.message}`);
 845:     return -1; // Ошибка
 846:   }
 847: }
 848: 
 849: // Загрузка базовых метрик
 850: function loadBaselineMetrics() {
 851:   try {
 852:     if (fs.existsSync(METRICS_FILE)) {
 853:       const data = fs.readFileSync(METRICS_FILE, 'utf8');
 854:       return JSON.parse(data);
 855:     }
 856:   } catch (error) {
 857:     console.error(`Error loading baseline metrics: ${error.message}`);
 858:   }
 859:   
 860:   // Если файл не существует или произошла ошибка, возвращаем значения по умолчанию
 861:   return {
 862:     apiResponseTime: 500, // 500 мс
 863:     frontendLoadTime: 1000 // 1 секунда
 864:   };
 865: }
 866: 
 867: // Сохранение текущих метрик как базовых
 868: function saveBaselineMetrics(metrics) {
 869:   try {
 870:     fs.writeFileSync(METRICS_FILE, JSON.stringify(metrics, null, 2));
 871:     console.log('✅ Baseline metrics saved');
 872:   } catch (error) {
 873:     console.error(`Error saving baseline metrics: ${error.message}`);
 874:   }
 875: }
 876: 
 877: // Функция мониторинга
 878: async function monitorPerformance() {
 879:   console.log('🔍 Начинаем мониторинг производительности');
 880:   
 881:   const baseline = loadBaselineMetrics();
 882:   console.log(`Базовые метрики: API ${baseline.apiResponseTime}ms, Frontend ${baseline.frontendLoadTime}ms`);
 883:   
 884:   const maxApiTime = baseline.apiResponseTime * THRESHOLD_MULTIPLIER;
 885:   const maxFrontendTime = baseline.frontendLoadTime * THRESHOLD_MULTIPLIER;
 886:   
 887:   console.log(`Пороговые значения: API ${maxApiTime}ms, Frontend ${maxFrontendTime}ms`);
 888:   
 889:   let failures = 0;
 890:   const maxFailures = 3; // Максимальное количество последовательных провалов
 891:   
 892:   const endTime = Date.now() + MONITORING_DURATION;
 893:   
 894:   while (Date.now() < endTime) {
 895:     // Замеряем текущие метрики
 896:     const apiTime = await measureApiResponseTime();
 897:     const frontendTime = await measureFrontendLoadTime();
 898:     
 899:     console.log(`[${new Date().toISOString()}] API: ${apiTime}ms, Frontend: ${frontendTime}ms`);
 900:     
 901:     // Проверяем, не превышены ли пороги
 902:     if (apiTime === -1 || frontendTime === -1 || apiTime > maxApiTime || frontendTime > maxFrontendTime) {
 903:       failures++;
 904:       console.warn(`⚠️ Порог превышен (${failures}/${maxFailures})`);
 905:       
 906:       if (failures >= maxFailures) {
 907:         console.error('❌ Слишком много последовательных провалов! Рекомендуется откат.');
 908:         process.exit(1);
 909:       }
 910:     } else {
 911:       failures = 0; // Сбрасываем счетчик при успешной проверке
 912:     }
 913:     
 914:     // Ждем до следующей проверки
 915:     await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL));
 916:   }
 917:   
 918:   console.log('✅ Мониторинг производительности завершен успешно');
 919:   
 920:   // Сохраняем текущие метрики как базовые (если они лучше предыдущих)
 921:   const finalApiTime = await measureApiResponseTime();
 922:   const finalFrontendTime = await measureFrontendLoadTime();
 923:   
 924:   if (finalApiTime !== -1 && finalFrontendTime !== -1) {
 925:     // Используем среднее значение между текущими базовыми и новыми измерениями
 926:     const newBaseline = {
 927:       apiResponseTime: Math.min((baseline.apiResponseTime + finalApiTime) / 2, baseline.apiResponseTime),
 928:       frontendLoadTime: Math.min((baseline.frontendLoadTime + finalFrontendTime) / 2, baseline.frontendLoadTime)
 929:     };
 930:     
 931:     saveBaselineMetrics(newBaseline);
 932:   }
 933: }
 934: 
 935: monitorPerformance().catch(error => {
 936:   console.error(`Ошибка мониторинга: ${error.message}`);
 937:   process.exit(1);
 938: });
 939: ```
 940: 
 941: ## 4. Файлы конфигурации для настройки zero-downtime деплоя
 942: 
 943: ### 4.1. Конфигурация Nginx для балансировки нагрузки (`/etc/nginx/sites-available/quer-calc`)
 944: 
 945: ```nginx
 946: upstream backend {
 947:     server backend1.quer-calc.internal:8080;
 948:     server backend2.quer-calc.internal:8080;
 949:     keepalive 64;
 950: }
 951: 
 952: upstream frontend {
 953:     server frontend1.quer-calc.internal:3000;
 954:     server frontend2.quer-calc.internal:3000;
 955:     keepalive 64;
 956: }
 957: 
 958: server {
 959:     listen 80;
 960:     server_name app.quercalc.com;
 961:     return 301 https://$host$request_uri;
 962: }
 963: 
 964: server {
 965:     listen 443 ssl;
 966:     server_name app.quercalc.com;
 967: 
 968:     ssl_certificate /etc/letsencrypt/live/app.quercalc.com/fullchain.pem;
 969:     ssl_certificate_key /etc/letsencrypt/live/app.quercalc.com/privkey.pem;
 970:     ssl_protocols TLSv1.2 TLSv1.3;
 971:     ssl_prefer_server_ciphers on;
 972: 
 973:     # Настройки для фронтенда Next.js
 974:     location / {
 975:         proxy_pass http://frontend;
 976:         proxy_http_version 1.1;
 977:         proxy_set_header Upgrade $http_upgrade;
 978:         proxy_set_header Connection 'upgrade';
 979:         proxy_set_header Host $host;
 980:         proxy_set_header X-Real-IP $remote_addr;
 981:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 982:         proxy_set_header X-Forwarded-Proto $scheme;
 983:         proxy_cache_bypass $http_upgrade;
 984:         
 985:         # Настройки для zero-downtime и rolling updates
 986:         proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
 987:         proxy_next_upstream_tries 3;
 988:         proxy_read_timeout 60s;
 989:         proxy_connect_timeout 60s;
 990:     }
 991: }
 992: 
 993: server {
 994:     listen 80;
 995:     server_name api.quercalc.com;
 996:     return 301 https://$host$request_uri;
 997: }
 998: 
 999: server {
1000:     listen 443 ssl;
1001:     server_name api.quercalc.com;
1002: 
1003:     ssl_certificate /etc/letsencrypt/live/api.quercalc.com/fullchain.pem;
1004:     ssl_certificate_key /etc/letsencrypt/live/api.quercalc.com/privkey.pem;
1005:     ssl_protocols TLSv1.2 TLSv1.3;
1006:     ssl_prefer_server_ciphers on;
1007: 
1008:     # Настройки для бэкенда Rust
1009:     location / {
1010:         proxy_pass http://backend;
1011:         proxy_http_version 1.1;
1012:         proxy_set_header Host $host;
1013:         proxy_set_header X-Real-IP $remote_addr;
1014:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
1015:         proxy_set_header X-Forwarded-Proto $scheme;
1016:         
1017:         # Настройки для zero-downtime и rolling updates
1018:         proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
1019:         proxy_next_upstream_tries 3;
1020:         proxy_read_timeout 60s;
1021:         proxy_connect_timeout 60s;
1022:     }
1023: }
1024: ```
1025: 
1026: ### 4.2. Systemd Socket Activation для Zero-Downtime Restart (`/etc/systemd/system/quer-calc-backend.socket`)
1027: 
1028: ```ini
1029: [Unit]
1030: Description=Socket for Quer Calc Backend
1031: PartOf=quer-calc-backend.service
1032: 
1033: [Socket]
1034: ListenStream=8080
1035: NoDelay=true
1036: ReusePort=true
1037: Service=quer-calc-backend.service
1038: 
1039: [Install]
1040: WantedBy=sockets.target
1041: ```
1042: 
1043: ### 4.3. Systemd Service для Backend (`/etc/systemd/system/quer-calc-backend.service`)
1044: 
1045: ```ini
1046: [Unit]
1047: Description=Quer Calc Backend Service
1048: After=network.target
1049: Requires=quer-calc-backend.socket
1050: 
1051: [Service]
1052: Type=notify
1053: User=quer-calc
1054: Group=quer-calc
1055: WorkingDirectory=/var/www/quer-calc-backend/current
1056: ExecStart=/var/www/quer-calc-backend/current/quer-calc-backend
1057: Restart=on-failure
1058: RestartSec=5s
1059: TimeoutStartSec=5
1060: TimeoutStopSec=5
1061: 
1062: # Secure service settings
1063: PrivateTmp=true
1064: ProtectSystem=full
1065: NoNewPrivileges=true
1066: 
1067: # Environment variables
1068: Environment=ENV=production
1069: 
1070: [Install]
1071: WantedBy=multi-user.target
1072: ```
1073: 
1074: ### 4.4. Systemd Service для Frontend (`/etc/systemd/system/quer-calc-next.service`)
1075: 
1076: ```ini
1077: [Unit]
1078: Description=Quer Calc Frontend Service
1079: After=network.target
1080: 
1081: [Service]
1082: Type=simple
1083: User=quer-calc
1084: Group=quer-calc
1085: WorkingDirectory=/var/www/quer-calc-next/current
1086: ExecStart=/usr/bin/node /var/www/quer-calc-next/current/node_modules/.bin/next start
1087: Restart=on-failure
1088: RestartSec=5s
1089: 
1090: # Secure service settings
1091: PrivateTmp=true
1092: ProtectSystem=full
1093: NoNewPrivileges=true
1094: 
1095: # Environment variables
1096: Environment=NODE_ENV=production
1097: Environment=PORT=3000
1098: 
1099: [Install]
1100: WantedBy=multi-user.target
1101: ```
1102: 
1103: ## 5. План внедрения
1104: 
1105: 1. **Подготовительные работы (1-2 дня):**
1106:    - Настройка серверов (staging и production)
1107:    - Установка необходимого ПО (Nginx, Docker, базы данных)
1108:    - Настройка DNS и SSL-сертификатов
1109: 
1110: 2. **Настройка CI/CD инфраструктуры (1-2 дня):**
1111:    - Создание репозитория секретов в GitHub
1112:    - Настройка GitHub Actions workflow файлов
1113:    - Настройка окружений в GitHub (staging и production)
1114: 
1115: 3. **Создание и тестирование скриптов деплоя (2-3 дня):**
1116:    - Написание и отладка скриптов для деплоя фронтенда
1117:    - Написание и отладка скриптов для деплоя бэкенда
1118:    - Написание и отладка скриптов для миграций БД
1119:    - Тестирование системы отката
1120: 
1121: 4. **Настройка системы мониторинга и оповещений (1-2 дня):**
1122:    - Настройка health-check эндпоинтов
1123:    - Настройка алертов при падении сервисов
1124:    - Интеграция с системой мониторинга (например, Grafana + Prometheus)
1125: 
1126: 5. **Тестирование полного цикла деплоя (1-2 дня):**
1127:    - Тестирование на staging-окружении
1128:    - Симуляция ошибок и проверка работы системы отката
1129:    - Проверка zero-downtime обновлений
1130: 
1131: 6. **Документация и обучение (1 день):**
1132:    - Создание документации по процессу деплоя
1133:    - Обучение команды процессу релиза и отката
1134: 
1135: 7. **Запуск в production (1 день):**
1136:    - Первоначальный деплой на production-окружение
1137:    - Мониторинг и оптимизация процесса
1138: 
1139: ## 6. Мониторинг и поддержка процесса деплоя
1140: 
1141: 1. **Система мониторинга:**
1142:    - Интеграция с Prometheus для сбора метрик
1143:    - Настройка Grafana дашбордов для визуализации
1144:    - Создание алертов на критические ошибки
1145: 
1146: 2. **Логирование:**
1147:    - Централизованный сбор логов (ELK Stack или Loki)
1148:    - Настройка ротации логов
1149:    - Поиск и анализ ошибок
1150: 
1151: 3. **Оповещения:**
1152:    - Интеграция с Slack/Discord/Email для оповещений о деплоях
1153:    - Настройка оповещений при неудачном деплое
1154:    - Ежедневный отчет о состоянии сервисов
1155: 
1156: 4. **Регулярное обновление:**
1157:    - Ежемесячный пересмотр процесса деплоя
1158:    - Оптимизация скриптов и workflow
1159:    - Обновление зависимостей и инфраструктуры
````

## File: План развёртывания приложения и CI:CD/6. secrets-config-management-plan.md
````markdown
  1: # План управления секретами и конфигурацией
  2: 
  3: ## 1. Настройка переменных окружения
  4: 
  5: ### 1.1. Организация структуры директории /etc/secrets
  6: 
  7: ```bash
  8: # Создание основной структуры директорий
  9: sudo mkdir -p /etc/secrets/{staging,production}
 10: sudo mkdir -p /etc/secrets/common
 11: 
 12: # Установка правильных разрешений
 13: sudo chmod 750 /etc/secrets
 14: sudo chmod 750 /etc/secrets/{staging,production,common}
 15: 
 16: # Создание группы для доступа к секретам
 17: sudo groupadd secrets-access
 18: sudo chown root:secrets-access /etc/secrets
 19: sudo chown root:secrets-access /etc/secrets/{staging,production,common}
 20: 
 21: # Добавление сервисных пользователей в группу secrets-access
 22: sudo usermod -a -G secrets-access nodejs-app-user
 23: ```
 24: 
 25: ### 1.2. Создание файлов с секретами для разных окружений
 26: 
 27: #### Общие секреты (для всех окружений)
 28: 
 29: Создаем файл `/etc/secrets/common/app.env`:
 30: 
 31: ```env
 32: # Общие переменные для всех окружений
 33: APP_PORT=8080
 34: NODE_ENV=production
 35: RATE_LIMIT_BURST=100
 36: RATE_LIMIT_PER_SEC=50
 37: ```
 38: 
 39: #### Секреты для staging
 40: 
 41: Создаем файл `/etc/secrets/staging/app.env`:
 42: 
 43: ```env
 44: # Stripe API ключи (тестовые)
 45: STRIPE_SECRET_KEY=sk_test_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
 46: STRIPE_PUBLISHABLE_KEY=pk_test_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
 47: STRIPE_WEBHOOK_SECRET=whsec_test_1234567890abcdefghijklmnopqrstuvwxyz
 48: 
 49: # Clerk API ключи (тестовые)
 50: CLERK_SECRET_KEY=sk_test_clerk1234567890abcdefghijklmnopqrstuvwxyz
 51: CLERK_PUBLISHABLE_KEY=pk_test_clerk1234567890abcdefghijklmnopqrstuvwxyz
 52: CLERK_WEBHOOK_SECRET=whsec_test_clerk1234567890abcdefghijklmnopqrstuvwxyz
 53: 
 54: # Stripe тестовые product/price IDs
 55: NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_test_1234567890monthly
 56: NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_test_1234567890annual
 57: 
 58: # URL-адреса для окружения
 59: NEXT_PUBLIC_SITE_URL=https://staging.quercalc.com
 60: CORS_ORIGINS=https://staging.quercalc.com,http://localhost:3000
 61: 
 62: # Флаг для отладки API
 63: ALLOW_DIRECT_USER_ID=true
 64: ```
 65: 
 66: #### Секреты для production
 67: 
 68: Создаем файл `/etc/secrets/production/app.env`:
 69: 
 70: ```env
 71: # Stripe API ключи (боевые)
 72: STRIPE_SECRET_KEY=sk_live_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
 73: STRIPE_PUBLISHABLE_KEY=pk_live_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
 74: STRIPE_WEBHOOK_SECRET=whsec_live_1234567890abcdefghijklmnopqrstuvwxyz
 75: 
 76: # Clerk API ключи (боевые)
 77: CLERK_SECRET_KEY=sk_live_clerk1234567890abcdefghijklmnopqrstuvwxyz
 78: CLERK_PUBLISHABLE_KEY=pk_live_clerk1234567890abcdefghijklmnopqrstuvwxyz
 79: CLERK_WEBHOOK_SECRET=whsec_live_clerk1234567890abcdefghijklmnopqrstuvwxyz
 80: 
 81: # Stripe боевые product/price IDs
 82: NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_live_1234567890monthly
 83: NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_live_1234567890annual
 84: 
 85: # URL-адреса для окружения
 86: NEXT_PUBLIC_SITE_URL=https://quercalc.com
 87: CORS_ORIGINS=https://quercalc.com
 88: 
 89: # Флаг для API (выключен в production)
 90: ALLOW_DIRECT_USER_ID=false
 91: ```
 92: 
 93: ### 1.3. Настройка загрузки секретов в systemd-сервисы
 94: 
 95: #### Модификация systemd-сервиса для frontend (Next.js)
 96: 
 97: Создаем/редактируем файл `/etc/systemd/system/quercalc-frontend.service`:
 98: 
 99: ```ini
100: [Unit]
101: Description=Quer Calculator Frontend
102: After=network.target
103: 
104: [Service]
105: Type=simple
106: User=nodejs-app-user
107: WorkingDirectory=/opt/quercalc/frontend
108: EnvironmentFile=/etc/secrets/common/app.env
109: EnvironmentFile=/etc/secrets/%i/app.env
110: ExecStart=/usr/bin/node /opt/quercalc/frontend/.next/server/app.js
111: Restart=on-failure
112: RestartSec=10
113: 
114: [Install]
115: WantedBy=multi-user.target
116: ```
117: 
118: #### Модификация systemd-сервиса для backend (Rust)
119: 
120: Создаем/редактируем файл `/etc/systemd/system/quercalc-backend.service`:
121: 
122: ```ini
123: [Unit]
124: Description=Quer Calculator Backend
125: After=network.target
126: 
127: [Service]
128: Type=simple
129: User=nodejs-app-user
130: WorkingDirectory=/opt/quercalc/backend
131: EnvironmentFile=/etc/secrets/common/app.env
132: EnvironmentFile=/etc/secrets/%i/app.env
133: ExecStart=/opt/quercalc/backend/target/release/quer-calc
134: Restart=on-failure
135: RestartSec=10
136: 
137: [Install]
138: WantedBy=multi-user.target
139: ```
140: 
141: #### Создание множества инстансов для разных окружений
142: 
143: Переименовываем файлы сервисов, чтобы использовать шаблоны systemd:
144: 
145: ```bash
146: sudo mv /etc/systemd/system/quercalc-frontend.service /etc/systemd/system/quercalc-frontend@.service
147: sudo mv /etc/systemd/system/quercalc-backend.service /etc/systemd/system/quercalc-backend@.service
148: ```
149: 
150: #### Запуск и включение сервисов для staging и production
151: 
152: ```bash
153: # Перезагрузка конфигурации systemd
154: sudo systemctl daemon-reload
155: 
156: # Staging окружение
157: sudo systemctl enable --now <EMAIL>
158: sudo systemctl enable --now <EMAIL>
159: 
160: # Production окружение
161: sudo systemctl enable --now <EMAIL>
162: sudo systemctl enable --now <EMAIL>
163: ```
164: 
165: ### 1.4. Скрипт для безопасной загрузки секретов в процесс
166: 
167: Создаем файл `/opt/quercalc/scripts/load-secrets.sh`:
168: 
169: ```bash
170: #!/bin/bash
171: # Скрипт для безопасной загрузки переменных окружения
172: 
173: ENVIRONMENT=$1
174: if [ -z "$ENVIRONMENT" ]; then
175:     echo "Usage: $0 <environment>"
176:     echo "Available environments: staging, production"
177:     exit 1
178: fi
179: 
180: # Загрузка общих переменных
181: if [ -f "/etc/secrets/common/app.env" ]; then
182:     source "/etc/secrets/common/app.env"
183: fi
184: 
185: # Загрузка переменных окружения
186: if [ -f "/etc/secrets/$ENVIRONMENT/app.env" ]; then
187:     source "/etc/secrets/$ENVIRONMENT/app.env"
188: else
189:     echo "Error: Environment file for $ENVIRONMENT not found"
190:     exit 1
191: fi
192: 
193: # Запуск предоставленной команды с загруженными переменными
194: exec "${@:2}"
195: ```
196: 
197: ```bash
198: # Устанавливаем правильные разрешения
199: sudo chmod 750 /opt/quercalc/scripts/load-secrets.sh
200: sudo chown root:secrets-access /opt/quercalc/scripts/load-secrets.sh
201: ```
202: 
203: ## 2. Настройка интеграций
204: 
205: ### 2.1. Настройка Stripe
206: 
207: #### 2.1.1. Создание тестовых и боевых продуктов в Stripe
208: 
209: 1. **Регистрация/авторизация в Stripe Dashboard**:
210:    * Перейдите на [dashboard.stripe.com](https://dashboard.stripe.com)
211:    * Создайте аккаунт или войдите в существующий
212: 
213: 2. **Создание тестовых продуктов и цен** (в тестовом режиме):
214:    * Перейдите в раздел "Products" и нажмите "Add product"
215:    * Создайте два продукта:
216:      * "Monthly Subscription" с ценой $49/месяц (recurring)
217:      * "Annual Subscription" с ценой $348/год (recurring)
218:    * Сохраните ID продуктов и цен в `/etc/secrets/staging/app.env`
219: 
220: 3. **Создание боевых продуктов и цен** (в боевом режиме):
221:    * Переключитесь в боевой режим (toggle в верхней части Dashboard)
222:    * Повторите шаги создания продуктов, как для тестового режима
223:    * Сохраните ID продуктов и цен в `/etc/secrets/production/app.env`
224: 
225: #### 2.1.2. Настройка веб-хуков Stripe
226: 
227: 1. **Настройка тестовых вебхуков**:
228:    * В Stripe Dashboard перейдите в "Developers" -> "Webhooks"
229:    * Добавьте эндпоинт: `https://staging.quercalc.com/api/webhook/stripe`
230:    * Выберите события для отслеживания:
231:      * `checkout.session.completed`
232:      * `customer.subscription.updated`
233:      * `customer.subscription.deleted`
234:    * Запишите сгенерированный "Signing Secret" в `STRIPE_WEBHOOK_SECRET` в файле `/etc/secrets/staging/app.env`
235: 
236: 2. **Настройка боевых вебхуков**:
237:    * Переключитесь в боевой режим и повторите шаги
238:    * Используйте эндпоинт: `https://quercalc.com/api/webhook/stripe`
239:    * Сохраните "Signing Secret" в `/etc/secrets/production/app.env`
240: 
241: ### 2.2. Настройка Clerk
242: 
243: #### 2.2.1. Создание приложений в Clerk для разных окружений
244: 
245: 1. **Вход в Clerk Dashboard**:
246:    * Перейдите на [dashboard.clerk.dev](https://dashboard.clerk.dev)
247:    * Авторизуйтесь или создайте аккаунт
248: 
249: 2. **Создание тестового приложения**:
250:    * Нажмите "Add Application"
251:    * Назовите его "QuercCalc Staging"
252:    * Выберите "Web application"
253:    * Укажите домен: `staging.quercalc.com`
254:    * Настройте редиректы:
255:      * Sign-in URL: `https://staging.quercalc.com/sign-in`
256:      * Sign-up URL: `https://staging.quercalc.com/sign-up`
257:      * After Sign-in URL: `https://staging.quercalc.com/calculator`
258:    * Сохраните API ключи в `/etc/secrets/staging/app.env`
259: 
260: 3. **Создание продакшн приложения**:
261:    * Повторите шаги, но с названием "QuercCalc Production"
262:    * Используйте домен: `quercalc.com`
263:    * Сохраните API ключи в `/etc/secrets/production/app.env`
264: 
265: #### 2.2.2. Настройка веб-хуков Clerk
266: 
267: 1. **Настройка вебхуков для тестового приложения**:
268:    * В Clerk Dashboard выберите приложение "QuercCalc Staging"
269:    * Перейдите в раздел "Webhooks"
270:    * Добавьте эндпоинт: `https://staging.quercalc.com/api/webhook/clerk`
271:    * Выберите события:
272:      * `user.created`
273:      * `user.deleted`
274:    * Сохраните "Signing Secret" в `CLERK_WEBHOOK_SECRET` в файле `/etc/secrets/staging/app.env`
275: 
276: 2. **Настройка для продакшн приложения**:
277:    * Повторите шаги для "QuercCalc Production"
278:    * Используйте эндпоинт: `https://quercalc.com/api/webhook/clerk`
279:    * Сохраните секрет в `/etc/secrets/production/app.env`
280: 
281: ### 2.3. Настройка дополнительных сервисов
282: 
283: #### 2.3.1. Email-уведомления (SendGrid)
284: 
285: 1. **Создание аккаунта**:
286:    * Зарегистрируйтесь на [SendGrid](https://sendgrid.com)
287:    * Верифицируйте домен и создайте API-ключ
288: 
289: 2. **Добавление секретов**:
290:    * Добавьте в файлы секретов:
291: 
292: ```bash
293: # Добавление в /etc/secrets/staging/app.env
294: echo "SENDGRID_API_KEY=SG.staging_key_1234567890abcdef" | sudo tee -a /etc/secrets/staging/app.env
295: echo "SENDGRID_FROM_EMAIL=<EMAIL>" | sudo tee -a /etc/secrets/staging/app.env
296: 
297: # Добавление в /etc/secrets/production/app.env
298: echo "SENDGRID_API_KEY=SG.production_key_1234567890abcdef" | sudo tee -a /etc/secrets/production/app.env
299: echo "SENDGRID_FROM_EMAIL=<EMAIL>" | sudo tee -a /etc/secrets/production/app.env
300: ```
301: 
302: #### 2.3.2. Аналитика (Google Analytics)
303: 
304: 1. **Создание проектов в Google Analytics**:
305:    * Создайте отдельные проекты для staging и production
306: 
307: 2. **Добавление ID в конфигурацию**:
308:    * Добавьте в файлы секретов:
309: 
310: ```bash
311: # Добавление в /etc/secrets/staging/app.env
312: echo "NEXT_PUBLIC_GA_MEASUREMENT_ID=G-STAG1234567" | sudo tee -a /etc/secrets/staging/app.env
313: 
314: # Добавление в /etc/secrets/production/app.env
315: echo "NEXT_PUBLIC_GA_MEASUREMENT_ID=G-PROD1234567" | sudo tee -a /etc/secrets/production/app.env
316: ```
317: 
318: ## 3. Аудит безопасности
319: 
320: ### 3.1. Проверка файловых разрешений для секретов
321: 
322: Создаем скрипт аудита `/opt/quercalc/scripts/security-audit.sh`:
323: 
324: ```bash
325: #!/bin/bash
326: # Скрипт для проверки безопасности секретов
327: 
328: echo "===== Проверка файловых разрешений для /etc/secrets ====="
329: 
330: # Проверка разрешений основной директории
331: echo "Основная директория:"
332: ls -ld /etc/secrets
333: 
334: # Проверка поддиректорий
335: echo "Поддиректории:"
336: ls -ld /etc/secrets/common /etc/secrets/staging /etc/secrets/production
337: 
338: # Проверка файлов секретов
339: echo "Файлы секретов:"
340: ls -la /etc/secrets/common/
341: ls -la /etc/secrets/staging/
342: ls -la /etc/secrets/production/
343: 
344: # Проверка пользователей с доступом
345: echo "Пользователи с доступом к группе secrets-access:"
346: grep secrets-access /etc/group
347: 
348: # Проверка очистки секретов в логах
349: echo "Проверка логов на предмет утечки секретов:"
350: cd /var/log
351: grep -r "SECRET_KEY\|API_KEY" --include="*.log" .
352: 
353: echo "===== Завершение проверки ====="
354: ```
355: 
356: ```bash
357: # Устанавливаем права на скрипт
358: sudo chmod 700 /opt/quercalc/scripts/security-audit.sh
359: sudo chown root:root /opt/quercalc/scripts/security-audit.sh
360: ```
361: 
362: ### 3.2. Настройка механизма ротации секретов
363: 
364: #### 3.2.1. Создание скрипта для ротации секретов
365: 
366: Создаем файл `/opt/quercalc/scripts/rotate-secrets.sh`:
367: 
368: ```bash
369: #!/bin/bash
370: # Скрипт для ротации секретов
371: 
372: ENVIRONMENT=$1
373: SECRET_TYPE=$2
374: 
375: if [ -z "$ENVIRONMENT" ] || [ -z "$SECRET_TYPE" ]; then
376:     echo "Usage: $0 <environment> <secret_type>"
377:     echo "Available environments: staging, production"
378:     echo "Available secret types: stripe, clerk, sendgrid"
379:     exit 1
380: fi
381: 
382: # Проверка существования директории секретов
383: if [ ! -d "/etc/secrets/$ENVIRONMENT" ]; then
384:     echo "Error: Environment directory not found"
385:     exit 1
386: fi
387: 
388: # Резервное копирование текущих секретов
389: BACKUP_DIR="/etc/secrets/backups"
390: TIMESTAMP=$(date +%Y%m%d%H%M%S)
391: sudo mkdir -p "$BACKUP_DIR"
392: sudo cp "/etc/secrets/$ENVIRONMENT/app.env" "$BACKUP_DIR/app.env.$ENVIRONMENT.$TIMESTAMP"
393: 
394: # Функция для редактирования переменных окружения
395: update_env_var() {
396:     local file=$1
397:     local key=$2
398:     local value=$3
399:     
400:     if grep -q "^$key=" "$file"; then
401:         sudo sed -i "s|^$key=.*|$key=$value|" "$file"
402:     else
403:         echo "$key=$value" | sudo tee -a "$file" > /dev/null
404:     fi
405: }
406: 
407: # Обновление секретов в зависимости от типа
408: case "$SECRET_TYPE" in
409:     stripe)
410:         echo "Rotating Stripe API keys for $ENVIRONMENT..."
411:         read -p "Enter new Stripe Secret Key: " STRIPE_SECRET_KEY
412:         read -p "Enter new Stripe Publishable Key: " STRIPE_PUBLISHABLE_KEY
413:         read -p "Enter new Stripe Webhook Secret: " STRIPE_WEBHOOK_SECRET
414:         
415:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "STRIPE_SECRET_KEY" "$STRIPE_SECRET_KEY"
416:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "STRIPE_PUBLISHABLE_KEY" "$STRIPE_PUBLISHABLE_KEY"
417:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "STRIPE_WEBHOOK_SECRET" "$STRIPE_WEBHOOK_SECRET"
418:         ;;
419:         
420:     clerk)
421:         echo "Rotating Clerk API keys for $ENVIRONMENT..."
422:         read -p "Enter new Clerk Secret Key: " CLERK_SECRET_KEY
423:         read -p "Enter new Clerk Publishable Key: " CLERK_PUBLISHABLE_KEY
424:         read -p "Enter new Clerk Webhook Secret: " CLERK_WEBHOOK_SECRET
425:         
426:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "CLERK_SECRET_KEY" "$CLERK_SECRET_KEY"
427:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "CLERK_PUBLISHABLE_KEY" "$CLERK_PUBLISHABLE_KEY"
428:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "CLERK_WEBHOOK_SECRET" "$CLERK_WEBHOOK_SECRET"
429:         ;;
430:         
431:     sendgrid)
432:         echo "Rotating SendGrid API key for $ENVIRONMENT..."
433:         read -p "Enter new SendGrid API Key: " SENDGRID_API_KEY
434:         
435:         update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "SENDGRID_API_KEY" "$SENDGRID_API_KEY"
436:         ;;
437:         
438:     *)
439:         echo "Error: Unknown secret type"
440:         exit 1
441:         ;;
442: esac
443: 
444: echo "Secret rotation completed. Restarting services..."
445: 
446: # Перезапуск связанных сервисов
447: sudo systemctl restart quercalc-frontend@$ENVIRONMENT.service
448: sudo systemctl restart quercalc-backend@$ENVIRONMENT.service
449: 
450: echo "Services restarted successfully!"
451: ```
452: 
453: ```bash
454: # Устанавливаем правильные разрешения
455: sudo chmod 700 /opt/quercalc/scripts/rotate-secrets.sh
456: sudo chown root:root /opt/quercalc/scripts/rotate-secrets.sh
457: ```
458: 
459: #### 3.2.2. Документация по ротации секретов
460: 
461: **Зачем нужна ротация секретов:**
462: 
463: 1. **Минимизация возможного ущерба:** Даже если секреты были скомпрометированы, их регулярная замена ограничивает время, в течение которого они могут быть использованы злоумышленниками.
464: 
465: 2. **Соответствие требованиям безопасности:** Многие стандарты безопасности (например, PCI DSS) требуют регулярной ротации секретов.
466: 
467: 3. **Предотвращение неавторизованного доступа:** Бывшие сотрудники и подрядчики, которые имели доступ к секретам, не смогут использовать их после ротации.
468: 
469: 4. **Повышение защиты от утечек:** При долговременном использовании одних и тех же секретов повышается риск их случайного раскрытия.
470: 
471: **Рекомендации по ротации секретов:**
472: 
473: - **API ключи (Stripe, Clerk):** Ротация каждые 90 дней
474: - **Webhook секреты:** Ротация каждые 180 дней
475: - **Сессионные ключи:** Ротация каждые 30 дней
476: - **Ключи шифрования базы данных:** Ротация каждые 365 дней
477: 
478: ### 3.3. Настройка мониторинга доступа к секретам
479: 
480: #### 3.3.1. Установка auditd для мониторинга доступа к файлам
481: 
482: ```bash
483: # Установка auditd
484: sudo apt-get update
485: sudo apt-get install -y auditd
486: 
487: # Настройка правил аудита для директории с секретами
488: sudo bash -c 'cat > /etc/audit/rules.d/secrets.rules << EOF
489: # Мониторинг доступа к директории с секретами
490: -w /etc/secrets/ -p rwxa -k secrets_access
491: EOF'
492: 
493: # Перезапуск службы
494: sudo systemctl restart auditd
495: ```
496: 
497: #### 3.3.2. Создание скрипта для проверки журналов доступа
498: 
499: Создаем файл `/opt/quercalc/scripts/check-secrets-access.sh`:
500: 
501: ```bash
502: #!/bin/bash
503: # Скрипт для проверки доступа к секретам
504: 
505: echo "===== Проверка доступа к секретам ====="
506: 
507: # Проверка журналов auditd
508: echo "Последние записи доступа к секретам:"
509: sudo ausearch -k secrets_access -ts today
510: 
511: # Проверка неавторизованного доступа (пользователи не из группы secrets-access)
512: echo "Проверка на неавторизованный доступ:"
513: AUTHORIZED_USERS=$(grep secrets-access /etc/group | cut -d: -f4 | tr ',' ' ')
514: AUTHORIZED_USERS="$AUTHORIZED_USERS root"
515: 
516: sudo ausearch -k secrets_access -ts today | grep -i "auid" | while read line; do
517:     USER=$(echo $line | grep -o "auid=[0-9]*" | cut -d= -f2)
518:     USERNAME=$(getent passwd $USER | cut -d: -f1)
519:     
520:     AUTHORIZED=0
521:     for AUTH_USER in $AUTHORIZED_USERS; do
522:         if [ "$USERNAME" = "$AUTH_USER" ]; then
523:             AUTHORIZED=1
524:             break
525:         fi
526:     done
527:     
528:     if [ $AUTHORIZED -eq 0 ]; then
529:         echo "ПРЕДУПРЕЖДЕНИЕ: Обнаружен неавторизованный доступ от пользователя $USERNAME ($USER)"
530:     fi
531: done
532: 
533: echo "===== Завершение проверки ====="
534: ```
535: 
536: ```bash
537: # Устанавливаем правильные разрешения
538: sudo chmod 700 /opt/quercalc/scripts/check-secrets-access.sh
539: sudo chown root:root /opt/quercalc/scripts/check-secrets-access.sh
540: ```
541: 
542: #### 3.3.3. Настройка ежедневной проверки с уведомлениями
543: 
544: Создаем cron-задачу для ежедневной проверки:
545: 
546: ```bash
547: # Добавление cron-задачи для root
548: sudo bash -c 'cat > /etc/cron.d/secrets-monitor << EOF
549: # Ежедневная проверка доступа к секретам
550: 0 2 * * * root /opt/quercalc/scripts/check-secrets-access.sh | mail -s "QuercCalc Secrets Access Report" <EMAIL>
551: EOF'
552: ```
553: 
554: ## 4. Документация и обучение команды
555: 
556: ### 4.1. Создание руководства для разработчиков
557: 
558: Создаем файл `/opt/quercalc/docs/secrets-management.md`:
559: 
560: ```markdown
561: # Руководство по управлению секретами QuercCalc
562: 
563: ## Общие принципы
564: 
565: 1. **Принцип наименьших привилегий:** Используйте только те секреты, которые необходимы для вашей задачи.
566: 2. **Изоляция сред:** Никогда не смешивайте секреты из разных окружений (staging/production).
567: 3. **No hardcoding:** Никогда не включайте секреты в код напрямую.
568: 4. **Журналирование:** Не записывайте секреты в журналы.
569: 
570: ## Доступ к секретам
571: 
572: ### Локальная разработка
573: 
574: Для локальной разработки используйте файл `.env.local`:
575: 
576: ```bash
577: # Копирование шаблона env-файла
578: cp .env.example .env.local
579: 
580: # Заполнение необходимыми значениями
581: # ВНИМАНИЕ: Используйте только тестовые API-ключи для локальной разработки!
582: ```
583: 
584: ### Staging и Production
585: 
586: Секреты для staging и production хранятся в `/etc/secrets/{staging,production}/app.env`.
587: Изменения в эти файлы должны вноситься только через процедуру ротации секретов.
588: 
589: ## Процедуры
590: 
591: ### Добавление нового секрета
592: 
593: 1. Обратитесь к администратору с запросом на добавление секрета
594: 2. Предоставьте:
595:    - Название переменной
596:    - Значение для каждого окружения
597:    - Обоснование необходимости
598: 
599: ### Ротация секретов
600: 
601: Регулярная ротация секретов выполняется администратором системы.
602: Если вы заметили потенциальную компрометацию, немедленно сообщите об этом.
603: 
604: ## Интеграция в приложение
605: 
606: ### Next.js Frontend
607: 
608: Секреты доступны как переменные окружения:
609: 
610: ```javascript
611: // Пример использования
612: const stripeKey = process.env.STRIPE_PUBLISHABLE_KEY;
613: ```
614: 
615: ### Rust Backend
616: 
617: Секреты загружаются через переменные окружения:
618: 
619: ```rust
620: // Пример получения переменной окружения
621: let stripe_key = std::env::var("STRIPE_SECRET_KEY").expect("Missing STRIPE_SECRET_KEY");
622: ```
623: ```
624: 
625: ### 4.2. Настройка автоматизированной проверки конфигурации
626: 
627: Создаем скрипт `/opt/quercalc/scripts/check-config.sh`:
628: 
629: ```bash
630: #!/bin/bash
631: # Скрипт для проверки конфигурации приложения
632: 
633: ENVIRONMENT=$1
634: if [ -z "$ENVIRONMENT" ]; then
635:     echo "Usage: $0 <environment>"
636:     echo "Available environments: staging, production"
637:     exit 1
638: fi
639: 
640: echo "===== Проверка конфигурации для $ENVIRONMENT ====="
641: 
642: # Загрузка переменных окружения
643: source "/etc/secrets/common/app.env" 2>/dev/null
644: source "/etc/secrets/$ENVIRONMENT/app.env" 2>/dev/null
645: 
646: # Проверка обязательных переменных
647: REQUIRED_VARS=(
648:     "STRIPE_SECRET_KEY"
649:     "STRIPE_PUBLISHABLE_KEY"
650:     "STRIPE_WEBHOOK_SECRET"
651:     "CLERK_SECRET_KEY"
652:     "CLERK_PUBLISHABLE_KEY"
653:     "NEXT_PUBLIC_SITE_URL"
654:     "CORS_ORIGINS"
655: )
656: 
657: MISSING_VARS=0
658: for VAR in "${REQUIRED_VARS[@]}"; do
659:     if [ -z "${!VAR}" ]; then
660:         echo "ОШИБКА: Отсутствует обязательная переменная: $VAR"
661:         MISSING_VARS=$((MISSING_VARS+1))
662:     fi
663: done
664: 
665: if [ $MISSING_VARS -gt 0 ]; then
666:     echo "Найдено $MISSING_VARS отсутствующих переменных!"
667: else
668:     echo "Все обязательные переменные присутствуют."
669: fi
670: 
671: # Проверка формата ключей
672: if [[ ! "$STRIPE_SECRET_KEY" =~ ^sk_(test|live)_ ]]; then
673:     echo "ПРЕДУПРЕЖДЕНИЕ: Формат STRIPE_SECRET_KEY не соответствует ожидаемому паттерну"
674: fi
675: 
676: if [[ ! "$STRIPE_PUBLISHABLE_KEY" =~ ^pk_(test|live)_ ]]; then
677:     echo "ПРЕДУПРЕЖДЕНИЕ: Формат STRIPE_PUBLISHABLE_KEY не соответствует ожидаемому паттерну"
678: fi
679: 
680: if [[ ! "$CLERK_SECRET_KEY" =~ ^sk_(test|live)_ ]]; then
681:     echo "ПРЕДУПРЕЖДЕНИЕ: Формат CLERK_SECRET_KEY не соответствует ожидаемому паттерну"
682: fi
683: 
684: # Проверка соответствия ключей окружению
685: if [ "$ENVIRONMENT" = "production" ]; then
686:     if [[ "$STRIPE_SECRET_KEY" =~ ^sk_test_ ]]; then
687:         echo "КРИТИЧЕСКАЯ ОШИБКА: В production используется тестовый ключ Stripe!"
688:     fi
689:     if [[ "$CLERK_SECRET_KEY" =~ ^sk_test_ ]]; then
690:         echo "КРИТИЧЕСКАЯ ОШИБКА: В production используется тестовый ключ Clerk!"
691:     fi
692: elif [ "$ENVIRONMENT" = "staging" ]; then
693:     if [[ "$STRIPE_SECRET_KEY" =~ ^sk_live_ ]]; then
694:         echo "ПРЕДУПРЕЖДЕНИЕ: В staging используется боевой ключ Stripe!"
695:     fi
696:     if [[ "$CLERK_SECRET_KEY" =~ ^sk_live_ ]]; then
697:         echo "ПРЕДУПРЕЖДЕНИЕ: В staging используется боевой ключ Clerk!"
698:     fi
699: fi
700: 
701: echo "===== Завершение проверки ====="
702: ```
703: 
704: ```bash
705: # Устанавливаем правильные разрешения
706: sudo chmod 700 /opt/quercalc/scripts/check-config.sh
707: sudo chown root:root /opt/quercalc/scripts/check-config.sh
708: 
709: # Добавляем в cron для регулярных проверок
710: sudo bash -c 'cat > /etc/cron.d/config-check << EOF
711: # Ежедневная проверка конфигурации
712: 0 3 * * * root /opt/quercalc/scripts/check-config.sh staging | mail -s "QuercCalc Staging Config Check" <EMAIL>
713: 30 3 * * * root /opt/quercalc/scripts/check-config.sh production | mail -s "QuercCalc Production Config Check" <EMAIL>
714: EOF'
715: ```
````

## File: План развёртывания приложения и CI:CD/7. monitoring-logging-plan.md
````markdown
   1: # План настройки мониторинга и логирования
   2: 
   3: ## 1. Настройка логирования приложения
   4: 
   5: ### 1.1 Направление логов Next.js в определенные файлы
   6: 
   7: **Шаг 1: Установка необходимых зависимостей**
   8: ```bash
   9: cd /path/to/frontend
  10: npm install --save winston pino pino-pretty rotating-file-stream
  11: ```
  12: 
  13: **Шаг 2: Создание модуля логирования**
  14: Создайте файл `src/utils/logger.js`:
  15: 
  16: ```javascript
  17: const winston = require('winston');
  18: const path = require('path');
  19: const fs = require('fs');
  20: 
  21: // Создаем директорию для логов, если она не существует
  22: const logDir = path.join(process.cwd(), 'logs');
  23: if (!fs.existsSync(logDir)) {
  24:   fs.mkdirSync(logDir, { recursive: true });
  25: }
  26: 
  27: // Настройка форматирования
  28: const logFormat = winston.format.combine(
  29:   winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  30:   winston.format.metadata({ fillExcept: ['message', 'level', 'timestamp'] }),
  31:   winston.format.printf(({ timestamp, level, message, metadata }) => {
  32:     return `[${timestamp}] ${level.toUpperCase()}: ${message} ${Object.keys(metadata).length ? JSON.stringify(metadata) : ''}`;
  33:   })
  34: );
  35: 
  36: // Создание логгера
  37: const logger = winston.createLogger({
  38:   level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  39:   format: logFormat,
  40:   transports: [
  41:     // Логи для консоли
  42:     new winston.transports.Console({
  43:       format: winston.format.combine(
  44:         winston.format.colorize(),
  45:         logFormat
  46:       )
  47:     }),
  48:     // Логи ошибок в отдельный файл
  49:     new winston.transports.File({ 
  50:       filename: path.join(logDir, 'error.log'), 
  51:       level: 'error',
  52:       maxsize: 10485760, // 10MB
  53:       maxFiles: 5
  54:     }),
  55:     // Общие логи
  56:     new winston.transports.File({ 
  57:       filename: path.join(logDir, 'combined.log'),
  58:       maxsize: 10485760, // 10MB
  59:       maxFiles: 5
  60:     }),
  61:   ],
  62:   // Обрабатывать исключения и отказы
  63:   exceptionHandlers: [
  64:     new winston.transports.File({ filename: path.join(logDir, 'exceptions.log') })
  65:   ],
  66:   rejectionHandlers: [
  67:     new winston.transports.File({ filename: path.join(logDir, 'rejections.log') })
  68:   ]
  69: });
  70: 
  71: // Экспорт логгера
  72: module.exports = logger;
  73: ```
  74: 
  75: **Шаг 3: Интеграция логгера с API роутами Next.js**
  76: Создайте middleware для логирования в `src/middleware.js`:
  77: 
  78: ```javascript
  79: import logger from './utils/logger';
  80: import { NextResponse } from 'next/server';
  81: 
  82: export function middleware(request) {
  83:   const requestStartTime = Date.now();
  84:   const requestId = Math.random().toString(36).substring(2, 10);
  85:   
  86:   // Логирование входящего запроса
  87:   logger.info(`${request.method} ${request.nextUrl.pathname}`, {
  88:     requestId,
  89:     url: request.nextUrl.toString(),
  90:     method: request.method,
  91:     ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  92:   });
  93: 
  94:   // Обогащение запроса и обработка ответа
  95:   const response = NextResponse.next();
  96:   
  97:   // Добавление заголовка для отслеживания запроса
  98:   response.headers.set('X-Request-Id', requestId);
  99: 
 100:   // Логирование после обработки
 101:   response.on('finish', () => {
 102:     const duration = Date.now() - requestStartTime;
 103:     logger.info(`${request.method} ${request.nextUrl.pathname} completed`, {
 104:       requestId,
 105:       duration,
 106:       statusCode: response.statusCode
 107:     });
 108:   });
 109: 
 110:   return response;
 111: }
 112: 
 113: export const config = {
 114:   matcher: ['/api/:path*']
 115: };
 116: ```
 117: 
 118: **Шаг 4: Логирование глобальных ошибок**
 119: Добавьте в `src/pages/_app.js` или `src/app/layout.jsx`:
 120: 
 121: ```javascript
 122: import logger from '../utils/logger';
 123: import { useEffect } from 'react';
 124: 
 125: export default function App({ Component, pageProps }) {
 126:   useEffect(() => {
 127:     // Глобальные обработчики ошибок
 128:     const handleError = (error, info) => {
 129:       logger.error('Unhandled client error', { error: error.toString(), info });
 130:     };
 131: 
 132:     window.addEventListener('error', handleError);
 133:     window.addEventListener('unhandledrejection', handleError);
 134: 
 135:     return () => {
 136:       window.removeEventListener('error', handleError);
 137:       window.removeEventListener('unhandledrejection', handleError);
 138:     };
 139:   }, []);
 140: 
 141:   return <Component {...pageProps} />;
 142: }
 143: ```
 144: 
 145: ### 1.2 Настройка структурированного логирования для Rust бэкенда
 146: 
 147: **Шаг 1: Добавление зависимостей**
 148: Добавьте в `Cargo.toml`:
 149: 
 150: ```toml
 151: [dependencies]
 152: # Существующие зависимости...
 153: env_logger = "0.10.0"
 154: log = "0.4"
 155: serde_json = "1.0"
 156: chrono = "0.4"
 157: ```
 158: 
 159: **Шаг 2: Создание структурированного логгера**
 160: Создайте файл `src/logging.rs`:
 161: 
 162: ```rust
 163: use actix_web::middleware::Logger;
 164: use env_logger::Env;
 165: use log::{LevelFilter, info, error};
 166: use std::fs::{File, OpenOptions};
 167: use std::io::Write;
 168: use chrono::Local;
 169: use std::path::Path;
 170: 
 171: pub fn init_logger(log_dir: &str) -> std::io::Result<()> {
 172:     // Создаем директорию для логов, если она не существует
 173:     if !Path::new(log_dir).exists() {
 174:         std::fs::create_dir_all(log_dir)?;
 175:     }
 176: 
 177:     // Устанавливаем логирование
 178:     env_logger::Builder::from_env(Env::default().default_filter_or("info"))
 179:         .format(|buf, record| {
 180:             let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string();
 181:             let level = record.level();
 182:             let target = record.target();
 183:             let args = record.args();
 184:             
 185:             // Если это сообщение от actix-web, логируем только info и ниже
 186:             if target.starts_with("actix_web") && level > LevelFilter::Info {
 187:                 return Ok(());
 188:             }
 189:             
 190:             writeln!(
 191:                 buf,
 192:                 "{{\"timestamp\":\"{}\",\"level\":\"{}\",\"target\":\"{}\",\"message\":\"{}\"}}",
 193:                 timestamp, level, target, args
 194:             )
 195:         })
 196:         .init();
 197: 
 198:     // Логирование в файл
 199:     let error_log_file = format!("{}/error.log", log_dir);
 200:     let info_log_file = format!("{}/info.log", log_dir);
 201:     
 202:     // Настройка файла для ошибок
 203:     let error_file = OpenOptions::new()
 204:         .create(true)
 205:         .write(true)
 206:         .append(true)
 207:         .open(error_log_file)?;
 208:         
 209:     // Настройка файла для info-логов
 210:     let info_file = OpenOptions::new()
 211:         .create(true)
 212:         .write(true)
 213:         .append(true)
 214:         .open(info_log_file)?;
 215:     
 216:     // Логирование инициализации
 217:     info!("Logger initialized");
 218:     
 219:     Ok(())
 220: }
 221: 
 222: // Создаем кастомный FormatterFn для actix-web Logger
 223: pub fn get_logger() -> Logger {
 224:     Logger::new("%a \"%r\" %s %b \"%{Referer}i\" \"%{User-Agent}i\" %T")
 225: }
 226: 
 227: // Перехват паники и их логирование
 228: pub fn setup_panic_handler() {
 229:     std::panic::set_hook(Box::new(|panic_info| {
 230:         let backtrace = std::backtrace::Backtrace::capture();
 231:         let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
 232:         let panic_message = match panic_info.payload().downcast_ref::<&str>() {
 233:             Some(s) => *s,
 234:             None => "Unknown panic",
 235:         };
 236:         
 237:         let location = panic_info.location()
 238:             .map(|l| format!("{}:{}", l.file(), l.line()))
 239:             .unwrap_or_else(|| "unknown".to_string());
 240:             
 241:         error!("PANIC at {}: {} - {}\n{:?}", location, timestamp, panic_message, backtrace);
 242:     }));
 243: }
 244: ```
 245: 
 246: **Шаг 3: Интеграция логгера в основное приложение**
 247: В файле `src/main.rs`:
 248: 
 249: ```rust
 250: mod logging;
 251: 
 252: use actix_web::{App, HttpServer};
 253: use std::io;
 254: 
 255: #[actix_web::main]
 256: async fn main() -> io::Result<()> {
 257:     // Инициализация логгера
 258:     logging::init_logger("./logs")?;
 259:     logging::setup_panic_handler();
 260:     
 261:     println!("Backend server starting up...");
 262:     
 263:     // Остальной код main функции
 264:     HttpServer::new(move || {
 265:         App::new()
 266:             .wrap(logging::get_logger())
 267:             // Остальные middleware и сервисы
 268:     })
 269:     .bind("0.0.0.0:8080")?
 270:     .run()
 271:     .await
 272: }
 273: ```
 274: 
 275: ### 1.3 Настройка ротации логов
 276: 
 277: **Шаг 1: Настройка logrotate для системы**
 278: Создайте файл `/etc/logrotate.d/quer-calc`:
 279: 
 280: ```
 281: /path/to/project/logs/*.log {
 282:     daily
 283:     missingok
 284:     rotate 7
 285:     compress
 286:     delaycompress
 287:     notifempty
 288:     create 0640 www-data www-data
 289:     sharedscripts
 290:     postrotate
 291:         systemctl reload quer-calc-next >/dev/null 2>&1 || true
 292:         systemctl reload quer-calc-backend >/dev/null 2>&1 || true
 293:     endscript
 294: }
 295: 
 296: /path/to/project/backend-rust/logs/*.log {
 297:     daily
 298:     missingok
 299:     rotate 7
 300:     compress
 301:     delaycompress
 302:     notifempty
 303:     create 0640 www-data www-data
 304:     sharedscripts
 305:     postrotate
 306:         systemctl reload quer-calc-backend >/dev/null 2>&1 || true
 307:     endscript
 308: }
 309: ```
 310: 
 311: **Шаг 2: Проверка конфигурации logrotate**
 312: ```bash
 313: sudo logrotate -d /etc/logrotate.d/quer-calc
 314: ```
 315: 
 316: **Шаг 3: Принудительный запуск ротации (для тестирования)**
 317: ```bash
 318: sudo logrotate -f /etc/logrotate.d/quer-calc
 319: ```
 320: 
 321: **Шаг 4: Настройка скрипта для мониторинга размера логов**
 322: Создайте файл `/usr/local/bin/check-logs-size.sh`:
 323: 
 324: ```bash
 325: #!/bin/bash
 326: 
 327: LOGS_DIR="/path/to/project/logs"
 328: BACKEND_LOGS_DIR="/path/to/project/backend-rust/logs"
 329: MAX_SIZE_MB=500
 330: 
 331: # Функция для проверки размера директории
 332: check_dir_size() {
 333:   local dir=$1
 334:   local size=$(du -sm "$dir" | cut -f1)
 335:   
 336:   if [ "$size" -gt "$MAX_SIZE_MB" ]; then
 337:     echo "WARNING: Log directory $dir is larger than ${MAX_SIZE_MB}MB ($size MB)"
 338:     
 339:     # Дополнительно можно добавить отправку уведомления
 340:     # telegram-send "WARNING: Log directory $dir is larger than ${MAX_SIZE_MB}MB ($size MB)"
 341:     
 342:     # Запуск принудительной ротации
 343:     logrotate -f /etc/logrotate.d/quer-calc
 344:   fi
 345: }
 346: 
 347: check_dir_size "$LOGS_DIR"
 348: check_dir_size "$BACKEND_LOGS_DIR"
 349: ```
 350: 
 351: **Шаг 5: Назначение прав и добавление в cron**
 352: ```bash
 353: sudo chmod +x /usr/local/bin/check-logs-size.sh
 354: sudo crontab -e
 355: ```
 356: 
 357: Добавьте строку:
 358: ```
 359: 0 */4 * * * /usr/local/bin/check-logs-size.sh
 360: ```
 361: 
 362: ## 2. Настройка мониторинга
 363: 
 364: ### 2.1 Установка простого мониторинга (Netdata)
 365: 
 366: **Шаг 1: Установка Netdata**
 367: ```bash
 368: bash <(curl -Ss https://get.netdata.cloud/kickstart.sh)
 369: ```
 370: 
 371: **Шаг 2: Проверка установки**
 372: ```bash
 373: systemctl status netdata
 374: ```
 375: 
 376: **Шаг 3: Настройка доступа к панели Netdata**
 377: Откройте файл `/etc/netdata/netdata.conf`:
 378: 
 379: ```ini
 380: [web]
 381:     allow connections from = localhost 192.168.0.* 10.0.0.*
 382:     allow dashboard from = localhost 192.168.0.* 10.0.0.*
 383: ```
 384: 
 385: Чтобы защитить доступ к Netdata, установите nginx как прокси с базовой аутентификацией:
 386: 
 387: ```bash
 388: sudo apt-get install apache2-utils
 389: sudo htpasswd -c /etc/nginx/.htpasswd admin
 390: ```
 391: 
 392: Настройте nginx:
 393: 
 394: ```nginx
 395: server {
 396:     listen 80;
 397:     server_name monitor.yourdomain.com;
 398:     
 399:     return 301 https://$host$request_uri;
 400: }
 401: 
 402: server {
 403:     listen 443 ssl;
 404:     server_name monitor.yourdomain.com;
 405:     
 406:     ssl_certificate /etc/letsencrypt/live/monitor.yourdomain.com/fullchain.pem;
 407:     ssl_certificate_key /etc/letsencrypt/live/monitor.yourdomain.com/privkey.pem;
 408:     ssl_protocols TLSv1.2 TLSv1.3;
 409:     
 410:     auth_basic "Netdata Access";
 411:     auth_basic_user_file /etc/nginx/.htpasswd;
 412:     
 413:     location / {
 414:         proxy_pass http://localhost:19999;
 415:         proxy_set_header Host $host;
 416:         proxy_set_header X-Real-IP $remote_addr;
 417:         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
 418:         proxy_set_header X-Forwarded-Proto $scheme;
 419:     }
 420: }
 421: ```
 422: 
 423: **Шаг 4: Настройка мониторинга приложения**
 424: Добавьте в `/etc/netdata/python.d/web_log.conf` для мониторинга логов nginx:
 425: 
 426: ```yaml
 427: nginx_log:
 428:   name: 'nginx'
 429:   path: '/var/log/nginx/access.log'
 430: ```
 431: 
 432: **Шаг 5: Рестарт Netdata**
 433: ```bash
 434: sudo systemctl restart netdata
 435: ```
 436: 
 437: ### 2.2 Настройка оповещений через Telegram
 438: 
 439: **Шаг 1: Создание Telegram бота через BotFather**
 440: 1. Откройте Telegram, найдите @BotFather
 441: 2. Отправьте команду `/newbot`
 442: 3. Следуйте инструкциям и получите API токен
 443: 4. Создайте группу и добавьте бота в нее
 444: 5. Узнайте ID группы с помощью бота @RawDataBot
 445: 
 446: **Шаг 2: Настройка скрипта для отправки уведомлений**
 447: Создайте файл `/usr/local/bin/telegram-notify.sh`:
 448: 
 449: ```bash
 450: #!/bin/bash
 451: 
 452: TELEGRAM_BOT_TOKEN="YOUR_BOT_TOKEN"
 453: TELEGRAM_CHAT_ID="YOUR_CHAT_ID"
 454: 
 455: send_message() {
 456:     local message="$1"
 457:     curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
 458:          -d chat_id="${TELEGRAM_CHAT_ID}" \
 459:          -d text="${message}" \
 460:          -d parse_mode="HTML"
 461: }
 462: 
 463: # Если скрипт вызван напрямую
 464: if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
 465:     # Проверяем, передано ли сообщение как аргумент
 466:     if [[ $# -eq 0 ]]; then
 467:         echo "Usage: $0 'Your message'"
 468:         exit 1
 469:     fi
 470:     
 471:     # Отправляем сообщение
 472:     send_message "$1"
 473: fi
 474: ```
 475: 
 476: **Шаг 3: Сделать скрипт исполняемым**
 477: ```bash
 478: sudo chmod +x /usr/local/bin/telegram-notify.sh
 479: ```
 480: 
 481: **Шаг 4: Тестирование оповещений**
 482: ```bash
 483: /usr/local/bin/telegram-notify.sh "Тестовое сообщение системы мониторинга"
 484: ```
 485: 
 486: **Шаг 5: Интеграция с Netdata**
 487: Создайте файл `/etc/netdata/health_alarm_notify.conf`:
 488: 
 489: ```
 490: # Включаем уведомления для Telegram
 491: SEND_TELEGRAM="YES"
 492: 
 493: # Токен бота
 494: TELEGRAM_BOT_TOKEN="YOUR_BOT_TOKEN"
 495: 
 496: # ID чата
 497: DEFAULT_RECIPIENT_TELEGRAM="YOUR_CHAT_ID"
 498: 
 499: # Формат сообщений
 500: TELEGRAM_MESSAGE_PLAINTEXT="${status} ${host} - ${chart} - ${info}"
 501: ```
 502: 
 503: ### 2.3 Мониторинг использования ресурсов и производительности
 504: 
 505: **Шаг 1: Настройка аппаратного мониторинга в Netdata**
 506: Создайте файл `/etc/netdata/health.d/cpu_usage.conf`:
 507: 
 508: ```
 509: template: cpu_usage
 510:       on: system.cpu
 511:     calc: $user + $system
 512:    every: 1m
 513:     warn: $this > (($status >= $WARNING) ? (75) : (85))
 514:     crit: $this > (($status >= $CRITICAL) ? (85) : (95))
 515:    units: %
 516:     info: CPU utilization
 517:       to: sysadmin
 518: ```
 519: 
 520: Создайте файл `/etc/netdata/health.d/ram_usage.conf`:
 521: 
 522: ```
 523: template: ram_usage
 524:       on: system.ram
 525:     calc: $used * 100 / ($used + $free)
 526:    every: 1m
 527:     warn: $this > (($status >= $WARNING) ? (80) : (90))
 528:     crit: $this > (($status >= $CRITICAL) ? (90) : (95))
 529:    units: %
 530:     info: RAM utilization
 531:       to: sysadmin
 532: ```
 533: 
 534: Создайте файл `/etc/netdata/health.d/disk_usage.conf`:
 535: 
 536: ```
 537: template: disk_usage
 538:       on: disk.space
 539:     calc: $used * 100 / ($used + $avail)
 540:    every: 1m
 541:     warn: $this > (($status >= $WARNING) ? (80) : (90))
 542:     crit: $this > (($status >= $CRITICAL) ? (90) : (95))
 543:    units: %
 544:     info: Disk space utilization
 545:       to: sysadmin
 546: ```
 547: 
 548: **Шаг 2: Мониторинг открытых файлов и сетевых соединений**
 549: Добавьте в crontab:
 550: 
 551: ```
 552: */5 * * * * /usr/bin/lsof | wc -l | awk '{print "Open files: " $1}' | /usr/local/bin/telegram-notify.sh
 553: */10 * * * * netstat -an | grep -c ESTABLISHED | awk '{print "Active connections: " $1}' > /var/log/connections.log
 554: ```
 555: 
 556: **Шаг 3: Мониторинг журналов приложения**
 557: Создайте файл `/usr/local/bin/check-app-errors.sh`:
 558: 
 559: ```bash
 560: #!/bin/bash
 561: 
 562: ERROR_THRESHOLD=10
 563: LOG_FILE="/path/to/project/logs/error.log"
 564: RUST_LOG_FILE="/path/to/project/backend-rust/logs/error.log"
 565: 
 566: # Подсчет ошибок за последний час в Next.js приложении
 567: next_errors=$(grep -c "ERROR" $LOG_FILE | tail -n 1)
 568: 
 569: # Подсчет ошибок за последний час в Rust бэкенде
 570: rust_errors=$(grep -c "ERROR" $RUST_LOG_FILE | tail -n 1)
 571: 
 572: # Проверка превышения порога
 573: if [ "$next_errors" -gt "$ERROR_THRESHOLD" ] || [ "$rust_errors" -gt "$ERROR_THRESHOLD" ]; then
 574:     message="ВНИМАНИЕ: Высокий уровень ошибок в приложении!
 575: - Next.js: $next_errors ошибок
 576: - Rust: $rust_errors ошибок
 577: Проверьте логи для дополнительной информации."
 578:     
 579:     /usr/local/bin/telegram-notify.sh "$message"
 580: fi
 581: ```
 582: 
 583: Добавьте скрипт в crontab:
 584: ```
 585: */10 * * * * /usr/local/bin/check-app-errors.sh
 586: ```
 587: 
 588: ## 3. Мониторинг доступности
 589: 
 590: ### 3.1 Настройка регулярных проверок доступности
 591: 
 592: **Шаг 1: Создание эндпоинтов для проверки состояния**
 593: В Next.js приложении создайте файл `src/app/api/health/route.js`:
 594: 
 595: ```javascript
 596: import { NextResponse } from 'next/server';
 597: import logger from '../../../utils/logger';
 598: 
 599: export async function GET() {
 600:   try {
 601:     // Здесь можно добавить проверки внешних зависимостей
 602:     // например, базы данных или API
 603:     const status = {
 604:       status: 'ok',
 605:       timestamp: new Date().toISOString(),
 606:       uptime: process.uptime(),
 607:       version: process.env.APP_VERSION || 'unknown'
 608:     };
 609:     
 610:     return NextResponse.json(status);
 611:   } catch (error) {
 612:     logger.error('Health check failed', { error: error.toString() });
 613:     return NextResponse.json(
 614:       { status: 'error', message: error.message },
 615:       { status: 500 }
 616:     );
 617:   }
 618: }
 619: ```
 620: 
 621: В Rust бэкенде:
 622: 
 623: ```rust
 624: use actix_web::{web, HttpResponse, Responder};
 625: use serde_json::json;
 626: use std::time::{SystemTime, UNIX_EPOCH};
 627: use std::env;
 628: 
 629: pub async fn health_check() -> impl Responder {
 630:     let start = SystemTime::now();
 631:     let since_epoch = start
 632:         .duration_since(UNIX_EPOCH)
 633:         .expect("Time went backwards");
 634:         
 635:     HttpResponse::Ok().json(json!({
 636:         "status": "ok",
 637:         "timestamp": since_epoch.as_secs(),
 638:         "version": env::var("APP_VERSION").unwrap_or_else(|_| "unknown".to_string())
 639:     }))
 640: }
 641: ```
 642: 
 643: Добавьте в `src/main.rs`:
 644: 
 645: ```rust
 646: app.service(
 647:     web::scope("/api")
 648:         .route("/health", web::get().to(health_check))
 649:         // другие маршруты
 650: )
 651: ```
 652: 
 653: **Шаг 2: Настройка скрипта проверки доступности**
 654: Создайте файл `/usr/local/bin/check-availability.sh`:
 655: 
 656: ```bash
 657: #!/bin/bash
 658: 
 659: # Настройки
 660: FRONTEND_URL="https://yourdomain.com/api/health"
 661: BACKEND_URL="https://yourdomain.com/api/calculate"
 662: NOTIFICATION_SCRIPT="/usr/local/bin/telegram-notify.sh"
 663: LOG_FILE="/var/log/availability-checks.log"
 664: TIMEOUT=10
 665: 
 666: # Проверка доступности с измерением времени отклика
 667: check_endpoint() {
 668:     local url=$1
 669:     local start_time=$(date +%s.%N)
 670:     local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url")
 671:     local end_time=$(date +%s.%N)
 672:     local response_time=$(echo "$end_time - $start_time" | bc)
 673:     
 674:     # Логирование
 675:     echo "$(date +'%Y-%m-%d %H:%M:%S') - $url - HTTP: $http_code - Time: ${response_time}s" >> $LOG_FILE
 676:     
 677:     # Проверка статус-кода
 678:     if [ "$http_code" != "200" ]; then
 679:         $NOTIFICATION_SCRIPT "❌ Сервис недоступен: $url возвращает код $http_code"
 680:         return 1
 681:     fi
 682:     
 683:     # Проверка времени отклика
 684:     if (( $(echo "$response_time > 5.0" | bc -l) )); then
 685:         $NOTIFICATION_SCRIPT "⚠️ Медленный отклик: $url - ${response_time}s"
 686:     fi
 687:     
 688:     return 0
 689: }
 690: 
 691: # Проверка frontend
 692: check_endpoint "$FRONTEND_URL"
 693: frontend_status=$?
 694: 
 695: # Проверка backend
 696: check_endpoint "$BACKEND_URL"
 697: backend_status=$?
 698: 
 699: # Если оба сервиса недоступны, отправляем дополнительное уведомление
 700: if [ $frontend_status -ne 0 ] && [ $backend_status -ne 0 ]; then
 701:     $NOTIFICATION_SCRIPT "🔥 КРИТИЧНО: Оба сервиса (frontend и backend) недоступны!"
 702: fi
 703: ```
 704: 
 705: **Шаг 3: Добавление в crontab**
 706: ```bash
 707: chmod +x /usr/local/bin/check-availability.sh
 708: crontab -e
 709: ```
 710: 
 711: Добавьте:
 712: ```
 713: */5 * * * * /usr/local/bin/check-availability.sh
 714: ```
 715: 
 716: ### 3.2 Мониторинг времени отклика эндпоинтов API
 717: 
 718: **Шаг 1: Создание скрипта для мониторинга API**
 719: Создайте файл `/usr/local/bin/check-api-performance.sh`:
 720: 
 721: ```bash
 722: #!/bin/bash
 723: 
 724: # Настройки
 725: API_CALCULATE_URL="https://yourdomain.com/api/calculate"
 726: API_HEALTH_URL="https://yourdomain.com/api/health"
 727: LOG_FILE="/var/log/api-performance.log"
 728: ALERT_THRESHOLD=2.0  # секунд
 729: TIMEOUT=15
 730: 
 731: # Функция для тестирования API
 732: test_api() {
 733:     local endpoint=$1
 734:     local payload=$2
 735:     local method=${3:-"GET"}
 736:     
 737:     local start_time=$(date +%s.%N)
 738:     
 739:     if [ "$method" == "POST" ]; then
 740:         response=$(curl -s -X POST -H "Content-Type: application/json" -d "$payload" --max-time $TIMEOUT "$endpoint")
 741:         status=$?
 742:     else
 743:         response=$(curl -s --max-time $TIMEOUT "$endpoint")
 744:         status=$?
 745:     fi
 746:     
 747:     local end_time=$(date +%s.%N)
 748:     local response_time=$(echo "$end_time - $start_time" | bc)
 749:     
 750:     # Логирование
 751:     echo "$(date +'%Y-%m-%d %H:%M:%S') - $method $endpoint - Time: ${response_time}s - Status: $status" >> $LOG_FILE
 752:     
 753:     # Если запрос не выполнен успешно
 754:     if [ $status -ne 0 ]; then
 755:         /usr/local/bin/telegram-notify.sh "❌ API недоступен: $endpoint (ошибка curl: $status)"
 756:         return 1
 757:     fi
 758:     
 759:     # Если время ответа превышает порог
 760:     if (( $(echo "$response_time > $ALERT_THRESHOLD" | bc -l) )); then
 761:         /usr/local/bin/telegram-notify.sh "⚠️ Медленный API: $endpoint - ${response_time}s"
 762:     fi
 763:     
 764:     # Возвращаем время ответа для анализа
 765:     echo "$response_time"
 766: }
 767: 
 768: # Тестируем API health
 769: health_time=$(test_api "$API_HEALTH_URL")
 770: 
 771: # Тестируем API calculate с тестовыми данными
 772: calculate_payload='{"entryPrice":100,"stopLoss":90,"risk":1,"riskUnit":"percent","accountBalance":10000,"nominalRR":2,"maxLeverage":10,"entryFee":0.1,"exitFee":0.1,"maintenanceMargin":0.5,"safetyBuffer":2}'
 773: calculate_time=$(test_api "$API_CALCULATE_URL" "$calculate_payload" "POST")
 774: 
 775: # Сохраняем данные для построения графиков
 776: echo "$(date +'%s'),health,$health_time" >> /var/log/api-performance-data.csv
 777: echo "$(date +'%s'),calculate,$calculate_time" >> /var/log/api-performance-data.csv
 778: ```
 779: 
 780: **Шаг 2: Настройка графиков в Netdata**
 781: Создайте модуль для анализа логов API:
 782: 
 783: ```bash
 784: cat > /etc/netdata/python.d/custom_api_logs.conf << EOL
 785: api_performance:
 786:   name: 'API Performance'
 787:   path: '/var/log/api-performance-data.csv'
 788:   update_every: 60
 789:   autodetection_retry: 1
 790: EOL
 791: ```
 792: 
 793: **Шаг 3: Автоматизация через crontab**
 794: ```bash
 795: chmod +x /usr/local/bin/check-api-performance.sh
 796: crontab -e
 797: ```
 798: 
 799: Добавьте:
 800: ```
 801: */10 * * * * /usr/local/bin/check-api-performance.sh
 802: ```
 803: 
 804: ### 3.3 Мониторинг SSL-сертификатов и их сроков действия
 805: 
 806: **Шаг 1: Создание скрипта для проверки сертификатов**
 807: Создайте файл `/usr/local/bin/check-ssl-certs.sh`:
 808: 
 809: ```bash
 810: #!/bin/bash
 811: 
 812: # Настройки
 813: DOMAINS=("yourdomain.com" "api.yourdomain.com" "monitor.yourdomain.com")
 814: DAYS_WARNING=14
 815: LOG_FILE="/var/log/ssl-checks.log"
 816: 
 817: # Проверка сертификата
 818: check_ssl() {
 819:     local domain=$1
 820:     
 821:     echo "Проверка $domain..." >> $LOG_FILE
 822:     
 823:     # Получение данных сертификата
 824:     cert_info=$(timeout 10 openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -enddate -subject -issuer)
 825:     
 826:     if [ $? -ne 0 ]; then
 827:         echo "Ошибка получения данных сертификата для $domain" >> $LOG_FILE
 828:         /usr/local/bin/telegram-notify.sh "❌ Ошибка проверки SSL для $domain"
 829:         return 1
 830:     fi
 831:     
 832:     # Извлечение даты истечения
 833:     end_date=$(echo "$cert_info" | grep 'notAfter=' | cut -d'=' -f2)
 834:     
 835:     # Преобразование в формат Unix timestamp
 836:     end_timestamp=$(date -d "$end_date" +%s)
 837:     current_timestamp=$(date +%s)
 838:     
 839:     # Расчет оставшихся дней
 840:     days_left=$(( ($end_timestamp - $current_timestamp) / 86400 ))
 841:     
 842:     # Логирование результатов
 843:     echo "Домен: $domain, Срок действия: $end_date, Осталось дней: $days_left" >> $LOG_FILE
 844:     
 845:     # Проверка срока действия
 846:     if [ $days_left -le 0 ]; then
 847:         /usr/local/bin/telegram-notify.sh "🔴 КРИТИЧНО: SSL-сертификат для $domain ИСТЕК!"
 848:         return 2
 849:     elif [ $days_left -le $DAYS_WARNING ]; then
 850:         /usr/local/bin/telegram-notify.sh "🟠 ВНИМАНИЕ: SSL-сертификат для $domain истекает через $days_left дней!"
 851:         return 3
 852:     fi
 853:     
 854:     return 0
 855: }
 856: 
 857: # Проверка всех доменов
 858: echo "=== Проверка SSL-сертификатов $(date) ===" >> $LOG_FILE
 859: 
 860: for domain in "${DOMAINS[@]}"; do
 861:     check_ssl $domain
 862: done
 863: 
 864: echo "Проверка завершена" >> $LOG_FILE
 865: echo "" >> $LOG_FILE
 866: ```
 867: 
 868: **Шаг 2: Настройка автоматического обновления Let's Encrypt**
 869: ```bash
 870: sudo crontab -e
 871: ```
 872: 
 873: Добавьте:
 874: ```
 875: 0 3 * * * /usr/bin/certbot renew --quiet --post-hook "systemctl reload nginx"
 876: ```
 877: 
 878: **Шаг 3: Добавление проверки SSL в crontab**
 879: ```bash
 880: chmod +x /usr/local/bin/check-ssl-certs.sh
 881: crontab -e
 882: ```
 883: 
 884: Добавьте:
 885: ```
 886: 0 9 * * * /usr/local/bin/check-ssl-certs.sh
 887: ```
 888: 
 889: ## 4. Интеграция и Тестирование
 890: 
 891: ### 4.1 Тестирование системы логирования
 892: 
 893: **Шаг 1: Тестирование Frontend логирования**
 894: ```bash
 895: # Проверка записи логов
 896: tail -f /path/to/project/logs/combined.log
 897: 
 898: # Генерация тестовой ошибки
 899: curl -v http://localhost:3000/api/non-existent-endpoint
 900: ```
 901: 
 902: **Шаг 2: Тестирование Backend логирования**
 903: ```bash
 904: # Проверка записи логов
 905: tail -f /path/to/project/backend-rust/logs/info.log
 906: 
 907: # Генерация тестовой ошибки
 908: curl -v -X POST http://localhost:8080/api/calculate -H "Content-Type: application/json" -d '{}'
 909: ```
 910: 
 911: **Шаг 3: Тестирование ротации логов**
 912: ```bash
 913: # Ручной запуск logrotate для тестирования
 914: sudo logrotate -f /etc/logrotate.d/quer-calc
 915: 
 916: # Проверка создания новых файлов логов
 917: ls -la /path/to/project/logs/
 918: ```
 919: 
 920: ### 4.2 Тестирование системы мониторинга
 921: 
 922: **Шаг 1: Тестирование Netdata**
 923: ```bash
 924: # Проверка статуса сервиса
 925: systemctl status netdata
 926: 
 927: # Проверка доступности веб-интерфейса
 928: curl -v http://localhost:19999
 929: ```
 930: 
 931: **Шаг 2: Тестирование оповещений Telegram**
 932: ```bash
 933: # Отправка тестового уведомления
 934: /usr/local/bin/telegram-notify.sh "Тестовое сообщение системы мониторинга"
 935: 
 936: # Проверка настроек алармов Netdata
 937: sudo netdatacli reload-health
 938: sudo netdatacli health
 939: ```
 940: 
 941: **Шаг 3: Тестирование мониторинга доступности**
 942: ```bash
 943: # Ручной запуск проверки доступности
 944: /usr/local/bin/check-availability.sh
 945: 
 946: # Проверка логов
 947: tail -f /var/log/availability-checks.log
 948: ```
 949: 
 950: **Шаг 4: Тестирование мониторинга SSL**
 951: ```bash
 952: # Ручной запуск проверки сертификатов
 953: /usr/local/bin/check-ssl-certs.sh
 954: 
 955: # Проверка логов
 956: tail -f /var/log/ssl-checks.log
 957: ```
 958: 
 959: ## 5. Документация и обслуживание
 960: 
 961: ### 5.1 Документация настроек
 962: 
 963: **Шаг 1: Создание документации мониторинга**
 964: Создайте файл `/path/to/project/docs/monitoring.md`:
 965: 
 966: ```markdown
 967: # Документация по мониторингу и логированию
 968: 
 969: ## Логирование
 970: 
 971: ### Frontend (Next.js)
 972: - **Директория логов:** `/path/to/project/logs/`
 973: - **Основные файлы:**
 974:   - `combined.log` - все логи
 975:   - `error.log` - только ошибки
 976:   - `exceptions.log` - необработанные исключения
 977: 
 978: ### Backend (Rust)
 979: - **Директория логов:** `/path/to/project/backend-rust/logs/`
 980: - **Основные файлы:**
 981:   - `info.log` - информационные сообщения
 982:   - `error.log` - ошибки и критические события
 983: 
 984: ### Ротация логов
 985: - Настроена ежедневная ротация с сохранением 7 последних архивов
 986: - Логи старше 7 дней автоматически удаляются
 987: - Ручной запуск: `sudo logrotate -f /etc/logrotate.d/quer-calc`
 988: 
 989: ## Мониторинг
 990: 
 991: ### Netdata
 992: - **Адрес панели:** `https://monitor.yourdomain.com`
 993: - **Учетные данные:** обратитесь к администратору
 994: - **Настройки алармов:** `/etc/netdata/health.d/`
 995: 
 996: ### Оповещения
 997: - Настроены уведомления через Telegram
 998: - Группа мониторинга: "Quer-Calc Мониторинг"
 999: - Скрипт отправки: `/usr/local/bin/telegram-notify.sh`
1000: 
1001: ### Проверки доступности
1002: - Проверка каждые 5 минут
1003: - Эндпоинты:
1004:   - `https://yourdomain.com/api/health`
1005:   - `https://yourdomain.com/api/calculate`
1006: - Логи: `/var/log/availability-checks.log`
1007: 
1008: ### SSL-мониторинг
1009: - Ежедневная проверка сертификатов
1010: - Уведомление за 14 дней до истечения срока
1011: - Автоматическое обновление с Let's Encrypt
1012: ```
1013: 
1014: ### 5.2 Настройка автоматических проверок
1015: 
1016: **Шаг 1: Скрипт общей проверки системы мониторинга**
1017: Создайте файл `/usr/local/bin/monitoring-healthcheck.sh`:
1018: 
1019: ```bash
1020: #!/bin/bash
1021: 
1022: # Проверка работы служб логирования и мониторинга
1023: check_service() {
1024:     local service=$1
1025:     if ! systemctl is-active --quiet $service; then
1026:         /usr/local/bin/telegram-notify.sh "⚠️ Сервис $service не работает!"
1027:         systemctl restart $service
1028:         echo "$(date) - Перезапуск службы $service" >> /var/log/monitoring-healthcheck.log
1029:     fi
1030: }
1031: 
1032: # Проверка размера лог-файлов
1033: check_log_size() {
1034:     local log_file=$1
1035:     local max_size_mb=$2
1036:     
1037:     if [ -f "$log_file" ]; then
1038:         size_mb=$(du -m "$log_file" | cut -f1)
1039:         if [ "$size_mb" -gt "$max_size_mb" ]; then
1040:             /usr/local/bin/telegram-notify.sh "⚠️ Лог-файл $log_file превысил $max_size_mb MB (текущий размер: $size_mb MB)"
1041:         fi
1042:     fi
1043: }
1044: 
1045: # Проверка дискового пространства
1046: check_disk_space() {
1047:     local mount_point=$1
1048:     local threshold=$2
1049:     
1050:     usage=$(df -h "$mount_point" | awk 'NR==2 {print $5}' | sed 's/%//')
1051:     if [ "$usage" -gt "$threshold" ]; then
1052:         /usr/local/bin/telegram-notify.sh "⚠️ Недостаточно места на диске $mount_point: использовано $usage%"
1053:     fi
1054: }
1055: 
1056: # Проверка служб
1057: check_service "netdata"
1058: check_service "nginx"
1059: 
1060: # Проверка лог-файлов
1061: check_log_size "/path/to/project/logs/combined.log" 100
1062: check_log_size "/path/to/project/logs/error.log" 50
1063: check_log_size "/path/to/project/backend-rust/logs/info.log" 100
1064: check_log_size "/path/to/project/backend-rust/logs/error.log" 50
1065: 
1066: # Проверка дискового пространства
1067: check_disk_space "/" 80
1068: ```
1069: 
1070: **Шаг 2: Добавление в crontab**
1071: ```bash
1072: chmod +x /usr/local/bin/monitoring-healthcheck.sh
1073: crontab -e
1074: ```
1075: 
1076: Добавьте:
1077: ```
1078: 0 */6 * * * /usr/local/bin/monitoring-healthcheck.sh
1079: ```
1080: 
1081: ## 6. Оптимизация и масштабирование
1082: 
1083: ### 6.1 Оптимизация хранения логов
1084: 
1085: **Шаг 1: Настройка сжатия и архивации старых логов**
1086: Создайте файл `/usr/local/bin/archive-old-logs.sh`:
1087: 
1088: ```bash
1089: #!/bin/bash
1090: 
1091: LOGS_DIR="/path/to/project/logs"
1092: BACKEND_LOGS_DIR="/path/to/project/backend-rust/logs"
1093: ARCHIVE_DIR="/path/to/archive/logs"
1094: DAYS_TO_KEEP=30
1095: 
1096: # Создание архивной директории
1097: mkdir -p "$ARCHIVE_DIR"
1098: 
1099: # Функция архивации
1100: archive_old_logs() {
1101:     local source_dir=$1
1102:     local name=$2
1103:     
1104:     # Находим и архивируем старые логи
1105:     find "$source_dir" -name "*.log.*" -type f -mtime +7 | while read log_file; do
1106:         base_name=$(basename "$log_file")
1107:         dated_name="${name}-$(date +%Y%m%d)-${base_name}"
1108:         gzip -c "$log_file" > "${ARCHIVE_DIR}/${dated_name}.gz"
1109:         rm "$log_file"
1110:         echo "Архивирован: $log_file -> ${ARCHIVE_DIR}/${dated_name}.gz"
1111:     done
1112:     
1113:     # Удаляем очень старые архивы
1114:     find "$ARCHIVE_DIR" -name "${name}-*.gz" -type f -mtime +$DAYS_TO_KEEP -delete
1115: }
1116: 
1117: # Архивация логов
1118: archive_old_logs "$LOGS_DIR" "frontend"
1119: archive_old_logs "$BACKEND_LOGS_DIR" "backend"
1120: ```
1121: 
1122: **Шаг 2: Добавление в crontab**
1123: ```bash
1124: chmod +x /usr/local/bin/archive-old-logs.sh
1125: crontab -e
1126: ```
1127: 
1128: Добавьте:
1129: ```
1130: 0 2 * * 0 /usr/local/bin/archive-old-logs.sh
1131: ```
1132: 
1133: ### 6.2 Масштабирование мониторинга
1134: 
1135: **Шаг 1: Настройка агрегации логов для нескольких инстансов**
1136: Создайте файл `/etc/rsyslog.d/10-quer-calc.conf`:
1137: 
1138: ```
1139: # Шаблон для логов приложения
1140: template(name="QuerCalcLogs" type="string" string="/var/log/quer-calc/%PROGRAMNAME%.log")
1141: 
1142: # Правила для Next.js
1143: if $programname startswith 'quer-calc-next' then {
1144:     action(type="omfile" DynaFile="QuerCalcLogs")
1145:     stop
1146: }
1147: 
1148: # Правила для Rust бэкенда
1149: if $programname startswith 'quer-calc-backend' then {
1150:     action(type="omfile" DynaFile="QuerCalcLogs")
1151:     stop
1152: }
1153: ```
1154: 
1155: **Шаг 2: Настройка агрегации метрик**
1156: Если в будущем потребуется масштабирование, настройте центральный сервер мониторинга:
1157: 
1158: ```bash
1159: # На центральном сервере мониторинга
1160: mkdir -p /etc/netdata/stream.d/
1161: cat > /etc/netdata/stream.d/stream.conf << EOL
1162: [stream]
1163:   enabled = yes
1164:   api key = 00000000-0000-0000-0000-000000000000
1165:   destination = 127.0.0.1:19999
1166: EOL
1167: 
1168: # На каждом клиентском сервере
1169: mkdir -p /etc/netdata/stream.d/
1170: cat > /etc/netdata/stream.d/stream.conf << EOL
1171: [stream]
1172:   enabled = yes
1173:   api key = 00000000-0000-0000-0000-000000000000
1174:   destination = monitoring-server-ip:19999
1175: EOL
1176: ```
1177: 
1178: **Настройка завершена! В этом документе представлен полный план настройки мониторинга и логирования для вашего проекта Quer Calculator.**
````

## File: План развёртывания приложения и CI:CD/8. backup-maintenance-plan.md
````markdown
   1: # План резервного копирования и обслуживания
   2: 
   3: ## 1. Настройка бэкапов
   4: 
   5: ### 1.1. Резервное копирование базы данных
   6: 
   7: #### Для SQL баз данных (PostgreSQL, MySQL)
   8: 
   9: ```bash
  10: #!/bin/bash
  11: # backup-database.sh
  12: 
  13: # Настройка переменных
  14: DB_USER="имя_пользователя"
  15: DB_NAME="имя_базы_данных"
  16: BACKUP_DIR="/path/to/backups/db"
  17: DATE=$(date +"%Y-%m-%d_%H-%M-%S")
  18: BACKUP_FILE="$BACKUP_DIR/$DB_NAME-$DATE.sql"
  19: RETAIN_DAYS=30
  20: 
  21: # Создание директории для резервных копий, если она не существует
  22: mkdir -p $BACKUP_DIR
  23: 
  24: # Выполнение резервного копирования
  25: if [ "$DB_TYPE" = "postgres" ]; then
  26:     PGPASSWORD="$DB_PASSWORD" pg_dump -U $DB_USER $DB_NAME > $BACKUP_FILE
  27: elif [ "$DB_TYPE" = "mysql" ]; then
  28:     mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_FILE
  29: fi
  30: 
  31: # Сжатие файла резервной копии
  32: gzip $BACKUP_FILE
  33: 
  34: # Удаление старых резервных копий
  35: find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETAIN_DAYS -delete
  36: 
  37: # Логирование результата
  38: echo "Backup completed: $BACKUP_FILE.gz" >> $BACKUP_DIR/backup.log
  39: ```
  40: 
  41: **Настройка cron-задания для ежедневного выполнения:**
  42: 
  43: ```
  44: # Запуск в 3:00 утра каждый день
  45: 0 3 * * * /path/to/backup-database.sh
  46: ```
  47: 
  48: #### Для NoSQL баз данных (MongoDB)
  49: 
  50: ```bash
  51: #!/bin/bash
  52: # backup-mongodb.sh
  53: 
  54: # Настройка переменных
  55: MONGO_URI="*******************************************"
  56: DB_NAME="имя_базы_данных"
  57: BACKUP_DIR="/path/to/backups/mongo"
  58: DATE=$(date +"%Y-%m-%d_%H-%M-%S")
  59: BACKUP_FILE="$BACKUP_DIR/$DB_NAME-$DATE"
  60: RETAIN_DAYS=30
  61: 
  62: # Создание директории для резервных копий, если она не существует
  63: mkdir -p $BACKUP_DIR
  64: 
  65: # Выполнение резервного копирования
  66: mongodump --uri=$MONGO_URI --db=$DB_NAME --out=$BACKUP_FILE
  67: 
  68: # Сжатие файла резервной копии
  69: tar -czf "$BACKUP_FILE.tar.gz" -C $BACKUP_DIR "$DB_NAME-$DATE"
  70: rm -rf $BACKUP_FILE
  71: 
  72: # Удаление старых резервных копий
  73: find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETAIN_DAYS -delete
  74: 
  75: # Логирование результата
  76: echo "Backup completed: $BACKUP_FILE.tar.gz" >> $BACKUP_DIR/backup.log
  77: ```
  78: 
  79: ### 1.2. Бэкапы конфигурационных файлов и переменных окружения
  80: 
  81: ```bash
  82: #!/bin/bash
  83: # backup-configs.sh
  84: 
  85: # Настройка переменных
  86: APP_DIR="/path/to/application"
  87: CONFIG_DIR="$APP_DIR/config"
  88: ENV_FILES=(".env" ".env.production" ".env.local")
  89: BACKUP_DIR="/path/to/backups/configs"
  90: DATE=$(date +"%Y-%m-%d_%H-%M-%S")
  91: BACKUP_FILE="$BACKUP_DIR/configs-$DATE.tar.gz"
  92: RETAIN_DAYS=60
  93: 
  94: # Создание директории для резервных копий, если она не существует
  95: mkdir -p $BACKUP_DIR
  96: 
  97: # Создание временной директории для сбора конфигурационных файлов
  98: TEMP_DIR=$(mktemp -d)
  99: 
 100: # Копирование конфигурационных файлов
 101: if [ -d "$CONFIG_DIR" ]; then
 102:     cp -r $CONFIG_DIR $TEMP_DIR/
 103: fi
 104: 
 105: # Копирование .env файлов
 106: for ENV_FILE in "${ENV_FILES[@]}"; do
 107:     if [ -f "$APP_DIR/$ENV_FILE" ]; then
 108:         cp "$APP_DIR/$ENV_FILE" "$TEMP_DIR/"
 109:     fi
 110: done
 111: 
 112: # Копирование других важных файлов конфигурации
 113: if [ -f "$APP_DIR/next.config.js" ]; then
 114:     cp "$APP_DIR/next.config.js" "$TEMP_DIR/"
 115: fi
 116: 
 117: if [ -f "$APP_DIR/backend-rust/Cargo.toml" ]; then
 118:     cp "$APP_DIR/backend-rust/Cargo.toml" "$TEMP_DIR/"
 119: fi
 120: 
 121: # Создание архива с конфигурационными файлами
 122: tar -czf $BACKUP_FILE -C $TEMP_DIR .
 123: 
 124: # Удаление временной директории
 125: rm -rf $TEMP_DIR
 126: 
 127: # Удаление старых резервных копий
 128: find $BACKUP_DIR -name "configs-*.tar.gz" -mtime +$RETAIN_DAYS -delete
 129: 
 130: # Логирование результата
 131: echo "Config backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
 132: ```
 133: 
 134: **Настройка cron-задания для еженедельного выполнения:**
 135: 
 136: ```
 137: # Запуск в 4:00 утра каждое воскресенье
 138: 0 4 * * 0 /path/to/backup-configs.sh
 139: ```
 140: 
 141: ### 1.3. Резервное копирование пользовательских данных
 142: 
 143: ```bash
 144: #!/bin/bash
 145: # backup-user-data.sh
 146: 
 147: # Настройка переменных
 148: APP_DIR="/path/to/application"
 149: DATA_DIR="$APP_DIR/user_data"
 150: BACKUP_DIR="/path/to/backups/user_data"
 151: DATE=$(date +"%Y-%m-%d_%H-%M-%S")
 152: BACKUP_FILE="$BACKUP_DIR/user_data-$DATE.tar.gz"
 153: RETAIN_DAYS=30
 154: 
 155: # Создание директории для резервных копий, если она не существует
 156: mkdir -p $BACKUP_DIR
 157: 
 158: # Проверка существования директории с пользовательскими данными
 159: if [ ! -d "$DATA_DIR" ]; then
 160:     echo "User data directory not found: $DATA_DIR" >> $BACKUP_DIR/backup.log
 161:     exit 1
 162: fi
 163: 
 164: # Создание архива с пользовательскими данными
 165: tar -czf $BACKUP_FILE -C $APP_DIR user_data
 166: 
 167: # Удаление старых резервных копий
 168: find $BACKUP_DIR -name "user_data-*.tar.gz" -mtime +$RETAIN_DAYS -delete
 169: 
 170: # Логирование результата
 171: echo "User data backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
 172: ```
 173: 
 174: ### 1.4. Резервное копирование всего проекта (полный бэкап)
 175: 
 176: ```bash
 177: #!/bin/bash
 178: # backup-full-project.sh
 179: 
 180: # Настройка переменных
 181: APP_DIR="/path/to/application"
 182: BACKUP_DIR="/path/to/backups/full"
 183: DATE=$(date +"%Y-%m-%d_%H-%M-%S")
 184: BACKUP_FILE="$BACKUP_DIR/full-$DATE.tar.gz"
 185: EXCLUDE_DIRS=("node_modules" "target" ".next" "dist" "backup")
 186: RETAIN_DAYS=90
 187: 
 188: # Создание директории для резервных копий, если она не существует
 189: mkdir -p $BACKUP_DIR
 190: 
 191: # Формирование параметров исключения директорий
 192: EXCLUDE_PARAMS=""
 193: for DIR in "${EXCLUDE_DIRS[@]}"; do
 194:     EXCLUDE_PARAMS="$EXCLUDE_PARAMS --exclude=$DIR"
 195: done
 196: 
 197: # Создание полной резервной копии проекта
 198: tar -czf $BACKUP_FILE $EXCLUDE_PARAMS -C $(dirname $APP_DIR) $(basename $APP_DIR)
 199: 
 200: # Удаление старых резервных копий
 201: find $BACKUP_DIR -name "full-*.tar.gz" -mtime +$RETAIN_DAYS -delete
 202: 
 203: # Логирование результата
 204: echo "Full project backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
 205: ```
 206: 
 207: **Настройка cron-задания для ежемесячного выполнения:**
 208: 
 209: ```
 210: # Запуск в 2:00 утра в первый день каждого месяца
 211: 0 2 1 * * /path/to/backup-full-project.sh
 212: ```
 213: 
 214: ### 1.5. Загрузка резервных копий в облачное хранилище
 215: 
 216: ```bash
 217: #!/bin/bash
 218: # upload-backups-to-cloud.sh
 219: 
 220: # Настройка переменных
 221: BACKUP_DIR="/path/to/backups"
 222: CLOUD_TYPE="s3" # Допустимые значения: s3, gcloud, azure
 223: 
 224: # Проверка наличия свежих резервных копий (созданных в последние 24 часа)
 225: RECENT_BACKUPS=$(find $BACKUP_DIR -type f -name "*.tar.gz" -o -name "*.sql.gz" -mtime -1)
 226: 
 227: if [ -z "$RECENT_BACKUPS" ]; then
 228:     echo "No recent backups found to upload" >> $BACKUP_DIR/cloud-upload.log
 229:     exit 0
 230: fi
 231: 
 232: # Загрузка резервных копий в облачное хранилище
 233: for BACKUP in $RECENT_BACKUPS; do
 234:     BACKUP_FILENAME=$(basename $BACKUP)
 235:     
 236:     case $CLOUD_TYPE in
 237:         s3)
 238:             # Загрузка в AWS S3
 239:             aws s3 cp $BACKUP s3://your-bucket-name/backups/$BACKUP_FILENAME
 240:             RESULT=$?
 241:             ;;
 242:         gcloud)
 243:             # Загрузка в Google Cloud Storage
 244:             gsutil cp $BACKUP gs://your-bucket-name/backups/$BACKUP_FILENAME
 245:             RESULT=$?
 246:             ;;
 247:         azure)
 248:             # Загрузка в Azure Blob Storage
 249:             az storage blob upload --container-name your-container --file $BACKUP --name backups/$BACKUP_FILENAME
 250:             RESULT=$?
 251:             ;;
 252:         *)
 253:             echo "Unknown cloud type: $CLOUD_TYPE" >> $BACKUP_DIR/cloud-upload.log
 254:             exit 1
 255:     esac
 256:     
 257:     if [ $RESULT -eq 0 ]; then
 258:         echo "Successfully uploaded $BACKUP to cloud storage" >> $BACKUP_DIR/cloud-upload.log
 259:     else
 260:         echo "Failed to upload $BACKUP to cloud storage" >> $BACKUP_DIR/cloud-upload.log
 261:     fi
 262: done
 263: ```
 264: 
 265: **Настройка cron-задания для загрузки резервных копий:**
 266: 
 267: ```
 268: # Запуск в 6:00 утра каждый день
 269: 0 6 * * * /path/to/upload-backups-to-cloud.sh
 270: ```
 271: 
 272: ## 2. Автоматизация обслуживания
 273: 
 274: ### 2.1. Скрипты для очистки временных файлов
 275: 
 276: ```bash
 277: #!/bin/bash
 278: # cleanup-temp-files.sh
 279: 
 280: # Настройка переменных
 281: APP_DIR="/path/to/application"
 282: LOG_DIR="$APP_DIR/logs"
 283: TEMP_DIRS=("$APP_DIR/tmp" "$APP_DIR/.next/cache" "$APP_DIR/backend-rust/target/debug")
 284: LOG_RETENTION_DAYS=14
 285: 
 286: # Очистка временных директорий
 287: for DIR in "${TEMP_DIRS[@]}"; do
 288:     if [ -d "$DIR" ]; then
 289:         echo "Cleaning temporary directory: $DIR"
 290:         find "$DIR" -type f -mtime +1 -delete
 291:         find "$DIR" -type d -empty -delete
 292:     fi
 293: done
 294: 
 295: # Очистка старых файлов логов
 296: if [ -d "$LOG_DIR" ]; then
 297:     echo "Cleaning old log files older than $LOG_RETENTION_DAYS days"
 298:     find "$LOG_DIR" -type f -name "*.log" -mtime +$LOG_RETENTION_DAYS -delete
 299: fi
 300: 
 301: # Очистка артефактов сборки, которые не нужны в production
 302: if [ -d "$APP_DIR/.next/cache" ]; then
 303:     echo "Cleaning Next.js build cache"
 304:     rm -rf "$APP_DIR/.next/cache/images"
 305: fi
 306: 
 307: echo "Temporary files cleanup completed at $(date)" >> "$APP_DIR/maintenance.log"
 308: ```
 309: 
 310: **Настройка cron-задания для очистки:**
 311: 
 312: ```
 313: # Запуск в 1:00 утра каждый день
 314: 0 1 * * * /path/to/cleanup-temp-files.sh
 315: ```
 316: 
 317: ### 2.2. Автоматическое обновление зависимостей
 318: 
 319: ```bash
 320: #!/bin/bash
 321: # update-dependencies.sh
 322: 
 323: # Настройка переменных
 324: APP_DIR="/path/to/application"
 325: FRONTEND_DIR="$APP_DIR"
 326: BACKEND_DIR="$APP_DIR/backend-rust"
 327: LOG_FILE="$APP_DIR/updates.log"
 328: UPDATE_BRANCH="dependency-updates-$(date +%Y-%m-%d)"
 329: 
 330: # Функция для логирования
 331: log() {
 332:     echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> $LOG_FILE
 333: }
 334: 
 335: # Переход в директорию приложения
 336: cd $APP_DIR || { log "Failed to change directory to $APP_DIR"; exit 1; }
 337: 
 338: # Создание новой ветки Git для обновлений
 339: git checkout -b $UPDATE_BRANCH || { log "Failed to create new Git branch"; exit 1; }
 340: 
 341: # Обновление зависимостей фронтенда
 342: log "Updating frontend dependencies..."
 343: cd $FRONTEND_DIR || { log "Failed to change directory to $FRONTEND_DIR"; exit 1; }
 344: 
 345: # Проверка наличия устаревших зависимостей и их обновление
 346: npm outdated --json > outdated.json
 347: if [ -s outdated.json ]; then
 348:     log "Outdated NPM packages found, updating..."
 349:     npm update || { log "Failed to update NPM packages"; exit 1; }
 350:     log "NPM packages updated successfully"
 351: else
 352:     log "No outdated NPM packages found"
 353: fi
 354: rm outdated.json
 355: 
 356: # Обновление зависимостей бэкенда
 357: log "Updating backend dependencies..."
 358: cd $BACKEND_DIR || { log "Failed to change directory to $BACKEND_DIR"; exit 1; }
 359: 
 360: # Обновление Cargo.toml с помощью cargo-edit
 361: if command -v cargo-edit >/dev/null 2>&1; then
 362:     cargo update || { log "Failed to update Rust dependencies"; exit 1; }
 363:     log "Rust dependencies updated successfully"
 364: else
 365:     log "cargo-edit not found, install it with: cargo install cargo-edit"
 366: fi
 367: 
 368: # Возврат в директорию приложения
 369: cd $APP_DIR || { log "Failed to change directory to $APP_DIR"; exit 1; }
 370: 
 371: # Запуск тестов, чтобы проверить работоспособность после обновления
 372: log "Running tests after dependency updates..."
 373: 
 374: # Тесты фронтенда
 375: cd $FRONTEND_DIR
 376: if npm test; then
 377:     log "Frontend tests passed successfully"
 378: else
 379:     log "Frontend tests failed after dependency updates"
 380:     git checkout -- package.json package-lock.json
 381:     log "Reverted frontend dependency updates"
 382: fi
 383: 
 384: # Тесты бэкенда
 385: cd $BACKEND_DIR
 386: if cargo test; then
 387:     log "Backend tests passed successfully"
 388: else
 389:     log "Backend tests failed after dependency updates"
 390:     git checkout -- Cargo.toml Cargo.lock
 391:     log "Reverted backend dependency updates"
 392: fi
 393: 
 394: # Коммит изменений, если есть что коммитить
 395: cd $APP_DIR
 396: if git diff --quiet; then
 397:     log "No changes to commit"
 398: else
 399:     git add .
 400:     git commit -m "Update dependencies (automated)" || { log "Failed to commit changes"; exit 1; }
 401:     log "Changes committed to branch $UPDATE_BRANCH"
 402:     
 403:     # Отправляем изменения в удаленный репозиторий
 404:     # Раскомментируйте для автоматической отправки:
 405:     # git push origin $UPDATE_BRANCH
 406:     # log "Changes pushed to remote repository"
 407:     
 408:     log "Please review the changes in branch $UPDATE_BRANCH and merge them manually"
 409: fi
 410: ```
 411: 
 412: **Настройка cron-задания для еженедельного обновления зависимостей:**
 413: 
 414: ```
 415: # Запуск в 3:00 утра каждую субботу
 416: 0 3 * * 6 /path/to/update-dependencies.sh
 417: ```
 418: 
 419: ### 2.3. Периодическая проверка на уязвимости
 420: 
 421: ```bash
 422: #!/bin/bash
 423: # security-scan.sh
 424: 
 425: # Настройка переменных
 426: APP_DIR="/path/to/application"
 427: FRONTEND_DIR="$APP_DIR"
 428: BACKEND_DIR="$APP_DIR/backend-rust"
 429: REPORT_DIR="$APP_DIR/security_reports"
 430: DATE=$(date +"%Y-%m-%d")
 431: LOG_FILE="$REPORT_DIR/security_scan_$DATE.log"
 432: 
 433: # Создание директории для отчетов, если она не существует
 434: mkdir -p $REPORT_DIR
 435: 
 436: # Функция для логирования
 437: log() {
 438:     echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a $LOG_FILE
 439: }
 440: 
 441: log "Starting security scan for application"
 442: 
 443: # Проверка зависимостей NPM на уязвимости
 444: log "Scanning frontend dependencies for vulnerabilities..."
 445: cd $FRONTEND_DIR || { log "Failed to change directory to $FRONTEND_DIR"; exit 1; }
 446: npm audit --json > "$REPORT_DIR/npm_audit_$DATE.json"
 447: npm_vulns=$(grep -c "severity" "$REPORT_DIR/npm_audit_$DATE.json")
 448: if [ "$npm_vulns" -gt 0 ]; then
 449:     log "Found $npm_vulns potential vulnerabilities in NPM packages. See report for details."
 450: else
 451:     log "No vulnerabilities found in NPM packages."
 452: fi
 453: 
 454: # Проверка зависимостей Rust на уязвимости с помощью cargo-audit
 455: log "Scanning Rust dependencies for vulnerabilities..."
 456: cd $BACKEND_DIR || { log "Failed to change directory to $BACKEND_DIR"; exit 1; }
 457: if command -v cargo-audit >/dev/null 2>&1; then
 458:     cargo audit > "$REPORT_DIR/cargo_audit_$DATE.txt"
 459:     rust_vulns=$(grep -c "RUSTSEC" "$REPORT_DIR/cargo_audit_$DATE.txt")
 460:     if [ "$rust_vulns" -gt 0 ]; then
 461:         log "Found $rust_vulns potential vulnerabilities in Rust packages. See report for details."
 462:     else
 463:         log "No vulnerabilities found in Rust packages."
 464:     fi
 465: else
 466:     log "cargo-audit not found, install it with: cargo install cargo-audit"
 467: fi
 468: 
 469: # Проверка на наличие секретов в коде с помощью git-secrets
 470: log "Scanning for secrets in codebase..."
 471: cd $APP_DIR || { log "Failed to change directory to $APP_DIR"; exit 1; }
 472: if command -v git-secrets >/dev/null 2>&1; then
 473:     git-secrets --scan > "$REPORT_DIR/git_secrets_$DATE.txt" 2>&1
 474:     if [ $? -ne 0 ]; then
 475:         log "Potential secrets found in repository. See report for details."
 476:     else
 477:         log "No secrets found in repository."
 478:     fi
 479: else
 480:     log "git-secrets not found, install it from: https://github.com/awslabs/git-secrets"
 481: fi
 482: 
 483: # Статический анализ кода (опционально, если установлены инструменты)
 484: log "Running static code analysis..."
 485: 
 486: # Для JavaScript/TypeScript (ESLint)
 487: if command -v eslint >/dev/null 2>&1; then
 488:     cd $FRONTEND_DIR
 489:     eslint . -o "$REPORT_DIR/eslint_report_$DATE.txt" -f unix || log "ESLint found issues"
 490: fi
 491: 
 492: # Для Rust (Clippy)
 493: if command -v cargo-clippy >/dev/null 2>&1; then
 494:     cd $BACKEND_DIR
 495:     cargo clippy --message-format=json > "$REPORT_DIR/clippy_report_$DATE.json" 2>&1
 496: fi
 497: 
 498: log "Security scan completed. Reports saved to $REPORT_DIR"
 499: 
 500: # Отправка отчета по электронной почте (опционально)
 501: if command -v mail >/dev/null 2>&1; then
 502:     cat $LOG_FILE | mail -s "Security Scan Report - $DATE" <EMAIL>
 503:     log "Security report sent via email"
 504: fi
 505: ```
 506: 
 507: **Настройка cron-задания для ежемесячной проверки безопасности:**
 508: 
 509: ```
 510: # Запуск в 2:00 утра в первое воскресенье каждого месяца
 511: 0 2 1-7 * 0 [ "$(date +\%w)" = "0" ] && /path/to/security-scan.sh
 512: ```
 513: 
 514: ### 2.4. Мониторинг состояния сервера и приложения
 515: 
 516: ```bash
 517: #!/bin/bash
 518: # system-health-check.sh
 519: 
 520: # Настройка переменных
 521: APP_DIR="/path/to/application"
 522: LOG_DIR="$APP_DIR/logs"
 523: HEALTH_LOG="$LOG_DIR/health_check.log"
 524: API_ENDPOINT="http://localhost:8080/api/health"
 525: APP_PORT=3000
 526: SLACK_WEBHOOK_URL="https://hooks.slack.com/services/xxx/yyy/zzz"  # Опционально
 527: 
 528: # Создание директории для логов, если она не существует
 529: mkdir -p $LOG_DIR
 530: 
 531: # Функция для логирования
 532: log() {
 533:     echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> $HEALTH_LOG
 534: }
 535: 
 536: # Функция для отправки уведомлений в Slack
 537: send_slack_notification() {
 538:     if [ -n "$SLACK_WEBHOOK_URL" ]; then
 539:         curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$1\"}" $SLACK_WEBHOOK_URL
 540:     fi
 541: }
 542: 
 543: log "Starting system health check"
 544: 
 545: # Проверка использования CPU
 546: CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')
 547: log "CPU Usage: $CPU_USAGE%"
 548: if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
 549:     log "WARNING: High CPU usage detected"
 550:     send_slack_notification "Warning: High CPU usage ($CPU_USAGE%) on server"
 551: fi
 552: 
 553: # Проверка использования памяти
 554: MEMORY_USAGE=$(free | grep Mem | awk '{print $3/$2 * 100.0}')
 555: log "Memory Usage: $MEMORY_USAGE%"
 556: if (( $(echo "$MEMORY_USAGE > 85" | bc -l) )); then
 557:     log "WARNING: High memory usage detected"
 558:     send_slack_notification "Warning: High memory usage ($MEMORY_USAGE%) on server"
 559: fi
 560: 
 561: # Проверка использования диска
 562: DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
 563: log "Disk Usage: $DISK_USAGE%"
 564: if [ "$DISK_USAGE" -gt 85 ]; then
 565:     log "WARNING: High disk usage detected"
 566:     send_slack_notification "Warning: High disk usage ($DISK_USAGE%) on server"
 567: fi
 568: 
 569: # Проверка доступности API
 570: API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $API_ENDPOINT)
 571: log "API Status Code: $API_STATUS"
 572: if [ "$API_STATUS" != "200" ]; then
 573:     log "ERROR: API is not responding properly"
 574:     send_slack_notification "Error: API is not responding properly (Status code: $API_STATUS)"
 575: fi
 576: 
 577: # Проверка доступности приложения
 578: APP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$APP_PORT)
 579: log "App Status Code: $APP_STATUS"
 580: if [ "$APP_STATUS" != "200" ]; then
 581:     log "ERROR: Application is not responding properly"
 582:     send_slack_notification "Error: Application is not responding properly (Status code: $APP_STATUS)"
 583: fi
 584: 
 585: # Проверка количества процессов
 586: PROCESS_COUNT=$(ps aux | wc -l)
 587: log "Process Count: $PROCESS_COUNT"
 588: if [ "$PROCESS_COUNT" -gt 500 ]; then
 589:     log "WARNING: High number of processes"
 590:     send_slack_notification "Warning: High number of processes ($PROCESS_COUNT) on server"
 591: fi
 592: 
 593: # Проверка логов на наличие критических ошибок
 594: if [ -d "$LOG_DIR" ]; then
 595:     ERROR_COUNT=$(grep -i "error\|exception\|fatal" $LOG_DIR/*.log 2>/dev/null | wc -l)
 596:     log "Error Count in Logs: $ERROR_COUNT"
 597:     if [ "$ERROR_COUNT" -gt 50 ]; then
 598:         log "WARNING: High number of errors in logs"
 599:         send_slack_notification "Warning: High number of errors ($ERROR_COUNT) in application logs"
 600:     fi
 601: fi
 602: 
 603: log "System health check completed"
 604: ```
 605: 
 606: **Настройка cron-задания для периодической проверки состояния:**
 607: 
 608: ```
 609: # Запуск каждый час
 610: 0 * * * * /path/to/system-health-check.sh
 611: ```
 612: 
 613: ## 3. Документирование процессов
 614: 
 615: ### 3.1. Создание руководства по деплою
 616: 
 617: **Структура руководства по деплою (deployment-guide.md):**
 618: 
 619: ```markdown
 620: # Руководство по деплою приложения
 621: 
 622: ## Оглавление
 623: 1. Требования к системе
 624: 2. Установка зависимостей
 625: 3. Настройка окружения
 626: 4. Деплой бэкенда
 627: 5. Деплой фронтенда
 628: 6. Настройка Nginx и SSL
 629: 7. Проверка после деплоя
 630: 8. Устранение распространенных проблем
 631: 
 632: ## 1. Требования к системе
 633: 
 634: - Linux сервер (рекомендуется Ubuntu 20.04 LTS или новее)
 635: - Минимум 2 ГБ RAM, рекомендуется 4 ГБ
 636: - Минимум 20 ГБ свободного места на диске
 637: - Node.js 18.x или новее
 638: - Rust 1.70 или новее
 639: - Nginx 1.18 или новее
 640: - Git
 641: 
 642: ## 2. Установка зависимостей
 643: 
 644: ### Установка Node.js
 645: ```bash
 646: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
 647: sudo apt-get install -y nodejs
 648: ```
 649: 
 650: ### Установка Rust
 651: ```bash
 652: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
 653: source $HOME/.cargo/env
 654: ```
 655: 
 656: ### Установка Nginx
 657: ```bash
 658: sudo apt-get install -y nginx
 659: ```
 660: 
 661: ### Установка PM2 для управления Node.js процессами
 662: ```bash
 663: sudo npm install -g pm2
 664: ```
 665: 
 666: ## 3. Настройка окружения
 667: 
 668: ### Клонирование репозитория
 669: ```bash
 670: git clone https://github.com/your-organization/your-repo.git
 671: cd your-repo
 672: ```
 673: 
 674: ### Создание файла .env
 675: ```bash
 676: cp .env.example .env
 677: nano .env
 678: ```
 679: 
 680: Отредактируйте значения переменных окружения согласно вашей среде.
 681: 
 682: ## 4. Деплой бэкенда
 683: 
 684: ### Сборка Rust-бэкенда
 685: ```bash
 686: cd backend-rust
 687: cargo build --release
 688: ```
 689: 
 690: ### Создание systemd-сервиса для бэкенда
 691: 
 692: Создайте файл `/etc/systemd/system/your-app-backend.service`:
 693: 
 694: ```ini
 695: [Unit]
 696: Description=Your App Backend Service
 697: After=network.target
 698: 
 699: [Service]
 700: Type=simple
 701: User=your-user
 702: WorkingDirectory=/path/to/application/backend-rust
 703: ExecStart=/path/to/application/backend-rust/target/release/your-app
 704: Restart=always
 705: Environment=RUST_ENV=production
 706: Environment=PORT=8080
 707: 
 708: [Install]
 709: WantedBy=multi-user.target
 710: ```
 711: 
 712: Активируйте и запустите сервис:
 713: 
 714: ```bash
 715: sudo systemctl enable your-app-backend
 716: sudo systemctl start your-app-backend
 717: ```
 718: 
 719: ## 5. Деплой фронтенда
 720: 
 721: ### Установка зависимостей фронтенда
 722: ```bash
 723: cd /path/to/application
 724: npm install
 725: ```
 726: 
 727: ### Сборка фронтенда
 728: ```bash
 729: npm run build
 730: ```
 731: 
 732: ### Запуск приложения с PM2
 733: ```bash
 734: pm2 start npm --name "your-app-frontend" -- start
 735: pm2 save
 736: pm2 startup
 737: ```
 738: 
 739: ## 6. Настройка Nginx и SSL
 740: 
 741: ### Конфигурация Nginx
 742: 
 743: Создайте файл `/etc/nginx/sites-available/your-app`:
 744: 
 745: ```nginx
 746: server {
 747:     listen 80;
 748:     server_name your-domain.com;
 749: 
 750:     location / {
 751:         proxy_pass http://localhost:3000;
 752:         proxy_http_version 1.1;
 753:         proxy_set_header Upgrade $http_upgrade;
 754:         proxy_set_header Connection 'upgrade';
 755:         proxy_set_header Host $host;
 756:         proxy_cache_bypass $http_upgrade;
 757:     }
 758: 
 759:     location /api {
 760:         proxy_pass http://localhost:8080;
 761:         proxy_http_version 1.1;
 762:         proxy_set_header Upgrade $http_upgrade;
 763:         proxy_set_header Connection 'upgrade';
 764:         proxy_set_header Host $host;
 765:         proxy_cache_bypass $http_upgrade;
 766:     }
 767: }
 768: ```
 769: 
 770: Активируйте конфигурацию:
 771: 
 772: ```bash
 773: sudo ln -s /etc/nginx/sites-available/your-app /etc/nginx/sites-enabled/
 774: sudo nginx -t
 775: sudo systemctl reload nginx
 776: ```
 777: 
 778: ### Настройка SSL с Certbot
 779: 
 780: ```bash
 781: sudo apt-get install -y certbot python3-certbot-nginx
 782: sudo certbot --nginx -d your-domain.com
 783: ```
 784: 
 785: ## 7. Проверка после деплоя
 786: 
 787: После деплоя приложения, проведите следующие проверки:
 788: 
 789: 1. Доступность фронтенда по URL: https://your-domain.com
 790: 2. Доступность API: https://your-domain.com/api/health
 791: 3. Проверка логов:
 792:    ```bash
 793:    sudo journalctl -u your-app-backend -f
 794:    pm2 logs your-app-frontend
 795:    ```
 796: 4. Мониторинг ресурсов:
 797:    ```bash
 798:    htop
 799:    ```
 800: 
 801: ## 8. Устранение распространенных проблем
 802: 
 803: ### Приложение не запускается
 804: - Проверьте логи для получения информации об ошибках
 805: - Убедитесь, что переменные окружения настроены правильно
 806: - Проверьте права доступа к файлам и директориям
 807: 
 808: ### Проблемы с CORS
 809: - Убедитесь, что в бэкенде правильно настроены заголовки CORS
 810: - Проверьте, что домены в CORS_ORIGINS совпадают с фактическим доменом приложения
 811: 
 812: ### Проблемы с SSL
 813: - Проверьте срок действия сертификатов
 814: - Обновите сертификаты при необходимости:
 815:   ```bash
 816:   sudo certbot renew
 817:   ```
 818: ```
 819: 
 820: ### 3.2. Документирование процедур восстановления
 821: 
 822: **Структура руководства по восстановлению (recovery-guide.md):**
 823: 
 824: ```markdown
 825: # Руководство по восстановлению приложения
 826: 
 827: ## Оглавление
 828: 1. Восстановление из резервных копий
 829: 2. Восстановление после сбоя сервера
 830: 3. Восстановление после нарушения безопасности
 831: 4. Повторное развертывание приложения
 832: 5. Откат к предыдущей версии
 833: 6. Аварийные контакты и процедуры эскалации
 834: 
 835: ## 1. Восстановление из резервных копий
 836: 
 837: ### 1.1. Восстановление базы данных
 838: 
 839: #### Для PostgreSQL
 840: ```bash
 841: # Распаковка бэкапа
 842: gunzip /path/to/backups/db/database-name-YYYY-MM-DD.sql.gz
 843: 
 844: # Восстановление базы данных
 845: psql -U username -d database_name -f /path/to/backups/db/database-name-YYYY-MM-DD.sql
 846: ```
 847: 
 848: #### Для MongoDB
 849: ```bash
 850: # Распаковка бэкапа
 851: tar -xzf /path/to/backups/mongo/database-name-YYYY-MM-DD.tar.gz -C /tmp
 852: 
 853: # Восстановление базы данных
 854: mongorestore --uri="*******************************************" --db=database_name /tmp/database-name-YYYY-MM-DD
 855: ```
 856: 
 857: ### 1.2. Восстановление конфигурационных файлов
 858: 
 859: ```bash
 860: # Распаковка бэкапа конфигурационных файлов
 861: mkdir -p /tmp/config-restore
 862: tar -xzf /path/to/backups/configs/configs-YYYY-MM-DD.tar.gz -C /tmp/config-restore
 863: 
 864: # Восстановление конфигурационных файлов
 865: cp -r /tmp/config-restore/* /path/to/application/
 866: ```
 867: 
 868: ### 1.3. Восстановление пользовательских данных
 869: 
 870: ```bash
 871: # Распаковка бэкапа пользовательских данных
 872: tar -xzf /path/to/backups/user_data/user_data-YYYY-MM-DD.tar.gz -C /tmp
 873: 
 874: # Восстановление пользовательских данных
 875: cp -r /tmp/user_data /path/to/application/
 876: ```
 877: 
 878: ### 1.4. Восстановление полного проекта
 879: 
 880: ```bash
 881: # Распаковка полного бэкапа
 882: mkdir -p /tmp/full-restore
 883: tar -xzf /path/to/backups/full/full-YYYY-MM-DD.tar.gz -C /tmp/full-restore
 884: 
 885: # Перемещение восстановленного проекта
 886: mv /path/to/application /path/to/application.old
 887: mv /tmp/full-restore/your-repo /path/to/application
 888: 
 889: # Установка зависимостей
 890: cd /path/to/application
 891: npm install
 892: 
 893: # Сборка фронтенда
 894: npm run build
 895: 
 896: # Сборка бэкенда
 897: cd backend-rust
 898: cargo build --release
 899: 
 900: # Перезапуск сервисов
 901: sudo systemctl restart your-app-backend
 902: pm2 restart your-app-frontend
 903: ```
 904: 
 905: ## 2. Восстановление после сбоя сервера
 906: 
 907: ### 2.1. Проверка состояния сервера
 908: 
 909: ```bash
 910: # Проверка общей информации о системе
 911: uname -a
 912: uptime
 913: 
 914: # Проверка использования диска
 915: df -h
 916: 
 917: # Проверка использования памяти
 918: free -h
 919: 
 920: # Проверка логов системы
 921: sudo journalctl -p err | tail
 922: ```
 923: 
 924: ### 2.2. Перезапуск сервисов
 925: 
 926: ```bash
 927: # Перезапуск бэкенда
 928: sudo systemctl restart your-app-backend
 929: 
 930: # Перезапуск фронтенда
 931: pm2 restart your-app-frontend
 932: 
 933: # Перезапуск Nginx
 934: sudo systemctl restart nginx
 935: ```
 936: 
 937: ### 2.3. Восстановление после критического сбоя
 938: 
 939: 1. Подготовка нового сервера с необходимыми зависимостями (см. руководство по деплою)
 940: 2. Восстановление данных из резервных копий (см. раздел 1)
 941: 3. Настройка DNS для переключения на новый сервер
 942: 4. Проверка функционирования приложения на новом сервере
 943: 
 944: ## 3. Восстановление после нарушения безопасности
 945: 
 946: ### 3.1. Изоляция скомпрометированной системы
 947: 
 948: ```bash
 949: # Отключение от сети (в крайнем случае)
 950: sudo ifconfig eth0 down
 951: 
 952: # Остановка всех сервисов
 953: sudo systemctl stop your-app-backend
 954: pm2 stop all
 955: sudo systemctl stop nginx
 956: ```
 957: 
 958: ### 3.2. Оценка масштаба нарушения
 959: 
 960: ```bash
 961: # Проверка логов авторизации
 962: sudo cat /var/log/auth.log | grep "Failed password"
 963: 
 964: # Проверка запущенных процессов
 965: ps aux | grep -v -E "(root|USERNAME)"
 966: 
 967: # Проверка открытых соединений
 968: sudo netstat -tulpn
 969: ```
 970: 
 971: ### 3.3. Восстановление системы
 972: 
 973: 1. Создание полной резервной копии текущего состояния для последующего анализа
 974: 2. Восстановление из чистого образа системы
 975: 3. Применение актуальных обновлений безопасности:
 976:    ```bash
 977:    sudo apt-get update
 978:    sudo apt-get upgrade
 979:    ```
 980: 4. Восстановление приложения из проверенной резервной копии
 981: 5. Изменение всех учетных данных, паролей и ключей доступа
 982: 6. Настройка усиленных политик безопасности
 983: 
 984: ## 4. Повторное развертывание приложения
 985: 
 986: Если требуется полное переразвертывание приложения:
 987: 
 988: 1. Остановите текущие экземпляры:
 989:    ```bash
 990:    sudo systemctl stop your-app-backend
 991:    pm2 stop your-app-frontend
 992:    ```
 993: 
 994: 2. Удалите старые файлы:
 995:    ```bash
 996:    mv /path/to/application /path/to/application.old
 997:    ```
 998: 
 999: 3. Клонируйте репозиторий и настройте приложение заново:
1000:    ```bash
1001:    git clone https://github.com/your-organization/your-repo.git /path/to/application
1002:    cd /path/to/application
1003:    ```
1004: 
1005: 4. Следуйте стандартной процедуре деплоя (см. руководство по деплою)
1006: 
1007: 5. Внесите необходимые настройки и восстановите данные из резервных копий
1008: 
1009: ## 5. Откат к предыдущей версии
1010: 
1011: ### 5.1. Откат фронтенда
1012: 
1013: ```bash
1014: # Переход в директорию проекта
1015: cd /path/to/application
1016: 
1017: # Переключение на предыдущую версию в Git
1018: git checkout v1.x.x  # или конкретный тег/коммит
1019: 
1020: # Установка зависимостей и сборка
1021: npm install
1022: npm run build
1023: 
1024: # Перезапуск приложения
1025: pm2 restart your-app-frontend
1026: ```
1027: 
1028: ### 5.2. Откат бэкенда
1029: 
1030: ```bash
1031: # Переход в директорию бэкенда
1032: cd /path/to/application/backend-rust
1033: 
1034: # Переключение на предыдущую версию в Git
1035: git checkout v1.x.x  # или конкретный тег/коммит
1036: 
1037: # Сборка
1038: cargo build --release
1039: 
1040: # Перезапуск сервиса
1041: sudo systemctl restart your-app-backend
1042: ```
1043: 
1044: ### 5.3. Восстановление базы данных к предыдущему состоянию
1045: 
1046: Следуйте инструкциям в разделе 1.1 для восстановления базы данных из резервной копии, сделанной перед обновлением.
1047: 
1048: ## 6. Аварийные контакты и процедуры эскалации
1049: 
1050: ### 6.1. Контактная информация ответственных лиц
1051: 
1052: | Роль | Имя | Контакт | Время доступности |
1053: |------|-----|---------|-------------------|
1054: | Главный разработчик | Имя Фамилия | <EMAIL>, +1234567890 | 9:00-18:00 GMT+3 |
1055: | DevOps-инженер | Имя Фамилия | <EMAIL>, +1234567890 | 8:00-20:00 GMT+3 |
1056: | Руководитель проекта | Имя Фамилия | <EMAIL>, +1234567890 | 9:00-19:00 GMT+3 |
1057: 
1058: ### 6.2. Процедура эскалации
1059: 
1060: 1. **Уровень 1**: Попытка решения проблемы DevOps-инженером (время реакции: 30 минут, время решения: 2 часа)
1061: 2. **Уровень 2**: Вовлечение главного разработчика (время реакции: 1 час, время решения: 4 часа)
1062: 3. **Уровень 3**: Эскалация до руководителя проекта (время реакции: 2 часа, время решения: 8 часов)
1063: 4. **Критический инцидент**: Немедленное уведомление всей команды, формирование кризисной группы
1064: 
1065: ### 6.3. Шаблон отчета об инциденте
1066: 
1067: ```
1068: ## Отчет об инциденте
1069: 
1070: Дата и время инцидента: [ДАТА] [ВРЕМЯ]
1071: Обнаружено: [КЕМ]
1072: Затронутые системы: [СИСТЕМЫ]
1073: Описание проблемы: [ОПИСАНИЕ]
1074: 
1075: ### Хронология
1076: - [ВРЕМЯ]: [СОБЫТИЕ]
1077: - [ВРЕМЯ]: [СОБЫТИЕ]
1078: 
1079: ### Предпринятые действия
1080: 1. [ДЕЙСТВИЕ]
1081: 2. [ДЕЙСТВИЕ]
1082: 
1083: ### Первопричина
1084: [ОБЪЯСНЕНИЕ ПРИЧИНЫ]
1085: 
1086: ### Предлагаемые меры предотвращения
1087: 1. [МЕРА]
1088: 2. [МЕРА]
1089: 
1090: ### Извлеченные уроки
1091: [УРОКИ]
1092: ```
1093: ```
1094: 
1095: ### 3.3. Создание чек-листов для обновления приложения
1096: 
1097: **Чек-лист для планового обновления приложения (update-checklist.md):**
1098: 
1099: ```markdown
1100: # Чек-лист для обновления приложения
1101: 
1102: ## Подготовка к обновлению
1103: 
1104: - [ ] Проверьте наличие последней версии документации
1105: - [ ] Уведомите пользователей о предстоящем обновлении и возможных простоях
1106: - [ ] Создайте ветку для обновления в репозитории
1107: - [ ] Убедитесь, что все текущие изменения закоммичены
1108: - [ ] Создайте полную резервную копию приложения и базы данных
1109: - [ ] Убедитесь, что у вас есть все необходимые учетные данные и доступы
1110: 
1111: ## Подготовка среды разработки
1112: 
1113: - [ ] Выполните слияние изменений из основной ветки в ветку обновления
1114: - [ ] Установите новые зависимости (при необходимости)
1115: - [ ] Обновите конфигурационные файлы (при необходимости)
1116: - [ ] Выполните миграции базы данных на тестовой среде (при необходимости)
1117: - [ ] Запустите все тесты для проверки совместимости
1118: 
1119: ## Обновление тестовой среды
1120: 
1121: - [ ] Разверните обновление на тестовом сервере
1122: - [ ] Проверьте работоспособность основных функций
1123: - [ ] Проверьте производительность приложения
1124: - [ ] Выполните тесты безопасности (при необходимости)
1125: - [ ] Устраните выявленные проблемы
1126: - [ ] Обновите документацию (при необходимости)
1127: 
1128: ## Обновление production-среды
1129: 
1130: - [ ] Включите режим обслуживания или уведомление о проведении работ
1131: - [ ] Создайте финальную резервную копию перед обновлением
1132: - [ ] Обновите бэкенд:
1133:   - [ ] Остановите бэкенд-сервис: `sudo systemctl stop your-app-backend`
1134:   - [ ] Обновите код: `git pull origin main`
1135:   - [ ] Соберите приложение: `cargo build --release`
1136:   - [ ] Запустите бэкенд-сервис: `sudo systemctl start your-app-backend`
1137:   - [ ] Проверьте статус: `sudo systemctl status your-app-backend`
1138: - [ ] Обновите фронтенд:
1139:   - [ ] Остановите фронтенд: `pm2 stop your-app-frontend`
1140:   - [ ] Обновите код: `git pull origin main`
1141:   - [ ] Установите зависимости: `npm install`
1142:   - [ ] Соберите приложение: `npm run build`
1143:   - [ ] Запустите фронтенд: `pm2 start your-app-frontend`
1144:   - [ ] Проверьте статус: `pm2 status`
1145: - [ ] Выполните миграции базы данных (при необходимости)
1146: - [ ] Обновите конфигурации Nginx (при необходимости)
1147: - [ ] Перезагрузите Nginx: `sudo systemctl reload nginx`
1148: - [ ] Отключите режим обслуживания
1149: 
1150: ## Проверка после обновления
1151: 
1152: - [ ] Проверьте доступность приложения
1153: - [ ] Выполните проверку всех критичных бизнес-процессов
1154: - [ ] Проверьте производительность после обновления
1155: - [ ] Проверьте логи на наличие ошибок:
1156:   - [ ] Логи бэкенда: `sudo journalctl -u your-app-backend -f`
1157:   - [ ] Логи фронтенда: `pm2 logs your-app-frontend`
1158:   - [ ] Логи Nginx: `sudo tail -f /var/log/nginx/error.log`
1159: 
1160: ## Действия при обнаружении проблем
1161: 
1162: - [ ] Соберите информацию о проблеме (логи, скриншоты, данные мониторинга)
1163: - [ ] Оцените критичность проблемы
1164: - [ ] При критических проблемах выполните откат (см. руководство по восстановлению)
1165: - [ ] При некритических проблемах создайте задачу в трекере и назначьте ответственного
1166: - [ ] Обновите документацию с учетом выявленных проблем
1167: 
1168: ## Коммуникация
1169: 
1170: - [ ] Уведомите команду о завершении обновления
1171: - [ ] Уведомите пользователей о завершении работ и новых функциях
1172: - [ ] Обновите информацию о версии в документации
1173: - [ ] Создайте отчет о проведенном обновлении
1174: 
1175: ## Финальные шаги
1176: 
1177: - [ ] Создайте тег для новой версии в репозитории
1178: - [ ] Обновите changelog
1179: - [ ] Проверьте и обновите мониторинг и оповещения
1180: - [ ] Запланируйте следующее обновление (при необходимости)
1181: ```
1182: 
1183: **Чек-лист для экстренного обновления безопасности (security-update-checklist.md):**
1184: 
1185: ```markdown
1186: # Чек-лист для экстренного обновления безопасности
1187: 
1188: ## Оценка уязвимости
1189: 
1190: - [ ] Определите источник информации об уязвимости
1191: - [ ] Оцените критичность уязвимости (критическая, высокая, средняя, низкая)
1192: - [ ] Проверьте, затрагивает ли уязвимость ваше приложение
1193: - [ ] Определите компоненты, которые нуждаются в обновлении
1194: - [ ] Оцените риски, связанные с уязвимостью и обновлением
1195: 
1196: ## Подготовка к обновлению
1197: 
1198: - [ ] Создайте отдельную ветку для патча безопасности
1199: - [ ] Загрузите патч или обновление компонента
1200: - [ ] Примените патч к исходному коду
1201: - [ ] Протестируйте исправление в изолированной среде
1202: - [ ] Подготовьте скрипты отката на случай проблем
1203: - [ ] Создайте полную резервную копию приложения и данных
1204: 
1205: ## Развертывание обновления
1206: 
1207: - [ ] Создайте минимальное уведомление о техническом обслуживании (без раскрытия деталей уязвимости)
1208: - [ ] Обновите код в репозитории
1209: - [ ] Разверните обновление с минимальным временем простоя
1210:   - [ ] Для бэкенда:
1211:     ```bash
1212:     sudo systemctl stop your-app-backend
1213:     git pull origin security-patch
1214:     cargo build --release
1215:     sudo systemctl start your-app-backend
1216:     ```
1217:   - [ ] Для фронтенда:
1218:     ```bash
1219:     pm2 stop your-app-frontend
1220:     git pull origin security-patch
1221:     npm install
1222:     npm run build
1223:     pm2 start your-app-frontend
1224:     ```
1225: - [ ] Проверьте работоспособность приложения
1226: - [ ] Проверьте, устранена ли уязвимость
1227: 
1228: ## Проверка после обновления
1229: 
1230: - [ ] Мониторинг логов на наличие ошибок
1231: - [ ] Проверка основной функциональности
1232: - [ ] Проверка производительности
1233: - [ ] Убедитесь, что патч безопасности эффективен
1234: 
1235: ## Действия при возникновении проблем
1236: 
1237: - [ ] Оцените, перевешивает ли риск уязвимости проблемы обновления
1238: - [ ] При необходимости выполните откат к предыдущей версии
1239: - [ ] Изолируйте проблему и подготовьте новый патч
1240: - [ ] Повторно протестируйте и разверните исправление
1241: 
1242: ## Коммуникация и отчетность
1243: 
1244: - [ ] Уведомите команду о выполненном обновлении безопасности
1245: - [ ] Подготовьте отчет об инциденте (не раскрывая технические детали уязвимости)
1246: - [ ] Обновите внутреннюю документацию по безопасности
1247: - [ ] Уведомите пользователей о выполненном обновлении безопасности (в общих чертах)
1248: 
1249: ## Последующие действия
1250: 
1251: - [ ] Актуализируйте зависимости
1252: - [ ] Обновите методы сканирования для обнаружения подобных уязвимостей
1253: - [ ] Рассмотрите возможность автоматизации процесса применения патчей безопасности
1254: - [ ] Обновите план реагирования на инциденты, если необходимо
1255: ```
1256: 
1257: ## 4. Интеграция с системами CI/CD
1258: 
1259: ### 4.1. Автоматизация бэкапов в CI/CD пайплайне
1260: 
1261: **GitHub Actions Workflow для автоматического бэкапа при деплое (.github/workflows/backup-before-deploy.yml):**
1262: 
1263: ```yaml
1264: name: Backup Before Deploy
1265: 
1266: on:
1267:   push:
1268:     branches: [ main ]
1269:   workflow_dispatch:
1270: 
1271: jobs:
1272:   backup:
1273:     runs-on: ubuntu-latest
1274:     
1275:     steps:
1276:     - name: Checkout repository
1277:       uses: actions/checkout@v3
1278:     
1279:     - name: Set up SSH
1280:       uses: webfactory/ssh-agent@v0.7.0
1281:       with:
1282:         ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
1283:     
1284:     - name: Create pre-deploy backup
1285:       run: |
1286:         ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "bash -s" << 'EOF'
1287:           # Настройка переменных
1288:           APP_DIR="/path/to/application"
1289:           BACKUP_DIR="/path/to/backups/deploy"
1290:           DATE=$(date +"%Y-%m-%d_%H-%M-%S")
1291:           BACKUP_FILE="$BACKUP_DIR/pre-deploy-$DATE.tar.gz"
1292:           
1293:           # Создание директории для резервных копий, если она не существует
1294:           mkdir -p $BACKUP_DIR
1295:           
1296:           # Создание архива перед деплоем
1297:           tar -czf $BACKUP_FILE --exclude="node_modules" --exclude=".next" --exclude="target" -C $(dirname $APP_DIR) $(basename $APP_DIR)
1298:           
1299:           # Логирование результата
1300:           echo "Pre-deploy backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
1301:           
1302:           # Удаление старых резервных копий (хранить только последние 5)
1303:           ls -t $BACKUP_DIR/pre-deploy-*.tar.gz | tail -n +6 | xargs -r rm
1304:         EOF
1305: 
1306:   deploy:
1307:     needs: backup
1308:     runs-on: ubuntu-latest
1309:     
1310:     steps:
1311:     - name: Checkout repository
1312:       uses: actions/checkout@v3
1313:     
1314:     # Дальнейшие шаги по деплою приложения
1315: ```
1316: 
1317: ### 4.2. Интеграция проверок безопасности в CI/CD
1318: 
1319: **GitHub Actions Workflow для проверки безопасности (.github/workflows/security-checks.yml):**
1320: 
1321: ```yaml
1322: name: Security Checks
1323: 
1324: on:
1325:   push:
1326:     branches: [ main, development ]
1327:   pull_request:
1328:     branches: [ main ]
1329:   schedule:
1330:     - cron: '0 0 * * 1' # Запуск каждый понедельник
1331: 
1332: jobs:
1333:   security-scan:
1334:     runs-on: ubuntu-latest
1335:     
1336:     steps:
1337:     - name: Checkout repository
1338:       uses: actions/checkout@v3
1339:     
1340:     - name: Set up Node.js
1341:       uses: actions/setup-node@v3
1342:       with:
1343:         node-version: '18'
1344:     
1345:     - name: Set up Rust
1346:       uses: actions-rs/toolchain@v1
1347:       with:
1348:         toolchain: stable
1349:         override: true
1350:     
1351:     - name: Install cargo-audit
1352:       uses: actions-rs/cargo@v1
1353:       with:
1354:         command: install
1355:         args: cargo-audit
1356:     
1357:     - name: Check NPM dependencies for vulnerabilities
1358:       run: |
1359:         npm audit --json > npm_audit_report.json || true
1360:         echo "NPM Audit Completed"
1361:     
1362:     - name: Check Rust dependencies for vulnerabilities
1363:       working-directory: ./backend-rust
1364:       run: |
1365:         cargo audit > cargo_audit_report.txt || true
1366:         echo "Cargo Audit Completed"
1367:     
1368:     - name: Run ESLint security plugin
1369:       run: |
1370:         npm install eslint eslint-plugin-security
1371:         npx eslint --plugin security --ext .js,.jsx src/ -f json > eslint_security_report.json || true
1372:     
1373:     - name: Scan for secrets
1374:       uses: gitleaks/gitleaks-action@v2
1375:       env:
1376:         GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
1377:     
1378:     - name: Upload security reports as artifacts
1379:       uses: actions/upload-artifact@v3
1380:       with:
1381:         name: security-reports
1382:         path: |
1383:           npm_audit_report.json
1384:           cargo_audit_report.txt
1385:           eslint_security_report.json
1386: ```
1387: 
1388: ## 5. Шаблоны мониторинга и отчетности
1389: 
1390: ### 5.1. Шаблон ежедневного отчета о состоянии системы
1391: 
1392: ```markdown
1393: # Ежедневный отчет о состоянии системы - {{ date }}
1394: 
1395: ## Основные показатели
1396: - Время бесперебойной работы: {{ uptime }}
1397: - Использование CPU: {{ cpu_usage }}%
1398: - Использование памяти: {{ memory_usage }}%
1399: - Использование диска: {{ disk_usage }}%
1400: - Количество активных пользователей: {{ active_users }}
1401: 
1402: ## Выполненные бэкапы
1403: - База данных: {{ db_backup_status }}
1404: - Конфигурационные файлы: {{ config_backup_status }}
1405: - Пользовательские данные: {{ user_data_backup_status }}
1406: 
1407: ## Проблемы и предупреждения
1408: {{ issues_and_warnings }}
1409: 
1410: ## Рекомендации
1411: {{ recommendations }}
1412: 
1413: ## Предстоящие задачи обслуживания
1414: {{ upcoming_maintenance_tasks }}
1415: ```
1416: 
1417: ### 5.2. Шаблон для мониторинга в Prometheus (prometheus.yml)
1418: 
1419: ```yaml
1420: # Конфигурация Prometheus для мониторинга вашего приложения
1421: 
1422: global:
1423:   scrape_interval: 15s
1424:   evaluation_interval: 15s
1425: 
1426: alerting:
1427:   alertmanagers:
1428:   - static_configs:
1429:     - targets: ['localhost:9093']
1430: 
1431: rule_files:
1432:   - "alerts.yml"
1433: 
1434: scrape_configs:
1435:   - job_name: 'node'
1436:     static_configs:
1437:     - targets: ['localhost:9100']
1438:     
1439:   - job_name: 'backend'
1440:     metrics_path: '/metrics'
1441:     static_configs:
1442:     - targets: ['localhost:8080']
1443:     
1444:   - job_name: 'frontend'
1445:     metrics_path: '/api/metrics'
1446:     static_configs:
1447:     - targets: ['localhost:3000']
1448: ```
1449: 
1450: ### 5.3. Правила оповещений для Prometheus (alerts.yml)
1451: 
1452: ```yaml
1453: groups:
1454: - name: Alerts
1455:   rules:
1456:   - alert: HighCPUUsage
1457:     expr: avg(rate(node_cpu_seconds_total{mode!="idle"}[5m])) by (instance) > 0.8
1458:     for: 5m
1459:     labels:
1460:       severity: warning
1461:     annotations:
1462:       summary: "High CPU usage detected"
1463:       description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"
1464: 
1465:   - alert: HighMemoryUsage
1466:     expr: node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10
1467:     for: 5m
1468:     labels:
1469:       severity: warning
1470:     annotations:
1471:       summary: "High memory usage detected"
1472:       description: "Less than 10% memory available on {{ $labels.instance }}"
1473: 
1474:   - alert: HighDiskUsage
1475:     expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 10
1476:     for: 5m
1477:     labels:
1478:       severity: warning
1479:     annotations:
1480:       summary: "High disk usage detected"
1481:       description: "Less than 10% disk space available on {{ $labels.instance }}"
1482: 
1483:   - alert: InstanceDown
1484:     expr: up == 0
1485:     for: 5m
1486:     labels:
1487:       severity: critical
1488:     annotations:
1489:       summary: "Instance {{ $labels.instance }} down"
1490:       description: "{{ $labels.instance }} has been down for more than 5 minutes"
1491: 
1492:   - alert: BackendHighErrorRate
1493:     expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
1494:     for: 5m
1495:     labels:
1496:       severity: critical
1497:     annotations:
1498:       summary: "High error rate on backend"
1499:       description: "Error rate is higher than 5% on {{ $labels.instance }}"
1500: ```
````

## File: План развёртывания приложения и CI:CD/9. nextjs-hybrid-rendering-plan.md
````markdown
  1: # План оптимизации Next.js с гибридным рендерингом
  2: 
  3: ## 1. Оптимизация серверного рендеринга
  4: 
  5: ### 1.1. Настройка переменных окружения для Next.js
  6: 
  7: ```bash
  8: # .env.production
  9: NODE_ENV=production
 10: NEXT_PUBLIC_API_URL=https://api.example.com
 11: NEXT_TELEMETRY_DISABLED=1
 12: NEXT_SHARP_PATH=/usr/local/lib/node_modules/sharp
 13: NEXT_OPTIMIZE_FONTS=1
 14: NEXT_OPTIMIZE_IMAGES=1
 15: NEXT_OPTIMIZE_CSS=1
 16: ```
 17: 
 18: **Рекомендации:**
 19: - Создать отдельные файлы переменных окружения для разных сред: `.env.development`, `.env.test`, `.env.production`
 20: - Использовать префикс `NEXT_PUBLIC_` только для переменных, доступных на клиентской стороне
 21: - Отключить телеметрию Next.js в production для снижения накладных расходов
 22: - Указать путь к библиотеке Sharp для оптимизации изображений
 23: 
 24: ### 1.2. Настройка кэширования для ISR (Incremental Static Regeneration)
 25: 
 26: **Обновление конфигурации Next.js (`next.config.js`):**
 27: 
 28: ```javascript
 29: /** @type {import('next').NextConfig} */
 30: const nextConfig = {
 31:   reactStrictMode: true,
 32:   
 33:   // Включение experimental appDir для App Router (если используется)
 34:   experimental: {
 35:     appDir: true,
 36:   },
 37:   
 38:   // Настройки для компрессии и минификации
 39:   compress: true,
 40:   poweredByHeader: false,
 41:   
 42:   // Настройки для оптимизации изображений
 43:   images: {
 44:     domains: ['example.com', 'cdn.example.com'],
 45:     deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
 46:     imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
 47:     minimumCacheTTL: 60, // в секундах
 48:   },
 49:   
 50:   // Конфигурация для ISR
 51:   // В App Router это также можно настроить через опции fetch или generateStaticParams
 52:   async headers() {
 53:     return [
 54:       {
 55:         source: '/(.*)',
 56:         headers: [
 57:           {
 58:             key: 'Cache-Control',
 59:             value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=86400',
 60:           },
 61:         ],
 62:       },
 63:     ];
 64:   },
 65: };
 66: 
 67: module.exports = nextConfig;
 68: ```
 69: 
 70: **Настройка ISR для отдельных страниц (Pages Router):**
 71: 
 72: ```javascript
 73: // pages/product/[id].js
 74: export async function getStaticProps({ params }) {
 75:   const product = await fetchProduct(params.id);
 76:   
 77:   return {
 78:     props: {
 79:       product,
 80:     },
 81:     // Повторная генерация через каждые 60 секунд (если есть запрос)
 82:     revalidate: 60,
 83:   };
 84: }
 85: 
 86: export async function getStaticPaths() {
 87:   // Предварительно генерируем только популярные продукты
 88:   const popularProducts = await fetchPopularProducts();
 89:   
 90:   return {
 91:     paths: popularProducts.map(product => ({
 92:       params: { id: product.id.toString() },
 93:     })),
 94:     // Разрешаем генерацию по запросу для остальных продуктов
 95:     fallback: 'blocking',
 96:   };
 97: }
 98: ```
 99: 
100: **Настройка ISR для App Router:**
101: 
102: ```javascript
103: // app/product/[id]/page.js
104: export default async function ProductPage({ params }) {
105:   // Использование fetch с revalidate в App Router
106:   const product = await fetch(`https://api.example.com/products/${params.id}`, {
107:     next: { revalidate: 60 }, // Повторная проверка каждые 60 секунд
108:   }).then(res => res.json());
109:   
110:   return <ProductDetails product={product} />;
111: }
112: 
113: // Предварительно генерируемые пути
114: export async function generateStaticParams() {
115:   const popularProducts = await fetch('https://api.example.com/popular-products')
116:     .then(res => res.json());
117:   
118:   return popularProducts.map(product => ({
119:     id: product.id.toString(),
120:   }));
121: }
122: ```
123: 
124: ### 1.3. Оптимизация Node.js для production-окружения
125: 
126: **Настройка PM2 для управления процессами Node.js (`ecosystem.config.js`):**
127: 
128: ```javascript
129: module.exports = {
130:   apps: [
131:     {
132:       name: 'next-app',
133:       script: 'node_modules/next/dist/bin/next',
134:       args: 'start',
135:       instances: 'max', // Автомасштабирование в зависимости от доступных CPU
136:       exec_mode: 'cluster', // Режим кластера для балансировки нагрузки
137:       watch: false,
138:       env: {
139:         PORT: 3000,
140:         NODE_ENV: 'production',
141:       },
142:       // Настройки для оптимизации памяти
143:       node_args: [
144:         '--max-old-space-size=4096', // Для приложений с большим потреблением памяти
145:         '--optimize-for-size',
146:         '--max-http-header-size=8192',
147:         '--no-warnings',
148:       ],
149:       // Настройки перезапуска при проблемах с памятью
150:       max_memory_restart: '1G',
151:       shutdown_with_message: true,
152:       wait_ready: true,
153:       listen_timeout: 10000,
154:       kill_timeout: 5000,
155:     },
156:   ],
157: };
158: ```
159: 
160: **Оптимизация сборки Node.js:**
161: 
162: ```bash
163: # Использование переменных окружения для оптимизации производительности
164: export NODE_ENV=production
165: export NODE_OPTIONS="--max-old-space-size=4096"
166: 
167: # Сборка приложения с оптимизациями
168: npm run build
169: 
170: # Запуск приложения через PM2
171: pm2 start ecosystem.config.js
172: ```
173: 
174: **Советы по оптимизации сервера:**
175: - Использовать `NODE_ENV=production` для включения всех оптимизаций
176: - Настроить ограничение памяти в зависимости от доступных ресурсов
177: - Использовать кластерный режим для масштабирования на многоядерных серверах
178: - Настроить автоматический перезапуск при утечках памяти
179: - Включить source map для отладки в production-окружении
180: 
181: ## 2. Настройка статического контента
182: 
183: ### 2.1. Конфигурация Nginx для эффективной доставки статического контента
184: 
185: **Базовая конфигурация Nginx для Next.js (`nginx.conf`):**
186: 
187: ```nginx
188: user nginx;
189: worker_processes auto;
190: error_log /var/log/nginx/error.log warn;
191: pid /var/run/nginx.pid;
192: 
193: events {
194:     worker_connections 1024;
195: }
196: 
197: http {
198:     include /etc/nginx/mime.types;
199:     default_type application/octet-stream;
200:     
201:     # Настройки логирования
202:     log_format main '$remote_addr - $remote_user [$time_local] "$request" '
203:                      '$status $body_bytes_sent "$http_referer" '
204:                      '"$http_user_agent" "$http_x_forwarded_for"';
205:     access_log /var/log/nginx/access.log main;
206:     
207:     # Оптимизация производительности
208:     sendfile on;
209:     tcp_nopush on;
210:     tcp_nodelay on;
211:     keepalive_timeout 65;
212:     types_hash_max_size 2048;
213:     server_tokens off;
214:     
215:     # Настройки сжатия
216:     gzip on;
217:     gzip_comp_level 6;
218:     gzip_min_length 256;
219:     gzip_proxied any;
220:     gzip_vary on;
221:     gzip_types
222:         application/javascript
223:         application/json
224:         application/x-javascript
225:         application/xml
226:         application/xml+rss
227:         image/svg+xml
228:         text/css
229:         text/javascript
230:         text/plain
231:         text/xml;
232:     
233:     # Конфигурация сервера Next.js
234:     server {
235:         listen 80;
236:         server_name example.com www.example.com;
237:         
238:         # Перенаправление на HTTPS
239:         return 301 https://$host$request_uri;
240:     }
241:     
242:     server {
243:         listen 443 ssl http2;
244:         server_name example.com www.example.com;
245:         
246:         # SSL настройки
247:         ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
248:         ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
249:         ssl_protocols TLSv1.2 TLSv1.3;
250:         ssl_prefer_server_ciphers on;
251:         ssl_session_timeout 1d;
252:         ssl_session_cache shared:SSL:50m;
253:         ssl_stapling on;
254:         ssl_stapling_verify on;
255:         
256:         # Корневая директория Next.js
257:         root /var/www/next-app;
258:         
259:         # Настройки для статического контента
260:         location /_next/static/ {
261:             alias /var/www/next-app/.next/static/;
262:             expires 365d;
263:             add_header Cache-Control "public, max-age=31536000, immutable";
264:             access_log off;
265:         }
266:         
267:         # Настройки для статических файлов в public директории
268:         location /static/ {
269:             alias /var/www/next-app/public/static/;
270:             expires 30d;
271:             add_header Cache-Control "public, max-age=2592000";
272:             access_log off;
273:         }
274:         
275:         # Настройки для изображений
276:         location /_next/image {
277:             proxy_pass http://localhost:3000;
278:             proxy_http_version 1.1;
279:             proxy_set_header Upgrade $http_upgrade;
280:             proxy_set_header Connection 'upgrade';
281:             proxy_set_header Host $host;
282:             proxy_cache_bypass $http_upgrade;
283:             
284:             # Кэширование оптимизированных изображений
285:             proxy_cache nextjs_cache;
286:             proxy_cache_valid 200 302 60m;
287:             proxy_cache_valid 404 1m;
288:             add_header X-Cache-Status $upstream_cache_status;
289:         }
290:         
291:         # Настройки для остальных запросов к Next.js
292:         location / {
293:             proxy_pass http://localhost:3000;
294:             proxy_http_version 1.1;
295:             proxy_set_header Upgrade $http_upgrade;
296:             proxy_set_header Connection 'upgrade';
297:             proxy_set_header Host $host;
298:             proxy_set_header X-Real-IP $remote_addr;
299:             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
300:             proxy_set_header X-Forwarded-Proto $scheme;
301:             proxy_buffering on;
302:             proxy_buffer_size 128k;
303:             proxy_buffers 4 256k;
304:             proxy_busy_buffers_size 256k;
305:         }
306:         
307:         # Обработка ошибок
308:         error_page 404 /404.html;
309:         location = /404.html {
310:             root /var/www/next-app/public;
311:             internal;
312:         }
313:         
314:         error_page 500 502 503 504 /50x.html;
315:         location = /50x.html {
316:             root /var/www/next-app/public;
317:             internal;
318:         }
319:     }
320:     
321:     # Настройки кэширования
322:     proxy_cache_path /var/cache/nginx/nextjs_cache levels=1:2 keys_zone=nextjs_cache:10m max_size=1g inactive=60m use_temp_path=off;
323: }
324: ```
325: 
326: ### 2.2. Настройка кэширования для статически генерируемых страниц
327: 
328: **Дополнительные HTTP-заголовки для кэширования в Next.js (`next.config.js`):**
329: 
330: ```javascript
331: async headers() {
332:   return [
333:     // Кэширование для статических страниц
334:     {
335:       source: '/',
336:       headers: [
337:         {
338:           key: 'Cache-Control',
339:           value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
340:         },
341:       ],
342:     },
343:     // Кэширование для страниц блога
344:     {
345:       source: '/blog/:slug',
346:       headers: [
347:         {
348:           key: 'Cache-Control',
349:           value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
350:         },
351:       ],
352:     },
353:     // Кэширование для статических ресурсов
354:     {
355:       source: '/_next/static/:path*',
356:       headers: [
357:         {
358:           key: 'Cache-Control',
359:           value: 'public, max-age=31536000, immutable',
360:         },
361:       ],
362:     },
363:     // Кэширование для изображений
364:     {
365:       source: '/images/:path*',
366:       headers: [
367:         {
368:           key: 'Cache-Control',
369:           value: 'public, max-age=86400, stale-while-revalidate=43200',
370:         },
371:       ],
372:     },
373:   ];
374: }
375: ```
376: 
377: **Создание скрипта для согревания кэша (cache warming):**
378: 
379: ```javascript
380: // scripts/cache-warmer.js
381: const fetch = require('node-fetch');
382: const fs = require('fs');
383: const path = require('path');
384: 
385: // Путь к статически сгенерированным путям из getStaticPaths
386: const pagesManifestPath = path.join(process.cwd(), '.next/server/pages-manifest.json');
387: const pagesManifest = JSON.parse(fs.readFileSync(pagesManifestPath, 'utf8'));
388: 
389: const baseUrl = process.env.SITE_URL || 'https://example.com';
390: 
391: // Основные страницы для прогревания кэша
392: const pagesToWarm = [
393:   '/',
394:   '/about',
395:   '/contact',
396:   '/blog',
397: ];
398: 
399: // Динамические страницы на основе getStaticPaths
400: const dynamicPages = Object.keys(pagesManifest)
401:   .filter(page => page.includes('[') && page.includes(']'))
402:   .map(page => {
403:     const pathParam = page.match(/\[(.*?)\]/)[1];
404:     return { page, pathParam };
405:   });
406: 
407: // Получение данных для динамических страниц
408: async function getDynamicPageParams() {
409:   // Здесь можно реализовать логику получения параметров для динамических страниц
410:   // Например, из API или базы данных
411:   const response = await fetch(`${baseUrl}/api/page-params`);
412:   return response.json();
413: }
414: 
415: async function warmCache() {
416:   console.log('🔥 Начинаем согревание кэша...');
417:   
418:   // Прогрев статических страниц
419:   for (const page of pagesToWarm) {
420:     try {
421:       const url = `${baseUrl}${page}`;
422:       console.log(`Согреваем: ${url}`);
423:       await fetch(url);
424:     } catch (error) {
425:       console.error(`Ошибка при согревании ${page}:`, error);
426:     }
427:   }
428:   
429:   // Прогрев динамических страниц
430:   if (dynamicPages.length > 0) {
431:     const params = await getDynamicPageParams();
432:     
433:     for (const { page, pathParam } of dynamicPages) {
434:       for (const param of params) {
435:         try {
436:           const formattedPage = page.replace(`[${pathParam}]`, param[pathParam]);
437:           const url = `${baseUrl}${formattedPage}`;
438:           console.log(`Согреваем динамическую страницу: ${url}`);
439:           await fetch(url);
440:         } catch (error) {
441:           console.error(`Ошибка при согревании динамической страницы:`, error);
442:         }
443:       }
444:     }
445:   }
446:   
447:   console.log('✅ Кэш успешно согрет!');
448: }
449: 
450: warmCache().catch(console.error);
451: ```
452: 
453: **Добавление скрипта для автоматического согревания кэша в `package.json`:**
454: 
455: ```json
456: {
457:   "scripts": {
458:     "build": "next build",
459:     "start": "next start",
460:     "postbuild": "node scripts/cache-warmer.js"
461:   }
462: }
463: ```
464: 
465: ### 2.3. Оптимизация загрузки клиентских компонентов
466: 
467: **Использование динамического импорта для разделения кода:**
468: 
469: ```javascript
470: // app/components/HeavyComponent.js
471: 'use client';
472: 
473: import dynamic from 'next/dynamic';
474: import { useState } from 'react';
475: 
476: // Динамический импорт тяжелого компонента
477: const HeavyChart = dynamic(() => import('./HeavyChart'), {
478:   loading: () => <p>Загрузка графика...</p>,
479:   ssr: false, // Отключение SSR для компонента, который требуется только на клиенте
480: });
481: 
482: export default function HeavyComponent() {
483:   const [showChart, setShowChart] = useState(false);
484:   
485:   return (
486:     <div>
487:       <button onClick={() => setShowChart(!showChart)}>
488:         {showChart ? 'Скрыть график' : 'Показать график'}
489:       </button>
490:       
491:       {showChart && <HeavyChart />}
492:     </div>
493:   );
494: }
495: ```
496: 
497: **Оптимизация изображений и медиаконтента:**
498: 
499: ```javascript
500: // app/components/OptimizedImage.js
501: import Image from 'next/image';
502: 
503: export default function OptimizedImage({ src, alt, ...props }) {
504:   return (
505:     <div className="image-container">
506:       <Image
507:         src={src}
508:         alt={alt}
509:         width={800}
510:         height={600}
511:         placeholder="blur" // Блюр во время загрузки
512:         blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFOwJC8S9NkgAAAABJRU5ErkJggg=="
513:         loading="lazy" // Ленивая загрузка для изображений
514:         quality={75} // Баланс между качеством и размером
515:         sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" // Респонсивные размеры
516:         {...props}
517:       />
518:     </div>
519:   );
520: }
521: ```
522: 
523: **Optimizing Font Loading:**
524: 
525: ```javascript
526: // app/layout.js
527: import { Inter } from 'next/font/google';
528: 
529: // Оптимизация загрузки шрифтов
530: const inter = Inter({
531:   subsets: ['latin'],
532:   display: 'swap', // Используется font-display: swap
533:   variable: '--font-inter', // Использование CSS-переменных
534:   preload: true, // Предзагрузка шрифта
535:   fallback: ['system-ui', 'sans-serif'], // Резервные шрифты
536: });
537: 
538: export default function RootLayout({ children }) {
539:   return (
540:     <html lang="ru" className={inter.variable}>
541:       <body>{children}</body>
542:     </html>
543:   );
544: }
545: ```
546: 
547: **Предварительная загрузка важных ресурсов:**
548: 
549: ```javascript
550: // app/layout.js
551: import { Suspense } from 'react';
552: import Script from 'next/script';
553: 
554: export default function RootLayout({ children }) {
555:   return (
556:     <html lang="ru">
557:       <head>
558:         {/* Предзагрузка критических ресурсов */}
559:         <link
560:           rel="preload"
561:           href="/fonts/custom-font.woff2"
562:           as="font"
563:           type="font/woff2"
564:           crossOrigin="anonymous"
565:         />
566:         
567:         {/* Предсоединение к важным доменам */}
568:         <link rel="preconnect" href="https://api.example.com" />
569:         <link rel="dns-prefetch" href="https://cdn.example.com" />
570:       </head>
571:       <body>
572:         <Suspense fallback={<p>Загрузка...</p>}>
573:           {children}
574:         </Suspense>
575:         
576:         {/* Отложенная загрузка некритичных скриптов */}
577:         <Script
578:           src="https://analytics.example.com/script.js"
579:           strategy="lazyOnload"
580:           onLoad={() => console.log('Аналитика загружена')}
581:         />
582:       </body>
583:     </html>
584:   );
585: }
586: ```
587: 
588: ## 3. Мониторинг и дополнительные оптимизации
589: 
590: ### 3.1. Настройка мониторинга производительности
591: 
592: **Использование Next.js Metrics и OpenTelemetry:**
593: 
594: ```javascript
595: // instrumentation.js
596: export async function register() {
597:   if (process.env.NEXT_RUNTIME === 'nodejs') {
598:     const { NodeSDK } = await import('@opentelemetry/sdk-node');
599:     const { OTLPTraceExporter } = await import('@opentelemetry/exporter-trace-otlp-http');
600:     const { Resource } = await import('@opentelemetry/resources');
601:     const { SemanticResourceAttributes } = await import('@opentelemetry/semantic-conventions');
602:     const { SimpleSpanProcessor } = await import('@opentelemetry/sdk-trace-base');
603:     
604:     const sdk = new NodeSDK({
605:       resource: new Resource({
606:         [SemanticResourceAttributes.SERVICE_NAME]: 'next-app',
607:         [SemanticResourceAttributes.SERVICE_VERSION]: '1.0.0',
608:       }),
609:       spanProcessor: new SimpleSpanProcessor(
610:         new OTLPTraceExporter({
611:           url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4318/v1/traces',
612:         })
613:       ),
614:     });
615:     
616:     sdk.start();
617:   }
618: }
619: ```
620: 
621: **Добавление клиентского мониторинга Web Vitals:**
622: 
623: ```javascript
624: // lib/analytics.js
625: export function reportWebVitals({ id, name, label, value }) {
626:   // Отправка метрик в аналитическую систему
627:   fetch('/api/analytics', {
628:     method: 'POST',
629:     headers: {
630:       'Content-Type': 'application/json',
631:     },
632:     body: JSON.stringify({
633:       id,
634:       name,
635:       label,
636:       value,
637:       timestamp: Date.now(),
638:     }),
639:   });
640: }
641: 
642: // app/layout.js
643: import { reportWebVitals } from '@/lib/analytics';
644: 
645: export function reportWebVitals(metric) {
646:   reportWebVitals(metric);
647: }
648: ```
649: 
650: ### 3.2. Дополнительные техники оптимизации
651: 
652: **Использование Server Components для уменьшения JavaScript на клиенте:**
653: 
654: ```javascript
655: // app/components/DataTable.js
656: // По умолчанию - Server Component
657: import { db } from '@/lib/db';
658: 
659: // Серверный компонент, который не отправляет JavaScript на клиент
660: export default async function DataTable({ query }) {
661:   // Данные загружаются на сервере
662:   const data = await db.query(query);
663:   
664:   return (
665:     <table>
666:       <thead>
667:         <tr>
668:           <th>ID</th>
669:           <th>Name</th>
670:           <th>Created</th>
671:         </tr>
672:       </thead>
673:       <tbody>
674:         {data.map((item) => (
675:           <tr key={item.id}>
676:             <td>{item.id}</td>
677:             <td>{item.name}</td>
678:             <td>{new Date(item.created).toLocaleDateString()}</td>
679:           </tr>
680:         ))}
681:       </tbody>
682:     </table>
683:   );
684: }
685: ```
686: 
687: **Реализация стратегии Progressive Hydration:**
688: 
689: ```javascript
690: // app/components/ProgressiveHydration.js
691: 'use client';
692: 
693: import { useEffect, useState } from 'react';
694: import { useInView } from 'react-intersection-observer';
695: 
696: export default function ProgressiveHydration({ children, rootMargin = '200px' }) {
697:   const [isClient, setIsClient] = useState(false);
698:   const { ref, inView } = useInView({
699:     triggerOnce: true,
700:     rootMargin,
701:   });
702:   
703:   // Определяем, выполняется ли код на клиенте
704:   useEffect(() => {
705:     setIsClient(true);
706:   }, []);
707:   
708:   // Если ещё не просматривается или не на клиенте,
709:   // рендерим только серверную версию
710:   if (!isClient || !inView) {
711:     return <div ref={ref}>{children}</div>;
712:   }
713:   
714:   // После гидратации возвращаем полный интерактивный компонент
715:   return <div>{children}</div>;
716: }
717: 
718: // Использование
719: import HeavyComponent from './HeavyComponent';
720: 
721: function HomePage() {
722:   return (
723:     <div>
724:       <h1>Главная страница</h1>
725:       
726:       {/* HeavyComponent будет гидратирован, только когда появится в области видимости */}
727:       <ProgressiveHydration>
728:         <HeavyComponent />
729:       </ProgressiveHydration>
730:     </div>
731:   );
732: }
733: ```
734: 
735: **Оптимизация с помощью React Streaming и Suspense:**
736: 
737: ```javascript
738: // app/page.js
739: import { Suspense } from 'react';
740: import PageHeader from './components/PageHeader';
741: import ProductList from './components/ProductList';
742: import RecommendedProducts from './components/RecommendedProducts';
743: 
744: export default function Page() {
745:   return (
746:     <div>
747:       {/* Будет рендериться сразу */}
748:       <PageHeader />
749:       
750:       {/* Блок для основного контента со скелетоном во время загрузки */}
751:       <Suspense fallback={<div className="skeleton-loader">Загрузка продуктов...</div>}>
752:         <ProductList />
753:       </Suspense>
754:       
755:       {/* Менее приоритетный контент с задержкой рендеринга */}
756:       <Suspense fallback={<div className="skeleton-loader">Загрузка рекомендаций...</div>}>
757:         <RecommendedProducts />
758:       </Suspense>
759:     </div>
760:   );
761: }
762: ```
763: 
764: ## Заключение
765: 
766: Оптимизация Next.js с гибридным рендерингом требует комплексного подхода, включающего в себя:
767: 
768: 1. **Серверную оптимизацию:**
769:    - Правильная настройка переменных окружения
770:    - Эффективное использование ISR для балансирования между статическим и динамическим контентом
771:    - Оптимизация Node.js в production-окружении
772: 
773: 2. **Настройку статического контента:**
774:    - Конфигурация Nginx для эффективной доставки статических ресурсов
775:    - Применение агрессивного кэширования для статических страниц и ресурсов
776:    - Оптимизация загрузки клиентских компонентов через разделение кода и ленивую загрузку
777: 
778: 3. **Дополнительные техники:**
779:    - Использование мониторинга производительности
780:    - Применение Server Components для уменьшения размера JavaScript-бандла
781:    - Внедрение прогрессивной гидратации и потокового рендеринга
782: 
783: Реализуя данные рекомендации, вы сможете значительно повысить производительность и отзывчивость вашего Next.js приложения, что положительно скажется на пользовательском опыте и SEO-показателях.
````

## File: 5. deployment-guide.md
````markdown
  1: # Упрощенное руководство по развертыванию React + Rust на s.quer.us
  2: 
  3: ## Содержание
  4: 1. [Обзор процесса](#обзор-процесса)
  5: 2. [Настройка SSH для удобного деплоя](#настройка-ssh-для-удобного-деплоя)
  6: 3. [Настройка Nginx](#настройка-nginx)
  7: 4. [Деплой React-фронтенда](#деплой-react-фронтенда)
  8: 5. [Деплой Rust-бэкенда](#деплой-rust-бэкенда)
  9: 6. [Проверка работоспособности](#проверка-работоспособности)
 10: 7. [Устранение неполадок](#устранение-неполадок)
 11: 
 12: ## Обзор процесса
 13: 
 14: Мы настраиваем простую staging-среду на s.quer.us для:
 15: - **React-фронтенда** (статические файлы, обслуживаемые Nginx)
 16: - **Rust-бэкенда** (API-сервис, запущенный через systemd)
 17: 
 18: ## Настройка SSH для удобного деплоя
 19: 
 20: Поскольку ваш сервер использует нестандартный порт и SSH-ключ с паролем, рекомендуется настроить SSH-агент для более удобного деплоя.
 21: 
 22: ### Шаг 1: Запуск SSH-агента и добавление ключа
 23: 
 24: ```bash
 25: # На локальном компьютере, в начале рабочего дня
 26: eval "$(ssh-agent -s)"
 27: ssh-add ~/.ssh/hetzner_ed25519
 28: # Введите пароль один раз, когда будет запрошено
 29: ```
 30: 
 31: После этого вы сможете использовать SSH и SCP без повторного ввода пароля в течение текущей сессии.
 32: 
 33: ### Шаг 2: Проверка подключения
 34: 
 35: ```bash
 36: # Используя ваш настроенный псевдоним
 37: ssh quer-x0 "echo 'SSH подключение работает!'"
 38: 
 39: # Или с полными параметрами (если псевдоним не настроен)
 40: # ssh -i ~/.ssh/hetzner_ed25519 -p 48735 x0@************* "echo 'SSH подключение работает!'"
 41: ```
 42: 
 43: ## Настройка Nginx
 44: 
 45: ### Шаг 1: Проверьте конфигурацию Nginx
 46: 
 47: ```bash
 48: # На сервере
 49: ssh quer-x0 "sudo nginx -t"
 50: ```
 51: 
 52: ### Шаг 2: Убедитесь, что Nginx настроен для обслуживания React-приложения
 53: 
 54: ```bash
 55: # Проверка конфигурации для s.quer.us
 56: ssh quer-x0 "sudo cat /etc/nginx/sites-available/s.quer.us.conf"
 57: ```
 58: 
 59: Блок для обслуживания статических файлов React должен выглядеть так:
 60: ```nginx
 61: location / {
 62:     auth_basic "Restricted Area";
 63:     auth_basic_user_file /etc/nginx/auth/.htpasswd;
 64:     root /var/www/s.quer.us/current/public;
 65:     try_files $uri $uri/ /index.html;
 66:     index index.html;
 67: }
 68: ```
 69: 
 70: ### Шаг 3: Если нужно, отредактируйте конфигурацию
 71: 
 72: ```bash
 73: # Подключитесь к серверу
 74: ssh quer-x0
 75: 
 76: # Затем отредактируйте файл на сервере
 77: sudo nano /etc/nginx/sites-available/s.quer.us.conf
 78: 
 79: # После внесения изменений проверьте и перезагрузите Nginx
 80: sudo nginx -t
 81: sudo systemctl reload nginx
 82: exit
 83: ```
 84: 
 85: #### Деплой React-фронтенда
 86: 
 87: ### Шаг 1: Подготовка директории на сервере (если еще не создана)
 88: 
 89: ```bash
 90: # На локальном компьютере
 91: ssh quer-x0 "mkdir -p /var/www/s.quer.us/current/public"
 92: ```
 93: 
 94: ### Шаг 2: Загрузка React-сборки на сервер
 95: 
 96: ```bash
 97: # На локальном компьютере, в директории проекта после выполнения npm run build
 98: scp -r $(find build -type f -not -name ".DS_Store") quer-x0:/var/www/s.quer.us/current/public/
 99: ```
100: 
101: ## Или еще проще:
102: 
103: # Перед копированием
104: find build -name ".DS_Store" -delete
105: # Затем обычное копирование
106: scp -r build/* quer-x0:/var/www/s.quer.us/current/public/
107: 
108: Это всё! Просто одна команда и ваш фронтенд обновлен.
109: 
110: #### Деплой Rust-бэкенда
111: 
112: ### Шаг 1: Подготовка директории на сервере (если еще не создана)
113: 
114: ```bash
115: # На локальном компьютере
116: ssh quer-x0 "mkdir -p /var/www/s.quer.us/current/backend-rust/target/release"
117: ```
118: 
119: ### Шаг 2: Загрузка бэкенда на сервер и перезапуск сервиса
120: 
121: ```bash
122: # На локальном компьютере, в директории проекта после сборки
123: # Загрузка исполняемого файла
124: scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/
125: 
126: # Установка прав и перезапуск сервиса
127: ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
128: ```
129: 
130: ## Или одной командой:
131: 
132: scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
133: 
134: Обнаружена проблема с запуском `sudo` через SSH. Когда вы запускаете `sudo` через SSH без терминала, система требует пароль, но не может его запросить.
135: 
136: Давайте исправим это, разделив команду на два шага или используя флаг `-t` для SSH:
137: 
138: ### Вариант 1: Использование флага -t для принудительного выделения терминала
139: 
140: ```bash
141: scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh -t quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
142: ```
143: 
144: Флаг `-t` заставляет SSH выделить псевдотерминал, что позволит `sudo` запросить пароль.
145: 
146: ### Вариант 2: Разбиение на два отдельных шага
147: 
148: ```bash
149: # Шаг 1: Копирование файла и установка прав
150: scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc"
151: 
152: # Шаг 2: Подключение и перезапуск сервиса
153: ssh quer-x0
154: # После подключения:
155: sudo systemctl restart quer-backend-staging.service
156: ```
157: 
158: ### Вариант 3 (постоянное решение): Настройка sudo без пароля для конкретной команды
159: 
160: На сервере можно настроить sudo, чтобы разрешить пользователю x0 перезапускать конкретный сервис без пароля:
161: 
162: ```bash
163: # Подключитесь к серверу
164: ssh quer-x0
165: 
166: # Отредактируйте файл sudoers
167: sudo visudo -f /etc/sudoers.d/x0-staging
168: 
169: # Добавьте следующую строку:
170: x0 ALL=(ALL) NOPASSWD: /bin/systemctl restart quer-backend-staging.service
171: ```
172: 
173: После этого вы сможете использовать исходную команду без запроса пароля.
174: 
175: ## Проверка работоспособности
176: 
177: ### Проверка фронтенда и бэкенда
178: 
179: Просто откройте сайт в браузере:
180: ```
181: https://s.quer.us
182: ```
183: 
184: Или выполните проверку через curl:
185: ```bash
186: # На локальном компьютере или сервере
187: curl -I https://s.quer.us
188: 
189: # Проверка бэкенда
190: curl -I https://s.quer.us/api/
191: ```
192: 
193: ## Устранение неполадок
194: 
195: ### Проблемы с SSH
196: 
197: ```bash
198: # Проверка статуса SSH-агента
199: ssh-add -l
200: 
201: # Если ключ отсутствует, добавьте его снова
202: ssh-add ~/.ssh/hetzner_ed25519
203: 
204: # Проверка подключения с явным указанием параметров
205: ssh -i ~/.ssh/hetzner_ed25519 -p 48735 x0@************* "echo 'Test connection'"
206: ```
207: 
208: ### Проблемы с Nginx
209: 
210: ```bash
211: # На сервере
212: ssh quer-x0 "sudo tail -f /var/www/s.quer.us/shared/logs/nginx-error.log"
213: ```
214: 
215: ### Проблемы с бэкендом
216: 
217: ```bash
218: # На сервере
219: ssh quer-x0 "sudo journalctl -u quer-backend-staging.service -n 50 --no-pager"
220: ```
221: 
222: ### Проблемы с правами доступа
223: 
224: ```bash
225: # На сервере
226: ssh quer-x0 "sudo chown -R x0:x0 /var/www/s.quer.us/"
227: ```
228: 
229: ### Отключение frontend-сервиса (если он еще активен)
230: 
231: ```bash
232: # На сервере
233: ssh quer-x0 "sudo systemctl disable quer-frontend-staging.service && sudo systemctl stop quer-frontend-staging.service"
234: ```
235: 
236: ---
237: 
238: ## Памятка: повседневный процесс деплоя
239: 
240: Для обычного обновления приложения после настройки SSH-агента вам достаточно выполнить всего две команды:
241: 
242: ```bash
243: # 0. Предварительно (один раз в начале рабочего дня)
244: eval "$(ssh-agent -s)"
245: ssh-add ~/.ssh/hetzner_ed25519
246: 
247: # 1. Деплой фронтенда (из директории проекта после npm run build)
248: scp -r $(find build -type f -not -name ".DS_Store") quer-x0:/var/www/s.quer.us/current/public/
249: 
250: # 2. Деплой бэкенда (из директории проекта после cargo build --release)
251: scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
252: ```
253: 
254: Всё! Простой и эффективный процесс деплоя без лишних усложнений.
````

## File: complete-security-guide.md
````markdown
   1: # Полная пошаговая инструкция по настройке безопасного сервера на Hetzner
   2: 
   3: ## Этап 1: Ручная первоначальная настройка на Hetzner (выполнено)
   4: 
   5: ### 1.1 Регистрация и настройка аккаунта
   6: 
   7: 1. **Создание аккаунта Hetzner**:
   8:    - Откройте браузер и перейдите на [https://console.hetzner.cloud](https://console.hetzner.cloud)
   9:    - Нажмите кнопку "Register" или "Sign up"
  10:    - Заполните свой email, пароль и другие требуемые данные
  11:    - Проверьте электронную почту для подтверждения регистрации
  12: 
  13: 2. **Активация двухфакторной аутентификации (2FA)** – очень рекомендуется:
  14:    - После входа в консоль Hetzner перейдите в "Security" (или "Безопасность")
  15:    - Включите 2FA, следуя инструкциям на экране
  16:    - Сохраните резервные коды в надежном месте
  17: 
  18: ### 1.2 Создание SSH-ключа на Mac (выполнено)
  19: 
  20: 1. **Откройте Terminal** на Mac (находится в папке Applications → Utilities или используйте поиск Spotlight, нажав Cmd+Space и набрав "Terminal")
  21: 
  22: 2. **Создайте SSH-ключ** (это как цифровой ключ для доступа к серверу):
  23:    ```bash
  24:    ssh-keygen -t ed25519 -b 4096 -C "ваш******************" -f ~/.ssh/hetzner_ed25519
  25:    ```
  26:    - Когда спросит "Enter file in which to save the key", просто нажмите Enter (чтобы использовать стандартный путь)
  27:    - Когда попросит ввести пароль, придумайте надежный пароль и запомните его!
  28:    - Пароль будет запрашиваться каждый раз при подключении к серверу
  29: 
  30: 3. **Скопируйте содержимое вашего публичного ключа**:
  31:    ```bash
  32:    cat ~/.ssh/id_ed25519.pub
  33:    ```
  34:    - Выделите и скопируйте весь вывод (он начинается с "ssh-ed25519" и заканчивается вашим email)
  35: 
  36: ### 1.3 Создание сервера на Hetzner (выполнено)
  37: 
  38: 1. **Войдите в консоль Hetzner** и выберите "Create server" или "Add server"
  39: 
  40: 2. **Выберите конфигурацию сервера**:
  41:    - **Локация**: Выберите ближайший к вам датацентр (например, Falkenstein или Helsinki для Европы)
  42:    - **Операционная система**: Ubuntu 22.04 LTS
  43:    - **Тип**: CX11 (1 vCPU, 2GB RAM) для начала – это самый базовый и дешевый вариант
  44:    - **Имя сервера**: Например, "quer-server"
  45: 
  46: 3. **Добавьте ваш SSH-ключ**:
  47:    - В разделе "SSH key" нажмите "Add SSH key"
  48:    - Вставьте скопированный ранее ключ
  49:    - Дайте ему название (например, "My Mac Key")
  50: 
  51: 4. **Дополнительные настройки**:
  52:    - Разверните секцию "Additional settings" или "User data"
  53:    - В поле "Cloud config" вставьте:
  54:      ```yaml
  55:      #cloud-config
  56:      package_update: true
  57:      package_upgrade: true
  58:      ```
  59:    - Это позволит автоматически обновить систему при создании сервера
  60: 
  61: 5. **Завершите создание сервера**:
  62:    - Внизу страницы нажмите "Create & Buy" или подобную кнопку
  63:    - Hetzner создаст сервер и выделит ему IP-адрес (это займет около 1-2 минут)
  64: 
  65: 6. **Сохраните информацию о сервере**:
  66:    - Запишите или скопируйте IP-адрес вашего сервера (выглядит как ***************)
  67:    - Эта информация будет нужна для подключения
  68: 
  69: ## Этап 2: Немедленные действия после создания сервера
  70: 
  71: ### 2.1 Первое подключение к серверу (выполнено)
  72: 
  73: 1. **Откройте новое окно Terminal** на Mac
  74: 
  75: 2. **Подключитесь к серверу** через SSH:
  76:    ```bash
  77:    ssh root@ВАШ_IP_АДРЕС
  78:    ```
  79:    - Замените ВАШ_IP_АДРЕС на IP, который вы скопировали с Hetzner
  80:    - При первом подключении появится предупреждение о том, что сервер не распознан. Введите "yes"
  81:    - Введите пароль от SSH-ключа, который вы создали ранее
  82: 
  83: 3. **Проверьте, что вы подключились**:
  84:    - В терминале должно отобразиться что-то вроде `root@quer-server:~#` – это означает, что вы успешно зашли на сервер
  85: 
  86: ### 2.2 Немедленное усиление безопасности (выполнено)
  87: 
  88: 1. **Обновите систему** (если не использовали автоматическое обновление при создании):
  89:    ```bash
  90:    apt update && apt upgrade -y
  91:    ```
  92:    - Подождите, пока система обновится (может занять несколько минут)
  93: 
  94: 2. **Установите необходимые базовые пакеты**:
  95:    ```bash
  96:    apt install -y ufw nano curl gnupg2
  97:    ```
  98:    - `ufw`: простой файрвол
  99:    - `nano`: простой текстовый редактор
 100:    - `curl` и `gnupg2`: для установки дополнительных репозиториев
 101: 
 102: 3. **Измените SSH-порт** (чтобы усложнить автоматический поиск):
 103:    ```bash
 104:    # Откройте файл конфигурации SSH
 105:    nano /etc/ssh/sshd_config
 106:    ```
 107:    
 108:    - Найдите строку, начинающуюся с `#Port 22` (используйте стрелки для навигации)
 109:    - Удалите символ `#` и замените `22` на `48735` (или другой порт по вашему выбору)
 110:    - Должно получиться `Port 48735`
 111:    - Для сохранения нажмите Ctrl+O, затем Enter
 112:    - Для выхода нажмите Ctrl+X
 113: 
 114: 4. **Настройте дополнительные параметры SSH** в том же файле:
 115:    - Найдите и измените следующие параметры (если строка начинается с #, удалите #):
 116:      - `PasswordAuthentication no` (запрет входа по паролю)
 117:      - `PermitRootLogin prohibit-password` (разрешает вход root только по ключу)
 118:    - Для сохранения нажмите Ctrl+O, затем Enter
 119:    - Для выхода нажмите Ctrl+X
 120: 
 121: 5. **Настройте файрвол (UFW)**:
 122:    ```bash
 123:    # Разрешаем новый SSH-порт
 124:    ufw allow 48735/tcp
 125:    
 126:    # Разрешаем HTTP и HTTPS для веб-сервера
 127:    ufw allow 80/tcp
 128:    ufw allow 443/tcp
 129:    
 130:    # Включаем файрвол
 131:    ufw enable
 132:    ```
 133:    - Когда спросит о включении, введите `y` и нажмите Enter
 134:    - Это разрешит подключения только к указанным портам и заблокирует все остальные
 135: 
 136: 6. **Перезапустите SSH-сервис**:
 137:    ```bash
 138:    systemctl restart ssh
 139:    ```
 140: 
 141: 7. **НЕ ЗАКРЫВАЙТЕ ТЕКУЩЕЕ ОКНО TERMINAL!** Вам нужно проверить, что новые настройки работают, прежде чем закрывать текущее подключение.
 142: 
 143: ### 2.3 Проверка новых настроек (выполнено)
 144: 
 145: 1. **Откройте второе окно Terminal** на Mac (не закрывая первое)
 146: 
 147: 2. **Попробуйте подключиться через новый порт**:
 148:    ```bash
 149:    ssh -p 48735 root@ВАШ_IP_АДРЕС
 150:    ```
 151:    - Введите пароль от SSH-ключа
 152:    - Если подключение успешно, значит новые настройки работают
 153:    - Если не удалось подключиться, вернитесь в первое окно и проверьте настройки
 154: 
 155: ### 2.4 Создание пользователей (выполнено)
 156: 
 157: 1. **Создайте администратора** (в любом из двух открытых терминалов):
 158:    ```bash
 159:    # Создание пользователя x0
 160:    adduser x0
 161:    ```
 162:    - Введите надежный пароль и запомните его
 163:    - На остальные запросы информации можно просто нажимать Enter
 164: 
 165: 2. **Добавьте пользователя x0 в группу sudo** (для прав администратора):
 166:    ```bash
 167:    usermod -aG sudo x0
 168:    ```
 169: 
 170: 3. **Настройте SSH-доступ для пользователя x0**:
 171:    ```bash
 172:    # Создаем директорию для SSH
 173:    mkdir -p /home/<USER>/.ssh
 174:    
 175:    # Копируем ваш публичный ключ
 176:    cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
 177:    
 178:    # Устанавливаем правильные права
 179:    chown -R x0:x0 /home/<USER>/.ssh
 180:    chmod 700 /home/<USER>/.ssh
 181:    chmod 600 /home/<USER>/.ssh/authorized_keys
 182:    ```
 183: 
 184: 4. **Создайте пользователя duploy** (для деплоя приложений, без прав администратора):
 185:    ```bash
 186:    # Создание пользователя duploy
 187:    adduser duploy
 188:    ```
 189:    - Введите другой надежный пароль и запомните его
 190:    - На остальные запросы информации можно просто нажимать Enter
 191: 
 192: 5. **Настройте SSH-доступ для пользователя duploy**:
 193:    ```bash
 194:    # Создаем директорию для SSH
 195:    mkdir -p /home/<USER>/.ssh
 196:    
 197:    # Копируем ваш публичный ключ
 198:    cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
 199:    
 200:    # Устанавливаем правильные права
 201:    chown -R duploy:duploy /home/<USER>/.ssh
 202:    chmod 700 /home/<USER>/.ssh
 203:    chmod 600 /home/<USER>/.ssh/authorized_keys
 204:    ```
 205: 
 206: 6. **Настройте группы пользователей для SSH**:
 207:    ```bash
 208:    # Создаем группу для SSH-доступа
 209:    groupadd sshusers
 210:    
 211:    # Добавляем пользователей в эту группу
 212:    usermod -aG sshusers x0
 213:    usermod -aG sshusers duploy
 214:    
 215:    # Открываем файл конфигурации SSH
 216:    nano /etc/ssh/sshd_config
 217:    ```
 218:    
 219:    - Добавьте в конец файла строку:
 220:      ```
 221:      AllowGroups sshusers
 222:      ```
 223:    - Для сохранения нажмите Ctrl+O, затем Enter
 224:    - Для выхода нажмите Ctrl+X
 225: 
 226: 7. **Перезапустите SSH-сервис**:
 227:    ```bash
 228:    systemctl restart sshd или systemctl restart ssh
 229:    ```
 230: 
 231: ### 2.5 Базовая настройка CrowdSec (выполнено)
 232: 
 233: 1. **Установите CrowdSec** (современная замена Fail2ban):
 234:    ```bash
 235:    # Добавляем репозиторий CrowdSec
 236:    curl -s https://packagecloud.io/install/repositories/crowdsec/crowdsec/script.deb.sh | bash
 237:    
 238:    # Устанавливаем CrowdSec и базовый bouncer для UFW
 239:    apt install -y crowdsec
 240:    apt install -y crowdsec-firewall-bouncer
 241:    ```
 242: 
 243: 2. **Установите основные коллекции для обнаружения угроз**:
 244:    ```bash
 245:    # Установка коллекций для обнаружения атак на SSH и Linux
 246:    cscli collections install crowdsecurity/sshd
 247:    cscli collections install crowdsecurity/linux
 248:    ```
 249: 
 250: 3. **Настройте мониторинг логов SSH**:
 251:    ```bash
 252:    # Проверяем, что лог SSH отслеживается
 253:    cscli parsers list | grep ssh
 254:    
 255:    # Проверяем, что CrowdSec обрабатывает нужные логи
 256:    cscli metrics или curl http://localhost:6060/metrics
 257:    ```
 258: 
 259: 4. **Перезапустите CrowdSec для применения настроек**:
 260:    ```bash
 261:    systemctl enable crowdsec
 262:    systemctl restart crowdsec
 263:    
 264:    # Перезапускаем bouncer для UFW
 265:    systemctl enable crowdsec-firewall-bouncer
 266:    systemctl restart crowdsec-firewall-bouncer
 267:    ```
 268: 
 269: 5. **Проверьте статус CrowdSec**:
 270:    ```bash
 271:    # Проверка статуса CrowdSec
 272:    systemctl status crowdsec
 273:    
 274:    # Проверка статуса блокировок
 275:    cscli decisions list
 276:    
 277:    # Проверка работы bouncers
 278:    cscli bouncers list
 279:    ```
 280:    - Вы должны увидеть активный bouncer для UFW
 281: 
 282: ### Pre 2.6 Отключение Root (выполнено)
 283: 
 284: # Инструкция по безопасному ограничению доступа к учетной записи root
 285: 
 286: Вместо удаления пользователя root (что не рекомендуется и может сломать систему), следуйте этим рекомендациям для максимального ограничения доступа:
 287: 
 288: ## Pre 2.6.1. Полностью запретите SSH-доступ для root
 289: 
 290: ```bash
 291: # Отредактируйте файл конфигурации SSH
 292: sudo nano /etc/ssh/sshd_config
 293: 
 294: # Найдите строку PermitRootLogin и измените на:
 295: PermitRootLogin no
 296: 
 297: # Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
 298: 
 299: # Перезапустите SSH-сервис
 300: sudo systemctl restart sshd
 301: ```
 302: 
 303: ## Pre 2.6.2. Заблокируйте пароль учетной записи root
 304: 
 305: ```bash
 306: # Это предотвратит вход под root через пароль, но сохранит возможность sudo
 307: sudo passwd -l root
 308: ```
 309: 
 310: ## Pre 2.6.3. Настройте более строгие правила sudo
 311: 
 312: ```bash
 313: # Создайте отдельный файл с ограничениями
 314: sudo nano /etc/sudoers.d/secure_sudo
 315: 
 316: # Добавьте эти строки, заменив 'x0' на имя вашего админ-пользователя
 317: x0 ALL=(ALL:ALL) ALL
 318: Defaults logfile="/var/log/sudo.log"
 319: Defaults lecture=always
 320: Defaults passwd_timeout=1
 321: Defaults timestamp_timeout=5
 322: Defaults badpass_message="Incorrect password! This action is logged."
 323: 
 324: # Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
 325: ```
 326: 
 327: ## Pre 2.6.4. Ограничьте использование команды su
 328: 
 329: ```bash
 330: # Установите необходимый пакет
 331: sudo apt install -y libpam-modules
 332: 
 333: # Отредактируйте PAM-конфигурацию su
 334: sudo nano /etc/pam.d/su
 335: 
 336: # Найдите и раскомментируйте (или добавьте) строку:
 337: auth required pam_wheel.so
 338: 
 339: # Создайте группу wheel и добавьте в неё только админ-пользователя
 340: sudo groupadd wheel
 341: sudo usermod -aG wheel x0
 342: 
 343: # Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
 344: ```
 345: 
 346: ## Pre 2.6.5. Установите автоматический выход из root-сессии по таймауту
 347: 
 348: ```bash
 349: # Отредактируйте файл .profile для root
 350: sudo nano /root/.profile
 351: 
 352: # Добавьте в конец файла:
 353: TMOUT=300
 354: readonly TMOUT
 355: export TMOUT
 356: 
 357: # Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
 358: ```
 359: 
 360: ## Pre 2.6.6. Настройте логирование всех действий с повышенными привилегиями
 361: 
 362: ```bash
 363: # Отредактируйте конфигурацию auditd
 364: sudo apt install -y auditd
 365: sudo nano /etc/audit/rules.d/audit.rules
 366: 
 367: # Добавьте в конец файла:
 368: -a always,exit -F arch=b64 -F euid=0 -S execve -k rootcmd
 369: -a always,exit -F arch=b32 -F euid=0 -S execve -k rootcmd
 370: 
 371: # Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
 372: 
 373: # Перезапустите сервис audit
 374: sudo systemctl restart auditd
 375: ```
 376: 
 377: ## Pre 2.6.7. Настройте уведомления о входе с правами суперпользователя
 378: 
 379: ```bash
 380: # Создайте скрипт уведомления
 381: sudo nano /etc/profile.d/sudo-alert.sh
 382: 
 383: # Добавьте в файл:
 384: if [ "$USER" = "root" ]; then
 385:     echo -e "\033[1;31mВНИМАНИЕ: You are logged in with superuser rights. (root)!\033[0m"
 386:     echo -e "\033[1;31mThis access is logged. Use with caution.\033[0m"
 387:     logger -p auth.notice -t security "ROOT LOGIN: tty: $(tty), User: $USER, PWD: $PWD"
 388: fi
 389: 
 390: # Сделайте файл исполняемым
 391: sudo chmod +x /etc/profile.d/sudo-alert.sh
 392: ```
 393: 
 394: ## Pre 2.6.8. Проверка настроек после внесения изменений
 395: 
 396: ```bash
 397: # Перезагрузите сервер
 398: sudo reboot
 399: 
 400: # После перезагрузки проверьте, что можете войти как x0
 401: ssh -p 48735 x0@ВАШ_IP_АДРЕС
 402: 
 403: # Проверьте, что sudo работает
 404: sudo -l
 405: 
 406: # Попробуйте войти как root - должно быть запрещено
 407: ssh -p 48735 root@ВАШ_IP_АДРЕС
 408: ```
 409: 
 410: ## Важные предупреждения:
 411: 
 412: 1. **Никогда не закрывайте текущую сессию** до проверки работоспособности новых настроек.
 413: 2. **Всегда имейте запасной метод доступа** (например, консольный доступ через панель управления Hetzner).
 414: 3. **В случае потери доступа к административному пользователю**, вы сможете восстановить доступ через консоль Hetzner.
 415: 4. **Периодически проверяйте файл `/var/log/sudo.log`** для мониторинга действий с повышенными привилегиями.
 416: 
 417: Эти меры обеспечат гораздо более высокий уровень безопасности, чем описанный в исходном документе, и при этом сохранят функциональность системы, в отличие от полного удаления пользователя root.
 418: 
 419: ### 2.6 Настройка конфигурации на вашем Mac (выполнено)
 420: 
 421: 1. **Создайте файл конфигурации SSH** на вашем Mac (в новом окне Terminal):
 422:    ```bash
 423:    nano ~/.ssh/config
 424:    ```
 425: 
 426: 2. **Добавьте следующую конфигурацию**:
 427:    ```
 428:    # Сервер quer.us - x0 доступ
 429:    Host quer-x0
 430:        HostName *************
 431:        Port 48735
 432:        User x0
 433:        IdentityFile ~/.ssh/hetzner_ed25519
 434:    
 435:    # Сервер quer.us - Duploy доступ
 436:    Host quer-duploy
 437:        HostName *************
 438:        Port 48735
 439:        User duploy
 440:        IdentityFile ~/.ssh/hetzner_ed25519
 441:    ```
 442:    - Замените ************* на реальный IP вашего сервера
 443:    - Для сохранения нажмите Ctrl+O, затем Enter
 444:    - Для выхода нажмите Ctrl+X
 445: 
 446: 3. **Проверьте подключение** с использованием новой конфигурации:
 447:    ```bash
 448:    # Подключение как admin
 449:    ssh -v quer-x0
 450:    
 451:    # После проверки выйдите командой 'exit' и попробуйте deploy
 452:    exit
 453:    
 454:    # Подключение как duploy
 455:    ssh quer-duploy
 456:    ```
 457:    - Для обоих подключений вас должны пустить без проблем
 458:    - Теперь вы можете легко подключаться, используя короткие команды
 459: 
 460: ## Этап 3: Дополнительные настройки безопасности
 461: 
 462: ### 3.0 Настройка Podman для контейнеризации (не выполнено)
 463: 
 464: 1. **Установите Podman**:
 465:    ```bash
 466:    sudo apt install -y podman
 467:    ```
 468: 
 469: 2. **Проверьте установку**:
 470:    ```bash
 471:    podman --version
 472:    ```
 473: 
 474: 3. **Настройте хранилище Podman**:
 475:    ```bash
 476:    # Создание директории для контейнеров
 477:    sudo mkdir -p /var/lib/containers
 478:    
 479:    # Проверка базовой конфигурации Podman
 480:    sudo podman info
 481:    ```
 482: 
 483: 4. **Создайте базовый контейнер для тестирования** (опционально):
 484:    ```bash
 485:    # Пример запуска тестового контейнера
 486:    podman run --rm docker.io/library/hello-world
 487:    ```
 488: 
 489: ### 3.1 Настройка безопасного хранения секретов (не выполнено)
 490: 
 491: 1. **Создайте безопасную директорию для секретов**:
 492:    ```bash
 493:    # Создаем директорию с ограниченными правами
 494:    sudo mkdir -p /etc/secrets
 495:    sudo chmod 700 /etc/secrets
 496:    ```
 497: 
 498: 2. **Создайте файл для хранения секретов с ограниченными правами**:
 499:    ```bash
 500:    # Создаем файл с секретами приложения
 501:    sudo touch /etc/secrets/app_secrets
 502:    sudo chmod 600 /etc/secrets/app_secrets
 503:    
 504:    # Устанавливаем владельца файла
 505:    sudo chown duploy:duploy /etc/secrets/app_secrets
 506:    ```
 507: 
 508: 3. **Создайте скрипт для безопасного чтения секретов**:
 509:    ```bash
 510:    sudo nano /usr/local/bin/read-secret
 511:    ```
 512:    
 513:    - Вставьте следующий код:
 514:    ```bash
 515:    #!/bin/bash
 516:    # Использование: read-secret KEY
 517:    # Возвращает значение из файла секретов
 518:    
 519:    if [ $# -ne 1 ]; then
 520:      echo "Usage: read-secret KEY" >&2
 521:      exit 1
 522:    fi
 523:    
 524:    KEY="$1"
 525:    FILE="/etc/secrets/app_secrets"
 526:    
 527:    if [ ! -f "$FILE" ]; then
 528:      echo "Secret file not found!" >&2
 529:      exit 2
 530:    fi
 531:    
 532:    grep "^$KEY=" "$FILE" | cut -d '=' -f2-
 533:    ```
 534:    
 535:    - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
 536:    
 537: 4. **Сделайте скрипт исполняемым**:
 538:    ```bash
 539:    sudo chmod +x /usr/local/bin/read-secret
 540:    ```
 541: 
 542: 5. **Пример добавления секрета**:
 543:    ```bash
 544:    # Добавление секрета в файл (замените переменные на свои)
 545:    echo "DB_PASSWORD=my_secure_password" | sudo tee -a /etc/secrets/app_secrets
 546:    ```
 547: 
 548: 6. **Пример чтения секрета**:
 549:    ```bash
 550:    # Чтение секрета из файла
 551:    read-secret DB_PASSWORD
 552:    ```
 553: 
 554: ### 3.1 Автоматические обновления безопасности (выполнено)
 555: 
 556: ## Настройка временной зоны
 557: sudo timedatectl set-timezone America/New_York
 558: 
 559: ## Установка и настройка NTP для синхронизации времени
 560: sudo apt install -y systemd-timesyncd
 561: sudo systemctl enable systemd-timesyncd
 562: sudo systemctl start systemd-timesyncd
 563: 
 564: ## Проверка статуса синхронизации
 565: sudo timedatectl status
 566: 
 567: 1. **Подключитесь к серверу как x0**:
 568:    ```bash
 569:    ssh -v quer-x0
 570:    ```
 571: 
 572: 2. **Установите необходимые пакеты**:
 573:    ```bash
 574:    sudo apt install -y unattended-upgrades apt-listchanges
 575:    ```
 576: 
 577: 3. **Настройте автоматические обновления**:
 578:    ```bash
 579:    sudo nano /etc/apt/apt.conf.d/20auto-upgrades
 580:    ```
 581:    
 582:    - Вставьте следующий текст:
 583:    ```
 584:    APT::Periodic::Update-Package-Lists "1";
 585:    APT::Periodic::Unattended-Upgrade "1";
 586:    APT::Periodic::Download-Upgradeable-Packages "1";
 587:    APT::Periodic::AutocleanInterval "7";
 588:    ```
 589:    - Для сохранения нажмите Ctrl+O, затем Enter
 590:    - Для выхода нажмите Ctrl+X
 591: 
 592: 4. **Настройте параметры автоматических обновлений**:
 593:    ```bash
 594:    sudo nano /etc/apt/apt.conf.d/50unattended-upgrades
 595:    ```
 596:    
 597:    - Найдите и раскомментируйте (удалите //) следующие строки:
 598:      - `"${distro_id}:${distro_codename}-updates";`
 599:      - `Unattended-Upgrade::Remove-Unused-Dependencies "true";`
 600:      - `Unattended-Upgrade::Automatic-Reboot "true";`
 601:      - `Unattended-Upgrade::Automatic-Reboot-Time "02:00";`
 602:    - Для сохранения нажмите Ctrl+O, затем Enter
 603:    - Для выхода нажмите Ctrl+X
 604: 
 605: 5. **Проверьте настройки**:
 606:    ```bash
 607:    sudo unattended-upgrade --dry-run --debug
 608:    ```
 609: 
 610: ### 3.2 Настройка базовой системы логирования (выполнено)
 611: 
 612: 1. **Установите инструменты для логирования**:
 613:    ```bash
 614:    sudo apt install -y logrotate
 615:    ```
 616: 
 617: 2. **Настройте ротацию логов системы**:
 618:    ```bash
 619:    sudo nano /etc/logrotate.d/server-logs
 620:    ```
 621:    
 622:    - Вставьте следующий текст:
 623:    ```
 624:    /var/log/syslog
 625:    /var/log/auth.log
 626:    /var/log/kern.log
 627:    /var/log/boot.log
 628:    /var/log/dpkg.log
 629:    /var/log/nginx/*.log
 630:    {
 631:        rotate 30
 632:        daily
 633:        missingok
 634:        notifempty
 635:        compress
 636:        delaycompress
 637:        sharedscripts
 638:        postrotate
 639:            systemctl reload rsyslog >/dev/null 2>&1 || true
 640:        endscript
 641:    }
 642:    ```
 643:    - Для сохранения нажмите Ctrl+O, затем Enter
 644:    - Для выхода нажмите Ctrl+X
 645: 
 646: 3. **Проверьте конфигурацию logrotate**:
 647:    ```bash
 648:    sudo logrotate -d /etc/logrotate.d/server-logs
 649:    ```
 650: 
 651: 4. **Настройте простой скрипт для проверки критических ошибок**:
 652:    ```bash
 653:    sudo nano /usr/local/bin/check-critical-logs
 654:    ```
 655:    
 656:    - Вставьте следующий текст:
 657:    ```bash
 658:    #!/bin/bash
 659:    
 660:    # Выводит критические ошибки за последние 24 часа
 661:    journalctl -p err..emerg --since "24 hours ago" | less
 662:    ```
 663:    
 664:    - Для сохранения нажмите Ctrl+O, затем Enter
 665:    - Для выхода нажмите Ctrl+X
 666: 
 667: 5. **Сделайте скрипт исполняемым**:
 668:    ```bash
 669:    sudo chmod +x /usr/local/bin/check-critical-logs
 670:    ```
 671: 
 672: Эта конфигурация обеспечивает:
 673: - Ротацию важных системных логов каждый день
 674: - Сжатие старых логов для экономии места
 675: - Хранение логов в течение 30 дней (оптимальный период для большинства стандартных серверов)
 676: - Удобный инструмент для ручной проверки критических ошибок при необходимости
 677: 
 678: Когда вы почувствуете, что что-то не так, вы можете просто запустить `sudo check-critical-logs`, чтобы быстро увидеть важные ошибки.
 679:  
 680: ### 3.2.1 Настройка уведомлений о критических ошибках через Telegram (выполнено)
 681: 
 682: Инструкция для настройки Telegram-уведомлений о критических ошибках:
 683: 
 684: 1. **Создайте бота в Telegram**:
 685:    - Откройте Telegram и найдите @BotFather
 686:    - Отправьте команду `/newbot`
 687:    - Следуйте инструкциям для создания бота
 688:    - Сохраните полученный токен (выглядит как `123456789:ABCdefGhIJKlmNoPQRsTUVwxyZ`)
 689:    - Отправьте сообщение своему новому боту и выполните следующие шаги
 690: 
 691: 2. **Узнайте ID вашего чата**:
 692:    - Отправьте сообщение боту `/start`
 693:    - Посетите URL в браузере (замените YOUR_BOT_TOKEN на токен вашего бота):
 694:      ```
 695:      https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates
 696:      ```
 697:    - Найдите значение `"id"` в поле `"chat"` и запишите его (это ваш chat_id)
 698: 
 699: 3. **Установите необходимые пакеты на сервере**:
 700:    ```bash
 701:    sudo apt install -y curl jq
 702:    ```
 703: 
 704: 4. **Создайте конфигурационный файл для бота**:
 705:    ```bash
 706:    sudo mkdir -p /etc/telegram-notify
 707:    sudo nano /etc/telegram-notify/config
 708:    ```
 709:    
 710:    - Вставьте следующий текст (замените значения):
 711:    ```
 712:    BOT_TOKEN="YOUR_BOT_TOKEN"
 713:    CHAT_ID="YOUR_CHAT_ID"
 714:    ```
 715:    - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
 716:    - Установите безопасные разрешения:
 717:    ```bash
 718:    sudo chmod 600 /etc/telegram-notify/config
 719:    ```
 720: 
 721: 5. **Создайте скрипт для отправки уведомлений**:
 722:    ```bash
 723:    sudo nano /usr/local/bin/telegram-notify
 724:    ```
 725:    
 726:    - Вставьте следующий код:
 727: #!/bin/bash
 728: 
 729: # Загрузка конфигурации
 730: source /etc/telegram-notify/config
 731: 
 732: # Функция для отправки сообщений
 733: send_message() {
 734:   MESSAGE="$1"
 735:   curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
 736:     -d chat_id="$CHAT_ID" \
 737:     -d text="$MESSAGE" \
 738:     -d parse_mode="HTML" > /dev/null
 739: }
 740: 
 741: # Получение имени хоста
 742: HOSTNAME=$(hostname)
 743: 
 744: # Получение критических ошибок за последние 24 часа
 745: ERRORS=$(journalctl -p err..emerg --since "24 hours ago" --no-pager)
 746: 
 747: # Проверка наличия ошибок
 748: if [ -n "$ERRORS" ]; then
 749:   # Подсчет количества ошибок
 750:   ERROR_COUNT=$(echo "$ERRORS" | wc -l)
 751:   
 752:   # Формирование сообщения
 753:   MESSAGE="⚠️ <b>ВНИМАНИЕ:</b> Обнаружено $ERROR_COUNT критических ошибок на сервере <b>$HOSTNAME</b>
 754: 
 755: Последние 10 ошибок:
 756: <pre>$(echo "$ERRORS" | tail -10)</pre>
 757: 
 758: $(date '+%d.%m.%Y %H:%M:%S')"
 759:   
 760:   # Отправка уведомления
 761:   send_message "$MESSAGE"
 762:   
 763:   logger -t telegram-notify "Отправлено уведомление о $ERROR_COUNT критических ошибках"
 764: else
 765:   logger -t telegram-notify "Критических ошибок не обнаружено"
 766: fi
 767:    
 768:    - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
 769: 
 770: 6. **Сделайте скрипт исполняемым**:
 771:    ```bash
 772:    sudo chmod +x /usr/local/bin/telegram-notify
 773:    ```
 774: 
 775: 7. **Добавьте задачу в cron для ежедневного выполнения**:
 776:    ```bash
 777:    sudo nano /etc/cron.d/error-notify
 778:    ```
 779:    
 780:    - Вставьте следующий текст:
 781:    ```
 782:    # Проверка критических ошибок каждый день в 08:00
 783:    0 8 * * * root /usr/local/bin/telegram-notify
 784:    ```
 785:    - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
 786: 
 787: 8. **Протестируйте работу скрипта**:
 788:    ```bash
 789:    sudo /usr/local/bin/telegram-notify
 790:    ```
 791:    
 792:    Вы должны получить сообщение в Telegram, если есть критические ошибки, или запись в логе, если их нет.
 793: 
 794: 9. **Проверьте запись в логе**:
 795:    ```bash
 796:    sudo grep telegram-notify /var/log/syslog
 797:    ```
 798: 
 799: Этот подход обеспечивает мгновенные уведомления о критических ошибках прямо в ваш Telegram, что удобнее, чем проверять электронную почту.
 800: 
 801: ### 3.3 Настройка Nginx с базовой безопасностью (не выполнено)
 802: 
 803: 1. **Установите Nginx**:
 804:    ```bash
 805:    sudo apt install -y nginx
 806:    ```
 807: 
 808: 2. **Создайте базовую конфигурацию безопасности**:
 809:    ```bash
 810:    sudo nano /etc/nginx/conf.d/security.conf
 811:    ```
 812:    
 813:    - Вставьте следующий текст:
 814:    ```
 815:    # Скрытие версии Nginx
 816:    server_tokens off;
 817:    
 818:    # Защита от XSS-атак
 819:    add_header X-XSS-Protection "1; mode=block";
 820:    
 821:    # Защита от clickjacking
 822:    add_header X-Frame-Options "SAMEORIGIN";
 823:    
 824:    # Защита от атак с изменением типа контента
 825:    add_header X-Content-Type-Options "nosniff";
 826:    ```
 827:    - Для сохранения нажмите Ctrl+O, затем Enter
 828:    - Для выхода нажмите Ctrl+X
 829: 
 830: 3. **Перезапустите Nginx**:
 831:    ```bash
 832:    sudo systemctl restart nginx
 833:    ```
 834: 
 835: ### 3.4 Настройка базовой защиты от сканирования портов (выполнено)
 836: 
 837: 1. **Установите psad для обнаружения сканирования портов**:
 838:    ```bash
 839:    sudo apt install -y psad
 840:    ```
 841: 
 842: 2. **Настройте базовую конфигурацию psad**:
 843:    ```bash
 844:    sudo nano /etc/psad/psad.conf
 845:    ```
 846:    
 847:    - Найдите и измените следующие параметры:
 848:      - `EMAIL_ADDRESSES ваш******************;` (замените на ваш email)
 849:      - `HOSTNAME quer-server;` (замените на имя вашего сервера)
 850:    - Для сохранения нажмите Ctrl+O, затем Enter
 851:    - Для выхода нажмите Ctrl+X
 852: 
 853: 3. **Перезапустите psad**:
 854:    ```bash
 855:    sudo systemctl restart psad
 856:    ```
 857: 
 858: # Настройка уведомлений PSAD через Telegram (выполнено)
 859: 
 860: ## Как настроить PSAD с уведомлениями через Telegram
 861: 
 862: Я заметил, что в вашей документации (complete-security-guide.md) уже есть раздел 3.2.1 с инструкцией для настройки уведомлений через Telegram для критических ошибок. Давайте адаптируем этот подход для PSAD:
 863: 
 864: 1. **Создайте конфигурацию Telegram** (если еще не создали):
 865:    ```bash
 866:    sudo mkdir -p /etc/telegram-notify
 867:    sudo nano /etc/telegram-notify/config
 868:    ```
 869: 
 870:    Содержимое (замените на свои значения):
 871:    ```
 872:    BOT_TOKEN="YOUR_BOT_TOKEN"
 873:    CHAT_ID="YOUR_CHAT_ID"
 874:    ```
 875: 
 876: 2. **Создайте скрипт для PSAD-уведомлений**:
 877:    ```bash
 878:    sudo nano /usr/local/bin/psad-telegram-notify
 879:    ```
 880: 
 881:    Вставьте следующий код:
 882:    ```bash
 883:    #!/bin/bash
 884: 
 885:    # Загрузка конфигурации Telegram
 886:    source /etc/telegram-notify/config
 887: 
 888:    # Функция для отправки сообщений в Telegram
 889:    send_message() {
 890:      MESSAGE="$1"
 891:      curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
 892:        -d chat_id="$CHAT_ID" \
 893:        -d text="$MESSAGE" \
 894:        -d parse_mode="HTML" > /dev/null
 895:    }
 896: 
 897:    # Получение имени хоста
 898:    HOSTNAME=$(hostname)
 899: 
 900:    # Получаем информацию от PSAD
 901:    PSAD_INFO=$(sudo psad --Status | grep -A 20 "DANGER LEVEL")
 902: 
 903:    # Проверка наличия подозрительной активности
 904:    if [[ "$PSAD_INFO" == *"DANGER LEVEL 3"* || "$PSAD_INFO" == *"DANGER LEVEL 4"* || "$PSAD_INFO" == *"DANGER LEVEL 5"* ]]; then
 905:      # Формирование сообщения
 906:      MESSAGE="🚨 <b>ВНИМАНИЕ:</b> PSAD обнаружил подозрительную активность на сервере <b>$HOSTNAME</b>
 907: 
 908:    <pre>$PSAD_INFO</pre>
 909: 
 910:    $(date '+%d.%m.%Y %H:%M:%S')"
 911:      
 912:      # Отправка уведомления
 913:      send_message "$MESSAGE"
 914:      
 915:      logger -t psad-notify "Отправлено уведомление о подозрительной активности в Telegram"
 916:    else
 917:      logger -t psad-notify "Подозрительной активности не обнаружено"
 918:    fi
 919:    ```
 920: 
 921: 3. **Сделайте скрипт исполняемым**:
 922:    ```bash
 923:    sudo chmod +x /usr/local/bin/psad-telegram-notify
 924:    ```
 925: 
 926: 4. **Отключите email-уведомления в PSAD**:
 927:    ```bash
 928:    sudo nano /etc/psad/psad.conf
 929:    ```
 930:    
 931:    Найдите и измените:
 932:    ```
 933:    EMAIL_ADDRESSES             none;
 934:    ```
 935: 
 936: 5. **Добавьте проверку через cron**:
 937:    ```bash
 938:    sudo nano /etc/cron.d/psad-check
 939:    ```
 940:    
 941:    Добавьте:
 942:    ```
 943:    # Проверка PSAD каждые 10 минут
 944:    */10 * * * * root /usr/local/bin/psad-telegram-notify
 945:    ```
 946: 
 947: 6. **Протестируйте работу скрипта**:
 948:    ```bash
 949:    sudo /usr/local/bin/psad-telegram-notify
 950:    ```
 951: 
 952: Этот подход намного практичнее для личного использования, чем настройка полноценного почтового сервера. Многие администраторы сейчас переходят на мессенджеры для уведомлений, так что ваш подход абсолютно правильный!
 953: 
 954: ### 3.5 Базовая настройка бэкапов (не выполнено)
 955: 
 956: 1. **Создайте директории для бэкапов**:
 957:    ```bash
 958:    sudo mkdir -p /var/backups/quer-calc/daily
 959:    sudo chmod -R 700 /var/backups
 960:    ```
 961: 
 962: 2. **Создайте простой скрипт бэкапа**:
 963:    ```bash
 964:    sudo nano /usr/local/bin/backup-server
 965:    ```
 966:    
 967:    - Вставьте следующий текст:
 968:    ```bash
 969:    #!/bin/bash
 970:    
 971:    # Переменные
 972:    DATE=$(date +"%Y-%m-%d")
 973:    BACKUP_DIR="/var/backups/quer-calc/daily"
 974:    APP_DIR="/var/www/quer-calc"
 975:    
 976:    # Создание бэкапа приложения (если оно уже установлено)
 977:    if [ -d "$APP_DIR" ]; then
 978:      mkdir -p "$BACKUP_DIR/app-$DATE"
 979:      rsync -a "$APP_DIR/" "$BACKUP_DIR/app-$DATE/"
 980:    fi
 981:    
 982:    # Бэкап конфигураций
 983:    mkdir -p "$BACKUP_DIR/config-$DATE"
 984:    cp -r /etc/nginx /etc/ssh /etc/fail2ban "$BACKUP_DIR/config-$DATE/"
 985:    
 986:    # Удаление старых бэкапов (старше 7 дней)
 987:    find "$BACKUP_DIR" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null
 988:    ```
 989:    - Для сохранения нажмите Ctrl+O, затем Enter
 990:    - Для выхода нажмите Ctrl+X
 991: 
 992: 3. **Сделайте скрипт исполняемым**:
 993:    ```bash
 994:    sudo chmod +x /usr/local/bin/backup-server
 995:    ```
 996: 
 997: 4. **Добавьте задачу в cron для ежедневного запуска**:
 998:    ```bash
 999:    sudo nano /etc/cron.d/server-backup
1000:    ```
1001:    
1002:    - Вставьте следующий текст:
1003:    ```
1004:    # Запуск ежедневного бэкапа в 01:00
1005:    0 1 * * * root /usr/local/bin/backup-server
1006:    ```
1007:    - Для сохранения нажмите Ctrl+O, затем Enter
1008:    - Для выхода нажмите Ctrl+X
1009: 
1010: ### 3.7 Настройка автоматических перезапусков приложения (не выполнено)
1011: 
1012: 1. **Создайте systemd-сервис для вашего приложения** (пример для Node.js):
1013:    ```bash
1014:    sudo nano /etc/systemd/system/quer-app.service
1015:    ```
1016:    
1017:    - Вставьте следующий код (настройте пути в соответствии с вашим приложением):
1018:    ```
1019:    [Unit]
1020:    Description=Quer App Service
1021:    After=network.target
1022:    
1023:    [Service]
1024:    Type=simple
1025:    User=deploy
1026:    WorkingDirectory=/var/www/quer-calc/production
1027:    ExecStart=/usr/bin/node /var/www/quer-calc/production/index.js
1028:    Restart=always
1029:    RestartSec=3
1030:    Environment=NODE_ENV=production
1031:    # Добавление переменной для чтения секретов
1032:    ExecStartPre=/bin/bash -c 'export $(cat /etc/secrets/app_secrets | xargs)'
1033:    
1034:    [Install]
1035:    WantedBy=multi-user.target
1036:    ```
1037:    
1038:    - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
1039: 
1040: 2. **Включите и запустите сервис**:
1041:    ```bash
1042:    sudo systemctl daemon-reload
1043:    sudo systemctl enable quer-app.service
1044:    sudo systemctl start quer-app.service
1045:    ```
1046: 
1047: 3. **Проверьте статус сервиса**:
1048:    ```bash
1049:    sudo systemctl status quer-app.service
1050:    ```
1051: 
1052: 4. **Настройте systemd-сервис для бэкенда на Rust** (опционально):
1053:    ```bash
1054:    sudo nano /etc/systemd/system/quer-backend.service
1055:    ```
1056:    
1057:    - Вставьте следующий код:
1058:    ```
1059:    [Unit]
1060:    Description=Quer Backend Service
1061:    After=network.target
1062:    
1063:    [Service]
1064:    Type=simple
1065:    User=deploy
1066:    WorkingDirectory=/var/www/quer-calc/production/backend-rust
1067:    ExecStart=/var/www/quer-calc/production/backend-rust/target/release/backend-rust
1068:    Restart=always
1069:    RestartSec=3
1070:    Environment=RUST_ENV=production
1071:    # Добавление переменной для чтения секретов
1072:    ExecStartPre=/bin/bash -c 'export $(cat /etc/secrets/app_secrets | xargs)'
1073:    
1074:    [Install]
1075:    WantedBy=multi-user.target
1076:    ```
1077:    
1078:    - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
1079:    
1080: 5. **Включите и запустите сервис**:
1081:    ```bash
1082:    sudo systemctl daemon-reload
1083:    sudo systemctl enable quer-backend.service
1084:    sudo systemctl start quer-backend.service
1085:    ```
1086: 
1087: ### 3.8 Аудит безопасности системы (выполнено)
1088: 
1089: 1. **Установите инструменты аудита**:
1090:    ```bash
1091:    sudo apt install -y lynis
1092:    ```
1093: 
1094: 2. **Запустите базовый аудит**:
1095:    ```bash
1096:    sudo lynis audit system
1097:    ```
1098:    - Изучите результаты и рекомендации
1099:    - Исправьте наиболее критичные проблемы, если они указаны
1100: 
1101: 3. **Настройте регулярное сканирование**:
1102:    ```bash
1103:    sudo nano /etc/cron.weekly/security-audit
1104:    ```
1105:    
1106:    - Вставьте следующий текст:
1107:    ```bash
1108:    #!/bin/bash
1109:    /usr/bin/lynis audit system --quick
1110:    ```
1111:    - Для сохранения нажмите Ctrl+O, затем Enter
1112:    - Для выхода нажмите Ctrl+X
1113: 
1114: 4. **Сделайте скрипт исполняемым**:
1115:    ```bash
1116:    sudo chmod +x /etc/cron.weekly/security-audit
1117:    ```
1118: 
1119: ## Финальные проверки и настройка SSL
1120: 
1121: ### 1. Проверка статуса служб
1122: 
1123: ```bash
1124: # Проверка статуса UFW
1125: sudo ufw status
1126: 
1127: # Проверка статуса CrowdSec
1128: sudo cscli metrics
1129: sudo cscli decisions list
1130: 
1131: # Проверка открытых портов
1132: sudo ss -tulpn
1133: ```
1134: 
1135: ### 2. Базовая настройка SSL с Let's Encrypt (если у вас есть домен) (не выполнено)
1136: 
1137: 1. **Установите certbot**:
1138:    ```bash
1139:    sudo apt install -y certbot python3-certbot-nginx
1140:    ```
1141: 
1142: 2. **Получите SSL-сертификат** (замените quer.us на ваш домен):
1143:    ```bash
1144:    sudo certbot --nginx -d quer.us -d www.quer.us
1145:    ```
1146:    - Следуйте инструкциям на экране
1147:    - Сертификат обновляется автоматически
1148: 
1149: ### 3. Завершающие действия
1150: 
1151: 1. **Перезагрузите сервер** для применения всех настроек:
1152:    ```bash
1153:    sudo reboot
1154:    ```
1155: 
1156: 2. **Проверьте подключение** после перезагрузки:
1157:    ```bash
1158:    # На вашем Mac
1159:    ssh -v quer-x0
1160:    ```
1161: 
1162: 3. **Проверьте логи на наличие ошибок**:
1163:    ```bash
1164:    sudo tail -100 /var/log/syslog
1165:    sudo tail -100 /var/log/auth.log
1166:    ```
1167: 
1168: ## Поздравляем!
1169: 
1170: Теперь у вас есть базовая, но достаточно безопасная настройка сервера Ubuntu на Hetzner. Ваш сервер защищен от большинства типовых атак:
1171: 
1172: - SSH доступен только через нестандартный порт и только с вашего ключа
1173: - Учетные записи разделены на административную (x0) и для деплоя (duploy)
1174: - Настроен файрвол, пропускающий только необходимые порты
1175: - Настроена защита от брутфорса с CrowdSec
1176: - Настроены автоматические обновления
1177: - Базовое логирование и бэкапы
1178: 
1179: Дальнейшие шаги могут включать настройку деплоя вашего приложения и более сложные инструменты безопасности, если они понадобятся.
````

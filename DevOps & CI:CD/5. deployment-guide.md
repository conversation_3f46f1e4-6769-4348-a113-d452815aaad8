# Упрощенное руководство по развертыванию React + Rust на s.quer.us

## Содержание
1. [Обзор процесса](#обзор-процесса)
2. [Настройка SSH для удобного деплоя](#настройка-ssh-для-удобного-деплоя)
3. [Настройка Nginx](#настройка-nginx)
4. [Деплой React-фронтенда](#деплой-react-фронтенда)
5. [Деплой Rust-бэкенда](#деплой-rust-бэкенда)
6. [Проверка работоспособности](#проверка-работоспособности)
7. [Устранение неполадок](#устранение-неполадок)

## Обзор процесса

Мы настраиваем простую staging-среду на s.quer.us для:
- **React-фронтенда** (статические файлы, обслуживаемые Nginx)
- **Rust-бэкенда** (API-сервис, запущенный через systemd)

## Настройка SSH для удобного деплоя

Поскольку ваш сервер использует нестандартный порт и SSH-ключ с паролем, рекомендуется настроить SSH-агент для более удобного деплоя.

### Шаг 1: Запуск SSH-агента и добавление ключа

```bash
# На локальном компьютере, в начале рабочего дня
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/hetzner_ed25519
# Введите пароль один раз, когда будет запрошено
```

После этого вы сможете использовать SSH и SCP без повторного ввода пароля в течение текущей сессии.

### Шаг 2: Проверка подключения

```bash
# Используя ваш настроенный псевдоним
ssh quer-x0 "echo 'SSH подключение работает!'"

# Или с полными параметрами (если псевдоним не настроен)
# ssh -i ~/.ssh/hetzner_ed25519 -p 48735 x0@138.199.175.5 "echo 'SSH подключение работает!'"
```

## Настройка Nginx

### Шаг 1: Проверьте конфигурацию Nginx

```bash
# На сервере
ssh quer-x0 "sudo nginx -t"
```

### Шаг 2: Убедитесь, что Nginx настроен для обслуживания React-приложения

```bash
# Проверка конфигурации для s.quer.us
ssh quer-x0 "sudo cat /etc/nginx/sites-available/s.quer.us.conf"
```

Блок для обслуживания статических файлов React должен выглядеть так:
```nginx
location / {
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/auth/.htpasswd;
    root /var/www/s.quer.us/current/public;
    try_files $uri $uri/ /index.html;
    index index.html;
}
```

### Шаг 3: Если нужно, отредактируйте конфигурацию

```bash
# Подключитесь к серверу
ssh quer-x0

# Затем отредактируйте файл на сервере
sudo nano /etc/nginx/sites-available/s.quer.us.conf

# После внесения изменений проверьте и перезагрузите Nginx
sudo nginx -t
sudo systemctl reload nginx
exit
```

#### Деплой React-фронтенда

### Шаг 1: Подготовка директории на сервере (если еще не создана)

```bash
# На локальном компьютере
ssh quer-x0 "mkdir -p /var/www/s.quer.us/current/public"
```

### Шаг 2: Загрузка React-сборки на сервер

```bash
# На локальном компьютере, в директории проекта после выполнения npm run build
scp -r $(find build -type f -not -name ".DS_Store") quer-x0:/var/www/s.quer.us/current/public/
```

## Или еще проще:

# Перед копированием
find build -name ".DS_Store" -delete
# Затем обычное копирование
scp -r build/* quer-x0:/var/www/s.quer.us/current/public/

Это всё! Просто одна команда и ваш фронтенд обновлен.

#### Деплой Rust-бэкенда

### Шаг 1: Подготовка директории на сервере (если еще не создана)

```bash
# На локальном компьютере
ssh quer-x0 "mkdir -p /var/www/s.quer.us/current/backend-rust/target/release"
```

### Шаг 2: Загрузка бэкенда на сервер и перезапуск сервиса

```bash
# На локальном компьютере, в директории проекта после сборки
# Загрузка исполняемого файла
scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/

# Установка прав и перезапуск сервиса
ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
```

## Или одной командой:

scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"

Обнаружена проблема с запуском `sudo` через SSH. Когда вы запускаете `sudo` через SSH без терминала, система требует пароль, но не может его запросить.

Давайте исправим это, разделив команду на два шага или используя флаг `-t` для SSH:

### Вариант 1: Использование флага -t для принудительного выделения терминала

```bash
scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh -t quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
```

Флаг `-t` заставляет SSH выделить псевдотерминал, что позволит `sudo` запросить пароль.

### Вариант 2: Разбиение на два отдельных шага

```bash
# Шаг 1: Копирование файла и установка прав
scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc"

# Шаг 2: Подключение и перезапуск сервиса
ssh quer-x0
# После подключения:
sudo systemctl restart quer-backend-staging.service
```

### Вариант 3 (постоянное решение): Настройка sudo без пароля для конкретной команды

На сервере можно настроить sudo, чтобы разрешить пользователю x0 перезапускать конкретный сервис без пароля:

```bash
# Подключитесь к серверу
ssh quer-x0

# Отредактируйте файл sudoers
sudo visudo -f /etc/sudoers.d/x0-staging

# Добавьте следующую строку:
x0 ALL=(ALL) NOPASSWD: /bin/systemctl restart quer-backend-staging.service
```

После этого вы сможете использовать исходную команду без запроса пароля.

## Проверка работоспособности

### Проверка фронтенда и бэкенда

Просто откройте сайт в браузере:
```
https://s.quer.us
```

Или выполните проверку через curl:
```bash
# На локальном компьютере или сервере
curl -I https://s.quer.us

# Проверка бэкенда
curl -I https://s.quer.us/api/
```

## Устранение неполадок

### Проблемы с SSH

```bash
# Проверка статуса SSH-агента
ssh-add -l

# Если ключ отсутствует, добавьте его снова
ssh-add ~/.ssh/hetzner_ed25519

# Проверка подключения с явным указанием параметров
ssh -i ~/.ssh/hetzner_ed25519 -p 48735 x0@138.199.175.5 "echo 'Test connection'"
```

### Проблемы с Nginx

```bash
# На сервере
ssh quer-x0 "sudo tail -f /var/www/s.quer.us/shared/logs/nginx-error.log"
```

### Проблемы с бэкендом

```bash
# На сервере
ssh quer-x0 "sudo journalctl -u quer-backend-staging.service -n 50 --no-pager"
```

### Проблемы с правами доступа

```bash
# На сервере
ssh quer-x0 "sudo chown -R x0:x0 /var/www/s.quer.us/"
```

### Отключение frontend-сервиса (если он еще активен)

```bash
# На сервере
ssh quer-x0 "sudo systemctl disable quer-frontend-staging.service && sudo systemctl stop quer-frontend-staging.service"
```

---

## Памятка: повседневный процесс деплоя

Для обычного обновления приложения после настройки SSH-агента вам достаточно выполнить всего две команды:

```bash
# 0. Предварительно (один раз в начале рабочего дня)
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/hetzner_ed25519

# 1. Деплой фронтенда (из директории проекта после npm run build)
scp -r $(find build -type f -not -name ".DS_Store") quer-x0:/var/www/s.quer.us/current/public/

# 2. Деплой бэкенда (из директории проекта после cargo build --release)
scp backend-rust/target/release/quer-calc quer-x0:/var/www/s.quer.us/current/backend-rust/target/release/ && ssh quer-x0 "chmod +x /var/www/s.quer.us/current/backend-rust/target/release/quer-calc && sudo systemctl restart quer-backend-staging.service"
```

Всё! Простой и эффективный процесс деплоя без лишних усложнений.
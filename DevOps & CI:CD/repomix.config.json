{"output": {"style": "markdown", "filePath": "repomix-output.md", "removeComments": true, "showLineNumbers": true, "topFilesLength": 200}, "ignore": {"customPatterns": ["repomix.config.json", "target/", "node_modules/", "public/", "assets/", "logo.svg", "Cargo.lock", "package-lock.json", "build/", "backend-source.tar.gz", ".git/", ".vscode/", "**/.vscode/**", "**/.g<PERSON><PERSON><PERSON>", "**/.git<PERSON><PERSON><PERSON>", "**/.gitattributes", "**/.env*", "**/config.*", "**/secrets.*", "**/private.*", "**/.DS_Store", "**/Thumbs.db", ".cursorignore", ".giti<PERSON>re", "**/*.svg", "README.md"]}}
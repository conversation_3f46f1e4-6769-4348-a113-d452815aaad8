# Полная пошаговая инструкция по настройке безопасного сервера на Hetzner

## Этап 1: Ручная первоначальная настройка на He<PERSON>ner (выполнено)

### 1.1 Регистрация и настройка аккаунта

1. **Создание аккаунта Hetzner**:
   - Откройте браузер и перейдите на [https://console.hetzner.cloud](https://console.hetzner.cloud)
   - Нажмите кнопку "Register" или "Sign up"
   - Заполните свой email, пароль и другие требуемые данные
   - Проверьте электронную почту для подтверждения регистрации

2. **Активация двухфакторной аутентификации (2FA)** – очень рекомендуется:
   - После входа в консоль Hetzner перейдите в "Security" (или "Безопасность")
   - Включите 2FA, следуя инструкциям на экране
   - Сохраните резервные коды в надежном месте

### 1.2 Создание SSH-ключа на Mac (выполнено)

1. **Откройте Terminal** на Mac (находится в папке Applications → Utilities или используйте поиск Spotlight, нажав Cmd+Space и набрав "Terminal")

2. **Создайте SSH-ключ** (это как цифровой ключ для доступа к серверу):
   ```bash
   ssh-keygen -t ed25519 -b 4096 -C "ваш******************" -f ~/.ssh/hetzner_ed25519
   ```
   - Когда спросит "Enter file in which to save the key", просто нажмите Enter (чтобы использовать стандартный путь)
   - Когда попросит ввести пароль, придумайте надежный пароль и запомните его!
   - Пароль будет запрашиваться каждый раз при подключении к серверу

3. **Скопируйте содержимое вашего публичного ключа**:
   ```bash
   cat ~/.ssh/id_ed25519.pub
   ```
   - Выделите и скопируйте весь вывод (он начинается с "ssh-ed25519" и заканчивается вашим email)

### 1.3 Создание сервера на Hetzner (выполнено)

1. **Войдите в консоль Hetzner** и выберите "Create server" или "Add server"

2. **Выберите конфигурацию сервера**:
   - **Локация**: Выберите ближайший к вам датацентр (например, Falkenstein или Helsinki для Европы)
   - **Операционная система**: Ubuntu 22.04 LTS
   - **Тип**: CX11 (1 vCPU, 2GB RAM) для начала – это самый базовый и дешевый вариант
   - **Имя сервера**: Например, "quer-server"

3. **Добавьте ваш SSH-ключ**:
   - В разделе "SSH key" нажмите "Add SSH key"
   - Вставьте скопированный ранее ключ
   - Дайте ему название (например, "My Mac Key")

4. **Дополнительные настройки**:
   - Разверните секцию "Additional settings" или "User data"
   - В поле "Cloud config" вставьте:
     ```yaml
     #cloud-config
     package_update: true
     package_upgrade: true
     ```
   - Это позволит автоматически обновить систему при создании сервера

5. **Завершите создание сервера**:
   - Внизу страницы нажмите "Create & Buy" или подобную кнопку
   - Hetzner создаст сервер и выделит ему IP-адрес (это займет около 1-2 минут)

6. **Сохраните информацию о сервере**:
   - Запишите или скопируйте IP-адрес вашего сервера (выглядит как ***************)
   - Эта информация будет нужна для подключения

## Этап 2: Немедленные действия после создания сервера

### 2.1 Первое подключение к серверу (выполнено)

1. **Откройте новое окно Terminal** на Mac

2. **Подключитесь к серверу** через SSH:
   ```bash
   ssh root@ВАШ_IP_АДРЕС
   ```
   - Замените ВАШ_IP_АДРЕС на IP, который вы скопировали с Hetzner
   - При первом подключении появится предупреждение о том, что сервер не распознан. Введите "yes"
   - Введите пароль от SSH-ключа, который вы создали ранее

3. **Проверьте, что вы подключились**:
   - В терминале должно отобразиться что-то вроде `root@quer-server:~#` – это означает, что вы успешно зашли на сервер

### 2.2 Немедленное усиление безопасности (выполнено)

1. **Обновите систему** (если не использовали автоматическое обновление при создании):
   ```bash
   apt update && apt upgrade -y
   ```
   - Подождите, пока система обновится (может занять несколько минут)

2. **Установите необходимые базовые пакеты**:
   ```bash
   apt install -y ufw nano curl gnupg2
   ```
   - `ufw`: простой файрвол
   - `nano`: простой текстовый редактор
   - `curl` и `gnupg2`: для установки дополнительных репозиториев

3. **Измените SSH-порт** (чтобы усложнить автоматический поиск):
   ```bash
   # Откройте файл конфигурации SSH
   nano /etc/ssh/sshd_config
   ```
   
   - Найдите строку, начинающуюся с `#Port 22` (используйте стрелки для навигации)
   - Удалите символ `#` и замените `22` на `48735` (или другой порт по вашему выбору)
   - Должно получиться `Port 48735`
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

4. **Настройте дополнительные параметры SSH** в том же файле:
   - Найдите и измените следующие параметры (если строка начинается с #, удалите #):
     - `PasswordAuthentication no` (запрет входа по паролю)
     - `PermitRootLogin prohibit-password` (разрешает вход root только по ключу)
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

5. **Настройте файрвол (UFW)**:
   ```bash
   # Разрешаем новый SSH-порт
   ufw allow 48735/tcp
   
   # Разрешаем HTTP и HTTPS для веб-сервера
   ufw allow 80/tcp
   ufw allow 443/tcp
   
   # Включаем файрвол
   ufw enable
   ```
   - Когда спросит о включении, введите `y` и нажмите Enter
   - Это разрешит подключения только к указанным портам и заблокирует все остальные

6. **Перезапустите SSH-сервис**:
   ```bash
   systemctl restart ssh
   ```

7. **НЕ ЗАКРЫВАЙТЕ ТЕКУЩЕЕ ОКНО TERMINAL!** Вам нужно проверить, что новые настройки работают, прежде чем закрывать текущее подключение.

### 2.3 Проверка новых настроек (выполнено)

1. **Откройте второе окно Terminal** на Mac (не закрывая первое)

2. **Попробуйте подключиться через новый порт**:
   ```bash
   ssh -p 48735 root@ВАШ_IP_АДРЕС
   ```
   - Введите пароль от SSH-ключа
   - Если подключение успешно, значит новые настройки работают
   - Если не удалось подключиться, вернитесь в первое окно и проверьте настройки

### 2.4 Создание пользователей (выполнено)

1. **Создайте администратора** (в любом из двух открытых терминалов):
   ```bash
   # Создание пользователя x0
   adduser x0
   ```
   - Введите надежный пароль и запомните его
   - На остальные запросы информации можно просто нажимать Enter

2. **Добавьте пользователя x0 в группу sudo** (для прав администратора):
   ```bash
   usermod -aG sudo x0
   ```

3. **Настройте SSH-доступ для пользователя x0**:
   ```bash
   # Создаем директорию для SSH
   mkdir -p /home/<USER>/.ssh
   
   # Копируем ваш публичный ключ
   cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
   
   # Устанавливаем правильные права
   chown -R x0:x0 /home/<USER>/.ssh
   chmod 700 /home/<USER>/.ssh
   chmod 600 /home/<USER>/.ssh/authorized_keys
   ```

4. **Создайте пользователя duploy** (для деплоя приложений, без прав администратора):
   ```bash
   # Создание пользователя duploy
   adduser duploy
   ```
   - Введите другой надежный пароль и запомните его
   - На остальные запросы информации можно просто нажимать Enter

5. **Настройте SSH-доступ для пользователя duploy**:
   ```bash
   # Создаем директорию для SSH
   mkdir -p /home/<USER>/.ssh
   
   # Копируем ваш публичный ключ
   cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
   
   # Устанавливаем правильные права
   chown -R duploy:duploy /home/<USER>/.ssh
   chmod 700 /home/<USER>/.ssh
   chmod 600 /home/<USER>/.ssh/authorized_keys
   ```

6. **Настройте группы пользователей для SSH**:
   ```bash
   # Создаем группу для SSH-доступа
   groupadd sshusers
   
   # Добавляем пользователей в эту группу
   usermod -aG sshusers x0
   usermod -aG sshusers duploy
   
   # Открываем файл конфигурации SSH
   nano /etc/ssh/sshd_config
   ```
   
   - Добавьте в конец файла строку:
     ```
     AllowGroups sshusers
     ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

7. **Перезапустите SSH-сервис**:
   ```bash
   systemctl restart sshd или systemctl restart ssh
   ```

### 2.5 Базовая настройка CrowdSec (выполнено)

1. **Установите CrowdSec** (современная замена Fail2ban):
   ```bash
   # Добавляем репозиторий CrowdSec
   curl -s https://packagecloud.io/install/repositories/crowdsec/crowdsec/script.deb.sh | bash
   
   # Устанавливаем CrowdSec и базовый bouncer для UFW
   apt install -y crowdsec
   apt install -y crowdsec-firewall-bouncer
   ```

2. **Установите основные коллекции для обнаружения угроз**:
   ```bash
   # Установка коллекций для обнаружения атак на SSH и Linux
   cscli collections install crowdsecurity/sshd
   cscli collections install crowdsecurity/linux
   ```

3. **Настройте мониторинг логов SSH**:
   ```bash
   # Проверяем, что лог SSH отслеживается
   cscli parsers list | grep ssh
   
   # Проверяем, что CrowdSec обрабатывает нужные логи
   cscli metrics или curl http://localhost:6060/metrics
   ```

4. **Перезапустите CrowdSec для применения настроек**:
   ```bash
   systemctl enable crowdsec
   systemctl restart crowdsec
   
   # Перезапускаем bouncer для UFW
   systemctl enable crowdsec-firewall-bouncer
   systemctl restart crowdsec-firewall-bouncer
   ```

5. **Проверьте статус CrowdSec**:
   ```bash
   # Проверка статуса CrowdSec
   systemctl status crowdsec
   
   # Проверка статуса блокировок
   cscli decisions list
   
   # Проверка работы bouncers
   cscli bouncers list
   ```
   - Вы должны увидеть активный bouncer для UFW

### Pre 2.6 Отключение Root (выполнено)

# Инструкция по безопасному ограничению доступа к учетной записи root

Вместо удаления пользователя root (что не рекомендуется и может сломать систему), следуйте этим рекомендациям для максимального ограничения доступа:

## Pre 2.6.1. Полностью запретите SSH-доступ для root

```bash
# Отредактируйте файл конфигурации SSH
sudo nano /etc/ssh/sshd_config

# Найдите строку PermitRootLogin и измените на:
PermitRootLogin no

# Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)

# Перезапустите SSH-сервис
sudo systemctl restart sshd
```

## Pre 2.6.2. Заблокируйте пароль учетной записи root

```bash
# Это предотвратит вход под root через пароль, но сохранит возможность sudo
sudo passwd -l root
```

## Pre 2.6.3. Настройте более строгие правила sudo

```bash
# Создайте отдельный файл с ограничениями
sudo nano /etc/sudoers.d/secure_sudo

# Добавьте эти строки, заменив 'x0' на имя вашего админ-пользователя
x0 ALL=(ALL:ALL) ALL
Defaults logfile="/var/log/sudo.log"
Defaults lecture=always
Defaults passwd_timeout=1
Defaults timestamp_timeout=5
Defaults badpass_message="Incorrect password! This action is logged."

# Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
```

## Pre 2.6.4. Ограничьте использование команды su

```bash
# Установите необходимый пакет
sudo apt install -y libpam-modules

# Отредактируйте PAM-конфигурацию su
sudo nano /etc/pam.d/su

# Найдите и раскомментируйте (или добавьте) строку:
auth required pam_wheel.so

# Создайте группу wheel и добавьте в неё только админ-пользователя
sudo groupadd wheel
sudo usermod -aG wheel x0

# Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
```

## Pre 2.6.5. Установите автоматический выход из root-сессии по таймауту

```bash
# Отредактируйте файл .profile для root
sudo nano /root/.profile

# Добавьте в конец файла:
TMOUT=300
readonly TMOUT
export TMOUT

# Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)
```

## Pre 2.6.6. Настройте логирование всех действий с повышенными привилегиями

```bash
# Отредактируйте конфигурацию auditd
sudo apt install -y auditd
sudo nano /etc/audit/rules.d/audit.rules

# Добавьте в конец файла:
-a always,exit -F arch=b64 -F euid=0 -S execve -k rootcmd
-a always,exit -F arch=b32 -F euid=0 -S execve -k rootcmd

# Сохраните файл (Ctrl+O, Enter, затем Ctrl+X)

# Перезапустите сервис audit
sudo systemctl restart auditd
```

## Pre 2.6.7. Настройте уведомления о входе с правами суперпользователя

```bash
# Создайте скрипт уведомления
sudo nano /etc/profile.d/sudo-alert.sh

# Добавьте в файл:
if [ "$USER" = "root" ]; then
    echo -e "\033[1;31mВНИМАНИЕ: You are logged in with superuser rights. (root)!\033[0m"
    echo -e "\033[1;31mThis access is logged. Use with caution.\033[0m"
    logger -p auth.notice -t security "ROOT LOGIN: tty: $(tty), User: $USER, PWD: $PWD"
fi

# Сделайте файл исполняемым
sudo chmod +x /etc/profile.d/sudo-alert.sh
```

## Pre 2.6.8. Проверка настроек после внесения изменений

```bash
# Перезагрузите сервер
sudo reboot

# После перезагрузки проверьте, что можете войти как x0
ssh -p 48735 x0@ВАШ_IP_АДРЕС

# Проверьте, что sudo работает
sudo -l

# Попробуйте войти как root - должно быть запрещено
ssh -p 48735 root@ВАШ_IP_АДРЕС
```

## Важные предупреждения:

1. **Никогда не закрывайте текущую сессию** до проверки работоспособности новых настроек.
2. **Всегда имейте запасной метод доступа** (например, консольный доступ через панель управления Hetzner).
3. **В случае потери доступа к административному пользователю**, вы сможете восстановить доступ через консоль Hetzner.
4. **Периодически проверяйте файл `/var/log/sudo.log`** для мониторинга действий с повышенными привилегиями.

Эти меры обеспечат гораздо более высокий уровень безопасности, чем описанный в исходном документе, и при этом сохранят функциональность системы, в отличие от полного удаления пользователя root.

### 2.6 Настройка конфигурации на вашем Mac (выполнено)

1. **Создайте файл конфигурации SSH** на вашем Mac (в новом окне Terminal):
   ```bash
   nano ~/.ssh/config
   ```

2. **Добавьте следующую конфигурацию**:
   ```
   # Сервер quer.us - x0 доступ
   Host quer-x0
       HostName *************
       Port 48735
       User x0
       IdentityFile ~/.ssh/hetzner_ed25519
   
   # Сервер quer.us - Duploy доступ
   Host quer-duploy
       HostName *************
       Port 48735
       User duploy
       IdentityFile ~/.ssh/hetzner_ed25519
   ```
   - Замените ************* на реальный IP вашего сервера
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

3. **Проверьте подключение** с использованием новой конфигурации:
   ```bash
   # Подключение как admin
   ssh -v quer-x0
   
   # После проверки выйдите командой 'exit' и попробуйте deploy
   exit
   
   # Подключение как duploy
   ssh quer-duploy
   ```
   - Для обоих подключений вас должны пустить без проблем
   - Теперь вы можете легко подключаться, используя короткие команды

## Этап 3: Дополнительные настройки безопасности

### 3.0 Настройка Podman для контейнеризации (не выполнено)

1. **Установите Podman**:
   ```bash
   sudo apt install -y podman
   ```

2. **Проверьте установку**:
   ```bash
   podman --version
   ```

3. **Настройте хранилище Podman**:
   ```bash
   # Создание директории для контейнеров
   sudo mkdir -p /var/lib/containers
   
   # Проверка базовой конфигурации Podman
   sudo podman info
   ```

4. **Создайте базовый контейнер для тестирования** (опционально):
   ```bash
   # Пример запуска тестового контейнера
   podman run --rm docker.io/library/hello-world
   ```

### 3.1 Настройка безопасного хранения секретов (не выполнено)

1. **Создайте безопасную директорию для секретов**:
   ```bash
   # Создаем директорию с ограниченными правами
   sudo mkdir -p /etc/secrets
   sudo chmod 700 /etc/secrets
   ```

2. **Создайте файл для хранения секретов с ограниченными правами**:
   ```bash
   # Создаем файл с секретами приложения
   sudo touch /etc/secrets/app_secrets
   sudo chmod 600 /etc/secrets/app_secrets
   
   # Устанавливаем владельца файла
   sudo chown duploy:duploy /etc/secrets/app_secrets
   ```

3. **Создайте скрипт для безопасного чтения секретов**:
   ```bash
   sudo nano /usr/local/bin/read-secret
   ```
   
   - Вставьте следующий код:
   ```bash
   #!/bin/bash
   # Использование: read-secret KEY
   # Возвращает значение из файла секретов
   
   if [ $# -ne 1 ]; then
     echo "Usage: read-secret KEY" >&2
     exit 1
   fi
   
   KEY="$1"
   FILE="/etc/secrets/app_secrets"
   
   if [ ! -f "$FILE" ]; then
     echo "Secret file not found!" >&2
     exit 2
   fi
   
   grep "^$KEY=" "$FILE" | cut -d '=' -f2-
   ```
   
   - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
   
4. **Сделайте скрипт исполняемым**:
   ```bash
   sudo chmod +x /usr/local/bin/read-secret
   ```

5. **Пример добавления секрета**:
   ```bash
   # Добавление секрета в файл (замените переменные на свои)
   echo "DB_PASSWORD=my_secure_password" | sudo tee -a /etc/secrets/app_secrets
   ```

6. **Пример чтения секрета**:
   ```bash
   # Чтение секрета из файла
   read-secret DB_PASSWORD
   ```

### 3.1 Автоматические обновления безопасности (выполнено)

## Настройка временной зоны
sudo timedatectl set-timezone America/New_York

## Установка и настройка NTP для синхронизации времени
sudo apt install -y systemd-timesyncd
sudo systemctl enable systemd-timesyncd
sudo systemctl start systemd-timesyncd

## Проверка статуса синхронизации
sudo timedatectl status

1. **Подключитесь к серверу как x0**:
   ```bash
   ssh -v quer-x0
   ```

2. **Установите необходимые пакеты**:
   ```bash
   sudo apt install -y unattended-upgrades apt-listchanges
   ```

3. **Настройте автоматические обновления**:
   ```bash
   sudo nano /etc/apt/apt.conf.d/20auto-upgrades
   ```
   
   - Вставьте следующий текст:
   ```
   APT::Periodic::Update-Package-Lists "1";
   APT::Periodic::Unattended-Upgrade "1";
   APT::Periodic::Download-Upgradeable-Packages "1";
   APT::Periodic::AutocleanInterval "7";
   ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

4. **Настройте параметры автоматических обновлений**:
   ```bash
   sudo nano /etc/apt/apt.conf.d/50unattended-upgrades
   ```
   
   - Найдите и раскомментируйте (удалите //) следующие строки:
     - `"${distro_id}:${distro_codename}-updates";`
     - `Unattended-Upgrade::Remove-Unused-Dependencies "true";`
     - `Unattended-Upgrade::Automatic-Reboot "true";`
     - `Unattended-Upgrade::Automatic-Reboot-Time "02:00";`
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

5. **Проверьте настройки**:
   ```bash
   sudo unattended-upgrade --dry-run --debug
   ```

### 3.2 Настройка базовой системы логирования (выполнено)

1. **Установите инструменты для логирования**:
   ```bash
   sudo apt install -y logrotate
   ```

2. **Настройте ротацию логов системы**:
   ```bash
   sudo nano /etc/logrotate.d/server-logs
   ```
   
   - Вставьте следующий текст:
   ```
   /var/log/syslog
   /var/log/auth.log
   /var/log/kern.log
   /var/log/boot.log
   /var/log/dpkg.log
   /var/log/nginx/*.log
   {
       rotate 30
       daily
       missingok
       notifempty
       compress
       delaycompress
       sharedscripts
       postrotate
           systemctl reload rsyslog >/dev/null 2>&1 || true
       endscript
   }
   ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

3. **Проверьте конфигурацию logrotate**:
   ```bash
   sudo logrotate -d /etc/logrotate.d/server-logs
   ```

4. **Настройте простой скрипт для проверки критических ошибок**:
   ```bash
   sudo nano /usr/local/bin/check-critical-logs
   ```
   
   - Вставьте следующий текст:
   ```bash
   #!/bin/bash
   
   # Выводит критические ошибки за последние 24 часа
   journalctl -p err..emerg --since "24 hours ago" | less
   ```
   
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

5. **Сделайте скрипт исполняемым**:
   ```bash
   sudo chmod +x /usr/local/bin/check-critical-logs
   ```

Эта конфигурация обеспечивает:
- Ротацию важных системных логов каждый день
- Сжатие старых логов для экономии места
- Хранение логов в течение 30 дней (оптимальный период для большинства стандартных серверов)
- Удобный инструмент для ручной проверки критических ошибок при необходимости

Когда вы почувствуете, что что-то не так, вы можете просто запустить `sudo check-critical-logs`, чтобы быстро увидеть важные ошибки.
 
### 3.2.1 Настройка уведомлений о критических ошибках через Telegram (выполнено)

Инструкция для настройки Telegram-уведомлений о критических ошибках:

1. **Создайте бота в Telegram**:
   - Откройте Telegram и найдите @BotFather
   - Отправьте команду `/newbot`
   - Следуйте инструкциям для создания бота
   - Сохраните полученный токен (выглядит как `123456789:ABCdefGhIJKlmNoPQRsTUVwxyZ`)
   - Отправьте сообщение своему новому боту и выполните следующие шаги

2. **Узнайте ID вашего чата**:
   - Отправьте сообщение боту `/start`
   - Посетите URL в браузере (замените YOUR_BOT_TOKEN на токен вашего бота):
     ```
     https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates
     ```
   - Найдите значение `"id"` в поле `"chat"` и запишите его (это ваш chat_id)

3. **Установите необходимые пакеты на сервере**:
   ```bash
   sudo apt install -y curl jq
   ```

4. **Создайте конфигурационный файл для бота**:
   ```bash
   sudo mkdir -p /etc/telegram-notify
   sudo nano /etc/telegram-notify/config
   ```
   
   - Вставьте следующий текст (замените значения):
   ```
   BOT_TOKEN="YOUR_BOT_TOKEN"
   CHAT_ID="YOUR_CHAT_ID"
   ```
   - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
   - Установите безопасные разрешения:
   ```bash
   sudo chmod 600 /etc/telegram-notify/config
   ```

5. **Создайте скрипт для отправки уведомлений**:
   ```bash
   sudo nano /usr/local/bin/telegram-notify
   ```
   
   - Вставьте следующий код:
#!/bin/bash

# Загрузка конфигурации
source /etc/telegram-notify/config

# Функция для отправки сообщений
send_message() {
  MESSAGE="$1"
  curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
    -d chat_id="$CHAT_ID" \
    -d text="$MESSAGE" \
    -d parse_mode="HTML" > /dev/null
}

# Получение имени хоста
HOSTNAME=$(hostname)

# Получение критических ошибок за последние 24 часа
ERRORS=$(journalctl -p err..emerg --since "24 hours ago" --no-pager)

# Проверка наличия ошибок
if [ -n "$ERRORS" ]; then
  # Подсчет количества ошибок
  ERROR_COUNT=$(echo "$ERRORS" | wc -l)
  
  # Формирование сообщения
  MESSAGE="⚠️ <b>ВНИМАНИЕ:</b> Обнаружено $ERROR_COUNT критических ошибок на сервере <b>$HOSTNAME</b>

Последние 10 ошибок:
<pre>$(echo "$ERRORS" | tail -10)</pre>

$(date '+%d.%m.%Y %H:%M:%S')"
  
  # Отправка уведомления
  send_message "$MESSAGE"
  
  logger -t telegram-notify "Отправлено уведомление о $ERROR_COUNT критических ошибках"
else
  logger -t telegram-notify "Критических ошибок не обнаружено"
fi
   
   - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)

6. **Сделайте скрипт исполняемым**:
   ```bash
   sudo chmod +x /usr/local/bin/telegram-notify
   ```

7. **Добавьте задачу в cron для ежедневного выполнения**:
   ```bash
   sudo nano /etc/cron.d/error-notify
   ```
   
   - Вставьте следующий текст:
   ```
   # Проверка критических ошибок каждый день в 08:00
   0 8 * * * root /usr/local/bin/telegram-notify
   ```
   - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)

8. **Протестируйте работу скрипта**:
   ```bash
   sudo /usr/local/bin/telegram-notify
   ```
   
   Вы должны получить сообщение в Telegram, если есть критические ошибки, или запись в логе, если их нет.

9. **Проверьте запись в логе**:
   ```bash
   sudo grep telegram-notify /var/log/syslog
   ```

Этот подход обеспечивает мгновенные уведомления о критических ошибках прямо в ваш Telegram, что удобнее, чем проверять электронную почту.

### 3.3 Настройка Nginx с базовой безопасностью (не выполнено)

1. **Установите Nginx**:
   ```bash
   sudo apt install -y nginx
   ```

2. **Создайте базовую конфигурацию безопасности**:
   ```bash
   sudo nano /etc/nginx/conf.d/security.conf
   ```
   
   - Вставьте следующий текст:
   ```
   # Скрытие версии Nginx
   server_tokens off;
   
   # Защита от XSS-атак
   add_header X-XSS-Protection "1; mode=block";
   
   # Защита от clickjacking
   add_header X-Frame-Options "SAMEORIGIN";
   
   # Защита от атак с изменением типа контента
   add_header X-Content-Type-Options "nosniff";
   ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

3. **Перезапустите Nginx**:
   ```bash
   sudo systemctl restart nginx
   ```

### 3.4 Настройка базовой защиты от сканирования портов (выполнено)

1. **Установите psad для обнаружения сканирования портов**:
   ```bash
   sudo apt install -y psad
   ```

2. **Настройте базовую конфигурацию psad**:
   ```bash
   sudo nano /etc/psad/psad.conf
   ```
   
   - Найдите и измените следующие параметры:
     - `EMAIL_ADDRESSES ваш******************;` (замените на ваш email)
     - `HOSTNAME quer-server;` (замените на имя вашего сервера)
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

3. **Перезапустите psad**:
   ```bash
   sudo systemctl restart psad
   ```

# Настройка уведомлений PSAD через Telegram (выполнено)

## Как настроить PSAD с уведомлениями через Telegram

Я заметил, что в вашей документации (complete-security-guide.md) уже есть раздел 3.2.1 с инструкцией для настройки уведомлений через Telegram для критических ошибок. Давайте адаптируем этот подход для PSAD:

1. **Создайте конфигурацию Telegram** (если еще не создали):
   ```bash
   sudo mkdir -p /etc/telegram-notify
   sudo nano /etc/telegram-notify/config
   ```

   Содержимое (замените на свои значения):
   ```
   BOT_TOKEN="YOUR_BOT_TOKEN"
   CHAT_ID="YOUR_CHAT_ID"
   ```

2. **Создайте скрипт для PSAD-уведомлений**:
   ```bash
   sudo nano /usr/local/bin/psad-telegram-notify
   ```

   Вставьте следующий код:
   ```bash
   #!/bin/bash

   # Загрузка конфигурации Telegram
   source /etc/telegram-notify/config

   # Функция для отправки сообщений в Telegram
   send_message() {
     MESSAGE="$1"
     curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
       -d chat_id="$CHAT_ID" \
       -d text="$MESSAGE" \
       -d parse_mode="HTML" > /dev/null
   }

   # Получение имени хоста
   HOSTNAME=$(hostname)

   # Получаем информацию от PSAD
   PSAD_INFO=$(sudo psad --Status | grep -A 20 "DANGER LEVEL")

   # Проверка наличия подозрительной активности
   if [[ "$PSAD_INFO" == *"DANGER LEVEL 3"* || "$PSAD_INFO" == *"DANGER LEVEL 4"* || "$PSAD_INFO" == *"DANGER LEVEL 5"* ]]; then
     # Формирование сообщения
     MESSAGE="🚨 <b>ВНИМАНИЕ:</b> PSAD обнаружил подозрительную активность на сервере <b>$HOSTNAME</b>

   <pre>$PSAD_INFO</pre>

   $(date '+%d.%m.%Y %H:%M:%S')"
     
     # Отправка уведомления
     send_message "$MESSAGE"
     
     logger -t psad-notify "Отправлено уведомление о подозрительной активности в Telegram"
   else
     logger -t psad-notify "Подозрительной активности не обнаружено"
   fi
   ```

3. **Сделайте скрипт исполняемым**:
   ```bash
   sudo chmod +x /usr/local/bin/psad-telegram-notify
   ```

4. **Отключите email-уведомления в PSAD**:
   ```bash
   sudo nano /etc/psad/psad.conf
   ```
   
   Найдите и измените:
   ```
   EMAIL_ADDRESSES             none;
   ```

5. **Добавьте проверку через cron**:
   ```bash
   sudo nano /etc/cron.d/psad-check
   ```
   
   Добавьте:
   ```
   # Проверка PSAD каждые 10 минут
   */10 * * * * root /usr/local/bin/psad-telegram-notify
   ```

6. **Протестируйте работу скрипта**:
   ```bash
   sudo /usr/local/bin/psad-telegram-notify
   ```

Этот подход намного практичнее для личного использования, чем настройка полноценного почтового сервера. Многие администраторы сейчас переходят на мессенджеры для уведомлений, так что ваш подход абсолютно правильный!

### 3.5 Базовая настройка бэкапов (не выполнено)

1. **Создайте директории для бэкапов**:
   ```bash
   sudo mkdir -p /var/backups/quer-calc/daily
   sudo chmod -R 700 /var/backups
   ```

2. **Создайте простой скрипт бэкапа**:
   ```bash
   sudo nano /usr/local/bin/backup-server
   ```
   
   - Вставьте следующий текст:
   ```bash
   #!/bin/bash
   
   # Переменные
   DATE=$(date +"%Y-%m-%d")
   BACKUP_DIR="/var/backups/quer-calc/daily"
   APP_DIR="/var/www/quer-calc"
   
   # Создание бэкапа приложения (если оно уже установлено)
   if [ -d "$APP_DIR" ]; then
     mkdir -p "$BACKUP_DIR/app-$DATE"
     rsync -a "$APP_DIR/" "$BACKUP_DIR/app-$DATE/"
   fi
   
   # Бэкап конфигураций
   mkdir -p "$BACKUP_DIR/config-$DATE"
   cp -r /etc/nginx /etc/ssh /etc/fail2ban "$BACKUP_DIR/config-$DATE/"
   
   # Удаление старых бэкапов (старше 7 дней)
   find "$BACKUP_DIR" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null
   ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

3. **Сделайте скрипт исполняемым**:
   ```bash
   sudo chmod +x /usr/local/bin/backup-server
   ```

4. **Добавьте задачу в cron для ежедневного запуска**:
   ```bash
   sudo nano /etc/cron.d/server-backup
   ```
   
   - Вставьте следующий текст:
   ```
   # Запуск ежедневного бэкапа в 01:00
   0 1 * * * root /usr/local/bin/backup-server
   ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

### 3.7 Настройка автоматических перезапусков приложения (не выполнено)

1. **Создайте systemd-сервис для вашего приложения** (пример для Node.js):
   ```bash
   sudo nano /etc/systemd/system/quer-app.service
   ```
   
   - Вставьте следующий код (настройте пути в соответствии с вашим приложением):
   ```
   [Unit]
   Description=Quer App Service
   After=network.target
   
   [Service]
   Type=simple
   User=deploy
   WorkingDirectory=/var/www/quer-calc/production
   ExecStart=/usr/bin/node /var/www/quer-calc/production/index.js
   Restart=always
   RestartSec=3
   Environment=NODE_ENV=production
   # Добавление переменной для чтения секретов
   ExecStartPre=/bin/bash -c 'export $(cat /etc/secrets/app_secrets | xargs)'
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)

2. **Включите и запустите сервис**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable quer-app.service
   sudo systemctl start quer-app.service
   ```

3. **Проверьте статус сервиса**:
   ```bash
   sudo systemctl status quer-app.service
   ```

4. **Настройте systemd-сервис для бэкенда на Rust** (опционально):
   ```bash
   sudo nano /etc/systemd/system/quer-backend.service
   ```
   
   - Вставьте следующий код:
   ```
   [Unit]
   Description=Quer Backend Service
   After=network.target
   
   [Service]
   Type=simple
   User=deploy
   WorkingDirectory=/var/www/quer-calc/production/backend-rust
   ExecStart=/var/www/quer-calc/production/backend-rust/target/release/backend-rust
   Restart=always
   RestartSec=3
   Environment=RUST_ENV=production
   # Добавление переменной для чтения секретов
   ExecStartPre=/bin/bash -c 'export $(cat /etc/secrets/app_secrets | xargs)'
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   - Сохраните файл (Ctrl+O, затем Enter) и выйдите (Ctrl+X)
   
5. **Включите и запустите сервис**:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable quer-backend.service
   sudo systemctl start quer-backend.service
   ```

### 3.8 Аудит безопасности системы (выполнено)

1. **Установите инструменты аудита**:
   ```bash
   sudo apt install -y lynis
   ```

2. **Запустите базовый аудит**:
   ```bash
   sudo lynis audit system
   ```
   - Изучите результаты и рекомендации
   - Исправьте наиболее критичные проблемы, если они указаны

3. **Настройте регулярное сканирование**:
   ```bash
   sudo nano /etc/cron.weekly/security-audit
   ```
   
   - Вставьте следующий текст:
   ```bash
   #!/bin/bash
   /usr/bin/lynis audit system --quick
   ```
   - Для сохранения нажмите Ctrl+O, затем Enter
   - Для выхода нажмите Ctrl+X

4. **Сделайте скрипт исполняемым**:
   ```bash
   sudo chmod +x /etc/cron.weekly/security-audit
   ```

## Финальные проверки и настройка SSL

### 1. Проверка статуса служб

```bash
# Проверка статуса UFW
sudo ufw status

# Проверка статуса CrowdSec
sudo cscli metrics
sudo cscli decisions list

# Проверка открытых портов
sudo ss -tulpn
```

### 2. Базовая настройка SSL с Let's Encrypt (если у вас есть домен) (не выполнено)

1. **Установите certbot**:
   ```bash
   sudo apt install -y certbot python3-certbot-nginx
   ```

2. **Получите SSL-сертификат** (замените quer.us на ваш домен):
   ```bash
   sudo certbot --nginx -d quer.us -d www.quer.us
   ```
   - Следуйте инструкциям на экране
   - Сертификат обновляется автоматически

### 3. Завершающие действия

1. **Перезагрузите сервер** для применения всех настроек:
   ```bash
   sudo reboot
   ```

2. **Проверьте подключение** после перезагрузки:
   ```bash
   # На вашем Mac
   ssh -v quer-x0
   ```

3. **Проверьте логи на наличие ошибок**:
   ```bash
   sudo tail -100 /var/log/syslog
   sudo tail -100 /var/log/auth.log
   ```

## Поздравляем!

Теперь у вас есть базовая, но достаточно безопасная настройка сервера Ubuntu на Hetzner. Ваш сервер защищен от большинства типовых атак:

- SSH доступен только через нестандартный порт и только с вашего ключа
- Учетные записи разделены на административную (x0) и для деплоя (duploy)
- Настроен файрвол, пропускающий только необходимые порты
- Настроена защита от брутфорса с CrowdSec
- Настроены автоматические обновления
- Базовое логирование и бэкапы

Дальнейшие шаги могут включать настройку деплоя вашего приложения и более сложные инструменты безопасности, если они понадобятся.

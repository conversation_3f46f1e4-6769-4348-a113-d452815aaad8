# План оптимизации Next.js с гибридным рендерингом

## 1. Оптимизация серверного рендеринга

### 1.1. Настройка переменных окружения для Next.js

```bash
# .env.production
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.example.com
NEXT_TELEMETRY_DISABLED=1
NEXT_SHARP_PATH=/usr/local/lib/node_modules/sharp
NEXT_OPTIMIZE_FONTS=1
NEXT_OPTIMIZE_IMAGES=1
NEXT_OPTIMIZE_CSS=1
```

**Рекомендации:**
- Создать отдельные файлы переменных окружения для разных сред: `.env.development`, `.env.test`, `.env.production`
- Использовать префикс `NEXT_PUBLIC_` только для переменных, доступных на клиентской стороне
- Отключить телеметрию Next.js в production для снижения накладных расходов
- Указать путь к библиотеке Sharp для оптимизации изображений

### 1.2. Настройка кэширования для ISR (Incremental Static Regeneration)

**Обновление конфигурации Next.js (`next.config.js`):**

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  
  // Включение experimental appDir для App Router (если используется)
  experimental: {
    appDir: true,
  },
  
  // Настройки для компрессии и минификации
  compress: true,
  poweredByHeader: false,
  
  // Настройки для оптимизации изображений
  images: {
    domains: ['example.com', 'cdn.example.com'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60, // в секундах
  },
  
  // Конфигурация для ISR
  // В App Router это также можно настроить через опции fetch или generateStaticParams
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=86400',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

**Настройка ISR для отдельных страниц (Pages Router):**

```javascript
// pages/product/[id].js
export async function getStaticProps({ params }) {
  const product = await fetchProduct(params.id);
  
  return {
    props: {
      product,
    },
    // Повторная генерация через каждые 60 секунд (если есть запрос)
    revalidate: 60,
  };
}

export async function getStaticPaths() {
  // Предварительно генерируем только популярные продукты
  const popularProducts = await fetchPopularProducts();
  
  return {
    paths: popularProducts.map(product => ({
      params: { id: product.id.toString() },
    })),
    // Разрешаем генерацию по запросу для остальных продуктов
    fallback: 'blocking',
  };
}
```

**Настройка ISR для App Router:**

```javascript
// app/product/[id]/page.js
export default async function ProductPage({ params }) {
  // Использование fetch с revalidate в App Router
  const product = await fetch(`https://api.example.com/products/${params.id}`, {
    next: { revalidate: 60 }, // Повторная проверка каждые 60 секунд
  }).then(res => res.json());
  
  return <ProductDetails product={product} />;
}

// Предварительно генерируемые пути
export async function generateStaticParams() {
  const popularProducts = await fetch('https://api.example.com/popular-products')
    .then(res => res.json());
  
  return popularProducts.map(product => ({
    id: product.id.toString(),
  }));
}
```

### 1.3. Оптимизация Node.js для production-окружения

**Настройка PM2 для управления процессами Node.js (`ecosystem.config.js`):**

```javascript
module.exports = {
  apps: [
    {
      name: 'next-app',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      instances: 'max', // Автомасштабирование в зависимости от доступных CPU
      exec_mode: 'cluster', // Режим кластера для балансировки нагрузки
      watch: false,
      env: {
        PORT: 3000,
        NODE_ENV: 'production',
      },
      // Настройки для оптимизации памяти
      node_args: [
        '--max-old-space-size=4096', // Для приложений с большим потреблением памяти
        '--optimize-for-size',
        '--max-http-header-size=8192',
        '--no-warnings',
      ],
      // Настройки перезапуска при проблемах с памятью
      max_memory_restart: '1G',
      shutdown_with_message: true,
      wait_ready: true,
      listen_timeout: 10000,
      kill_timeout: 5000,
    },
  ],
};
```

**Оптимизация сборки Node.js:**

```bash
# Использование переменных окружения для оптимизации производительности
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=4096"

# Сборка приложения с оптимизациями
npm run build

# Запуск приложения через PM2
pm2 start ecosystem.config.js
```

**Советы по оптимизации сервера:**
- Использовать `NODE_ENV=production` для включения всех оптимизаций
- Настроить ограничение памяти в зависимости от доступных ресурсов
- Использовать кластерный режим для масштабирования на многоядерных серверах
- Настроить автоматический перезапуск при утечках памяти
- Включить source map для отладки в production-окружении

## 2. Настройка статического контента

### 2.1. Конфигурация Nginx для эффективной доставки статического контента

**Базовая конфигурация Nginx для Next.js (`nginx.conf`):**

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Настройки логирования
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                     '$status $body_bytes_sent "$http_referer" '
                     '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;
    
    # Оптимизация производительности
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Настройки сжатия
    gzip on;
    gzip_comp_level 6;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/javascript
        application/json
        application/x-javascript
        application/xml
        application/xml+rss
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Конфигурация сервера Next.js
    server {
        listen 80;
        server_name example.com www.example.com;
        
        # Перенаправление на HTTPS
        return 301 https://$host$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name example.com www.example.com;
        
        # SSL настройки
        ssl_certificate /etc/letsencrypt/live/example.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/example.com/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_stapling on;
        ssl_stapling_verify on;
        
        # Корневая директория Next.js
        root /var/www/next-app;
        
        # Настройки для статического контента
        location /_next/static/ {
            alias /var/www/next-app/.next/static/;
            expires 365d;
            add_header Cache-Control "public, max-age=31536000, immutable";
            access_log off;
        }
        
        # Настройки для статических файлов в public директории
        location /static/ {
            alias /var/www/next-app/public/static/;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
            access_log off;
        }
        
        # Настройки для изображений
        location /_next/image {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            
            # Кэширование оптимизированных изображений
            proxy_cache nextjs_cache;
            proxy_cache_valid 200 302 60m;
            proxy_cache_valid 404 1m;
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # Настройки для остальных запросов к Next.js
        location / {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }
        
        # Обработка ошибок
        error_page 404 /404.html;
        location = /404.html {
            root /var/www/next-app/public;
            internal;
        }
        
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /var/www/next-app/public;
            internal;
        }
    }
    
    # Настройки кэширования
    proxy_cache_path /var/cache/nginx/nextjs_cache levels=1:2 keys_zone=nextjs_cache:10m max_size=1g inactive=60m use_temp_path=off;
}
```

### 2.2. Настройка кэширования для статически генерируемых страниц

**Дополнительные HTTP-заголовки для кэширования в Next.js (`next.config.js`):**

```javascript
async headers() {
  return [
    // Кэширование для статических страниц
    {
      source: '/',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
        },
      ],
    },
    // Кэширование для страниц блога
    {
      source: '/blog/:slug',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=3600, s-maxage=86400, stale-while-revalidate=43200',
        },
      ],
    },
    // Кэширование для статических ресурсов
    {
      source: '/_next/static/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=31536000, immutable',
        },
      ],
    },
    // Кэширование для изображений
    {
      source: '/images/:path*',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=86400, stale-while-revalidate=43200',
        },
      ],
    },
  ];
}
```

**Создание скрипта для согревания кэша (cache warming):**

```javascript
// scripts/cache-warmer.js
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Путь к статически сгенерированным путям из getStaticPaths
const pagesManifestPath = path.join(process.cwd(), '.next/server/pages-manifest.json');
const pagesManifest = JSON.parse(fs.readFileSync(pagesManifestPath, 'utf8'));

const baseUrl = process.env.SITE_URL || 'https://example.com';

// Основные страницы для прогревания кэша
const pagesToWarm = [
  '/',
  '/about',
  '/contact',
  '/blog',
];

// Динамические страницы на основе getStaticPaths
const dynamicPages = Object.keys(pagesManifest)
  .filter(page => page.includes('[') && page.includes(']'))
  .map(page => {
    const pathParam = page.match(/\[(.*?)\]/)[1];
    return { page, pathParam };
  });

// Получение данных для динамических страниц
async function getDynamicPageParams() {
  // Здесь можно реализовать логику получения параметров для динамических страниц
  // Например, из API или базы данных
  const response = await fetch(`${baseUrl}/api/page-params`);
  return response.json();
}

async function warmCache() {
  console.log('🔥 Начинаем согревание кэша...');
  
  // Прогрев статических страниц
  for (const page of pagesToWarm) {
    try {
      const url = `${baseUrl}${page}`;
      console.log(`Согреваем: ${url}`);
      await fetch(url);
    } catch (error) {
      console.error(`Ошибка при согревании ${page}:`, error);
    }
  }
  
  // Прогрев динамических страниц
  if (dynamicPages.length > 0) {
    const params = await getDynamicPageParams();
    
    for (const { page, pathParam } of dynamicPages) {
      for (const param of params) {
        try {
          const formattedPage = page.replace(`[${pathParam}]`, param[pathParam]);
          const url = `${baseUrl}${formattedPage}`;
          console.log(`Согреваем динамическую страницу: ${url}`);
          await fetch(url);
        } catch (error) {
          console.error(`Ошибка при согревании динамической страницы:`, error);
        }
      }
    }
  }
  
  console.log('✅ Кэш успешно согрет!');
}

warmCache().catch(console.error);
```

**Добавление скрипта для автоматического согревания кэша в `package.json`:**

```json
{
  "scripts": {
    "build": "next build",
    "start": "next start",
    "postbuild": "node scripts/cache-warmer.js"
  }
}
```

### 2.3. Оптимизация загрузки клиентских компонентов

**Использование динамического импорта для разделения кода:**

```javascript
// app/components/HeavyComponent.js
'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';

// Динамический импорт тяжелого компонента
const HeavyChart = dynamic(() => import('./HeavyChart'), {
  loading: () => <p>Загрузка графика...</p>,
  ssr: false, // Отключение SSR для компонента, который требуется только на клиенте
});

export default function HeavyComponent() {
  const [showChart, setShowChart] = useState(false);
  
  return (
    <div>
      <button onClick={() => setShowChart(!showChart)}>
        {showChart ? 'Скрыть график' : 'Показать график'}
      </button>
      
      {showChart && <HeavyChart />}
    </div>
  );
}
```

**Оптимизация изображений и медиаконтента:**

```javascript
// app/components/OptimizedImage.js
import Image from 'next/image';

export default function OptimizedImage({ src, alt, ...props }) {
  return (
    <div className="image-container">
      <Image
        src={src}
        alt={alt}
        width={800}
        height={600}
        placeholder="blur" // Блюр во время загрузки
        blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFOwJC8S9NkgAAAABJRU5ErkJggg=="
        loading="lazy" // Ленивая загрузка для изображений
        quality={75} // Баланс между качеством и размером
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" // Респонсивные размеры
        {...props}
      />
    </div>
  );
}
```

**Optimizing Font Loading:**

```javascript
// app/layout.js
import { Inter } from 'next/font/google';

// Оптимизация загрузки шрифтов
const inter = Inter({
  subsets: ['latin'],
  display: 'swap', // Используется font-display: swap
  variable: '--font-inter', // Использование CSS-переменных
  preload: true, // Предзагрузка шрифта
  fallback: ['system-ui', 'sans-serif'], // Резервные шрифты
});

export default function RootLayout({ children }) {
  return (
    <html lang="ru" className={inter.variable}>
      <body>{children}</body>
    </html>
  );
}
```

**Предварительная загрузка важных ресурсов:**

```javascript
// app/layout.js
import { Suspense } from 'react';
import Script from 'next/script';

export default function RootLayout({ children }) {
  return (
    <html lang="ru">
      <head>
        {/* Предзагрузка критических ресурсов */}
        <link
          rel="preload"
          href="/fonts/custom-font.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        
        {/* Предсоединение к важным доменам */}
        <link rel="preconnect" href="https://api.example.com" />
        <link rel="dns-prefetch" href="https://cdn.example.com" />
      </head>
      <body>
        <Suspense fallback={<p>Загрузка...</p>}>
          {children}
        </Suspense>
        
        {/* Отложенная загрузка некритичных скриптов */}
        <Script
          src="https://analytics.example.com/script.js"
          strategy="lazyOnload"
          onLoad={() => console.log('Аналитика загружена')}
        />
      </body>
    </html>
  );
}
```

## 3. Мониторинг и дополнительные оптимизации

### 3.1. Настройка мониторинга производительности

**Использование Next.js Metrics и OpenTelemetry:**

```javascript
// instrumentation.js
export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    const { NodeSDK } = await import('@opentelemetry/sdk-node');
    const { OTLPTraceExporter } = await import('@opentelemetry/exporter-trace-otlp-http');
    const { Resource } = await import('@opentelemetry/resources');
    const { SemanticResourceAttributes } = await import('@opentelemetry/semantic-conventions');
    const { SimpleSpanProcessor } = await import('@opentelemetry/sdk-trace-base');
    
    const sdk = new NodeSDK({
      resource: new Resource({
        [SemanticResourceAttributes.SERVICE_NAME]: 'next-app',
        [SemanticResourceAttributes.SERVICE_VERSION]: '1.0.0',
      }),
      spanProcessor: new SimpleSpanProcessor(
        new OTLPTraceExporter({
          url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT || 'http://localhost:4318/v1/traces',
        })
      ),
    });
    
    sdk.start();
  }
}
```

**Добавление клиентского мониторинга Web Vitals:**

```javascript
// lib/analytics.js
export function reportWebVitals({ id, name, label, value }) {
  // Отправка метрик в аналитическую систему
  fetch('/api/analytics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      id,
      name,
      label,
      value,
      timestamp: Date.now(),
    }),
  });
}

// app/layout.js
import { reportWebVitals } from '@/lib/analytics';

export function reportWebVitals(metric) {
  reportWebVitals(metric);
}
```

### 3.2. Дополнительные техники оптимизации

**Использование Server Components для уменьшения JavaScript на клиенте:**

```javascript
// app/components/DataTable.js
// По умолчанию - Server Component
import { db } from '@/lib/db';

// Серверный компонент, который не отправляет JavaScript на клиент
export default async function DataTable({ query }) {
  // Данные загружаются на сервере
  const data = await db.query(query);
  
  return (
    <table>
      <thead>
        <tr>
          <th>ID</th>
          <th>Name</th>
          <th>Created</th>
        </tr>
      </thead>
      <tbody>
        {data.map((item) => (
          <tr key={item.id}>
            <td>{item.id}</td>
            <td>{item.name}</td>
            <td>{new Date(item.created).toLocaleDateString()}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
```

**Реализация стратегии Progressive Hydration:**

```javascript
// app/components/ProgressiveHydration.js
'use client';

import { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';

export default function ProgressiveHydration({ children, rootMargin = '200px' }) {
  const [isClient, setIsClient] = useState(false);
  const { ref, inView } = useInView({
    triggerOnce: true,
    rootMargin,
  });
  
  // Определяем, выполняется ли код на клиенте
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Если ещё не просматривается или не на клиенте,
  // рендерим только серверную версию
  if (!isClient || !inView) {
    return <div ref={ref}>{children}</div>;
  }
  
  // После гидратации возвращаем полный интерактивный компонент
  return <div>{children}</div>;
}

// Использование
import HeavyComponent from './HeavyComponent';

function HomePage() {
  return (
    <div>
      <h1>Главная страница</h1>
      
      {/* HeavyComponent будет гидратирован, только когда появится в области видимости */}
      <ProgressiveHydration>
        <HeavyComponent />
      </ProgressiveHydration>
    </div>
  );
}
```

**Оптимизация с помощью React Streaming и Suspense:**

```javascript
// app/page.js
import { Suspense } from 'react';
import PageHeader from './components/PageHeader';
import ProductList from './components/ProductList';
import RecommendedProducts from './components/RecommendedProducts';

export default function Page() {
  return (
    <div>
      {/* Будет рендериться сразу */}
      <PageHeader />
      
      {/* Блок для основного контента со скелетоном во время загрузки */}
      <Suspense fallback={<div className="skeleton-loader">Загрузка продуктов...</div>}>
        <ProductList />
      </Suspense>
      
      {/* Менее приоритетный контент с задержкой рендеринга */}
      <Suspense fallback={<div className="skeleton-loader">Загрузка рекомендаций...</div>}>
        <RecommendedProducts />
      </Suspense>
    </div>
  );
}
```

## Заключение

Оптимизация Next.js с гибридным рендерингом требует комплексного подхода, включающего в себя:

1. **Серверную оптимизацию:**
   - Правильная настройка переменных окружения
   - Эффективное использование ISR для балансирования между статическим и динамическим контентом
   - Оптимизация Node.js в production-окружении

2. **Настройку статического контента:**
   - Конфигурация Nginx для эффективной доставки статических ресурсов
   - Применение агрессивного кэширования для статических страниц и ресурсов
   - Оптимизация загрузки клиентских компонентов через разделение кода и ленивую загрузку

3. **Дополнительные техники:**
   - Использование мониторинга производительности
   - Применение Server Components для уменьшения размера JavaScript-бандла
   - Внедрение прогрессивной гидратации и потокового рендеринга

Реализуя данные рекомендации, вы сможете значительно повысить производительность и отзывчивость вашего Next.js приложения, что положительно скажется на пользовательском опыте и SEO-показателях.

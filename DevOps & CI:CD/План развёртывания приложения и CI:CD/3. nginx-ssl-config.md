# Подробный план настройки Nginx и SSL с Cloudflare

В этом документе представлен детальный план настройки Nginx и SSL с использованием Cloudflare для проекта quer-calc, включая оптимизацию производительности и кэширования.

## 1. Настройка виртуальных хостов в Nginx

### 1.1 Предварительная проверка и подготовка

```bash
# Подключение к серверу
ssh quer-x0

# Проверка установки Nginx
nginx -v

# Если Nginx не установлен, установите его
sudo apt update
sudo apt install -y nginx

# Создание директорий для хранения конфигураций
sudo mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
sudo mkdir -p /etc/nginx/conf.d/includes
```

### 1.2 Создание общего конфигурационного файла для оптимизаций

Мы создадим общие включаемые файлы для унификации настроек между сайтами:

```bash
# Создание общего файла для оптимизации SSL
sudo nano /etc/nginx/conf.d/includes/ssl-params.conf
```

Содержимое файла `ssl-params.conf`:
```nginx
# Оптимальные параметры SSL
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 1d;
ssl_session_tickets off;
ssl_ecdh_curve secp384r1;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* ******* ******* valid=300s;
resolver_timeout 5s;

# Заголовки безопасности
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

```bash
# Создание общего файла для оптимизации кэширования
sudo nano /etc/nginx/conf.d/includes/cache-params.conf
```

Содержимое файла `cache-params.conf`:
```nginx
# Кэширование статических файлов
location ~* \.(jpg|jpeg|png|gif|ico|css|js|webp|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, max-age=31536000, immutable";
    access_log off;
}

# Специальные правила для файлов Next.js
location /_next/static/ {
    expires 1y;
    add_header Cache-Control "public, max-age=31536000, immutable";
    access_log off;
}

location /_next/image {
    expires 1w;
    add_header Cache-Control "public, max-age=604800";
}

location /_next/data/ {
    expires 1d;
    add_header Cache-Control "public, max-age=86400";
}
```

```bash
# Создание общего файла для настроек сжатия
sudo nano /etc/nginx/conf.d/includes/compression.conf
```

Содержимое файла `compression.conf`:
```nginx
# Настройки Gzip сжатия
gzip on;
gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_buffers 16 8k;
gzip_http_version 1.1;
gzip_min_length 256;
gzip_types
    application/atom+xml
    application/geo+json
    application/javascript
    application/json
    application/ld+json
    application/manifest+json
    application/rdf+xml
    application/rss+xml
    application/vnd.ms-fontobject
    application/wasm
    application/x-font-ttf
    application/x-javascript
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/collection
    font/eot
    font/opentype
    font/otf
    font/ttf
    image/bmp
    image/svg+xml
    image/x-icon
    text/cache-manifest
    text/calendar
    text/css
    text/javascript
    text/markdown
    text/plain
    text/vcard
    text/vnd.rim.location.xloc
    text/vtt
    text/x-component
    text/x-cross-domain-policy;
```

### 1.3 Настройка виртуального хоста для staging (s.quer.us)

```bash
# Создание конфигурации для s.quer.us
sudo nano /etc/nginx/sites-available/s.quer.us.conf
```

Содержимое файла `s.quer.us.conf`:
```nginx
# HTTP сервер - редирект на HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name s.quer.us;
    
    # Лог файлы
    access_log /var/www/s.quer.us/shared/logs/nginx-access.log;
    error_log /var/www/s.quer.us/shared/logs/nginx-error.log warn;
    
    # Редирект на HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
    
    # Путь для проверки Let's Encrypt (если будет использоваться)
    location /.well-known/acme-challenge/ {
        root /var/www/s.quer.us/shared/acme;
    }
}

# HTTPS сервер
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name s.quer.us;
    
    # Директории сайта
    root /var/www/s.quer.us/current/public;
    
    # Лог файлы
    access_log /var/www/s.quer.us/shared/logs/nginx-access.log;
    error_log /var/www/s.quer.us/shared/logs/nginx-error.log warn;
    
    # SSL сертификаты (пути будут обновлены после получения сертификатов)
    ssl_certificate /etc/ssl/cloudflare/s.quer.us.pem;
    ssl_certificate_key /etc/ssl/cloudflare/s.quer.us.key;
    
    # Включение общих параметров
    include /etc/nginx/conf.d/includes/ssl-params.conf;
    include /etc/nginx/conf.d/includes/compression.conf;
    
    # Проксирование запросов к API бэкенду
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Настройки таймаутов для API (можно увеличить для длительных операций)
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Обработка webhook-запросов (для Clerk, Stripe и т.д.)
    location /api/webhook/ {
        proxy_pass http://localhost:8080/api/webhook/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Увеличенные таймауты для webhook
        proxy_connect_timeout 180s;
        proxy_send_timeout 180s;
        proxy_read_timeout 180s;
    }
    
    # Проксирование к фронтенду Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Буферизация ответов для лучшей производительности
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Включение настроек кэширования статического контента
    include /etc/nginx/conf.d/includes/cache-params.conf;
    
    # Отдача favicon.ico
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # Отдача robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
    
    # Запрет доступа к скрытым файлам
    location ~ /\. {
        deny all;
    }
}
```

### 1.4 Настройка виртуального хоста для production (quer.us)

```bash
# Создание конфигурации для quer.us
sudo nano /etc/nginx/sites-available/quer.us.conf
```

Содержимое файла `quer.us.conf`:
```nginx
# HTTP сервер - редирект на HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name quer.us www.quer.us;
    
    # Лог файлы
    access_log /var/www/quer.us/shared/logs/nginx-access.log;
    error_log /var/www/quer.us/shared/logs/nginx-error.log warn;
    
    # Редирект на HTTPS
    location / {
        return 301 https://quer.us$request_uri;
    }
    
    # Путь для проверки Let's Encrypt (если будет использоваться)
    location /.well-known/acme-challenge/ {
        root /var/www/quer.us/shared/acme;
    }
}

# HTTPS редирект с www на без www
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name www.quer.us;
    
    # SSL сертификаты (пути будут обновлены после получения сертификатов)
    ssl_certificate /etc/ssl/cloudflare/quer.us.pem;
    ssl_certificate_key /etc/ssl/cloudflare/quer.us.key;
    
    # Включение общих SSL параметров
    include /etc/nginx/conf.d/includes/ssl-params.conf;
    
    # Редирект с www на без www
    return 301 https://quer.us$request_uri;
}

# HTTPS основной сервер
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name quer.us;
    
    # Директории сайта
    root /var/www/quer.us/current/public;
    
    # Лог файлы
    access_log /var/www/quer.us/shared/logs/nginx-access.log;
    error_log /var/www/quer.us/shared/logs/nginx-error.log warn;
    
    # SSL сертификаты (пути будут обновлены после получения сертификатов)
    ssl_certificate /etc/ssl/cloudflare/quer.us.pem;
    ssl_certificate_key /etc/ssl/cloudflare/quer.us.key;
    
    # Включение общих параметров
    include /etc/nginx/conf.d/includes/ssl-params.conf;
    include /etc/nginx/conf.d/includes/compression.conf;
    
    # Проксирование запросов к API бэкенду
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Настройки таймаутов для API
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Обработка webhook-запросов (для Clerk, Stripe и т.д.)
    location /api/webhook/ {
        proxy_pass http://localhost:8080/api/webhook/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Увеличенные таймауты для webhook
        proxy_connect_timeout 180s;
        proxy_send_timeout 180s;
        proxy_read_timeout 180s;
    }
    
    # Проксирование к фронтенду Next.js
    location / {
        proxy_pass http://localhost:3100;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Буферизация ответов для лучшей производительности
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Включение настроек кэширования статического контента
    include /etc/nginx/conf.d/includes/cache-params.conf;
    
    # Отдача favicon.ico
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # Отдача robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
    
    # Запрет доступа к скрытым файлам
    location ~ /\. {
        deny all;
    }
}
```

### 1.5 Активация конфигураций и проверка синтаксиса

```bash
# Создание символических ссылок для активации конфигураций
sudo ln -sf /etc/nginx/sites-available/s.quer.us.conf /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/quer.us.conf /etc/nginx/sites-enabled/

# Удаление дефолтной конфигурации (если существует)
sudo rm -f /etc/nginx/sites-enabled/default

# Проверка конфигурации Nginx на наличие ошибок
sudo nginx -t
```

## 2. Настройка SSL для Origin сервера с Cloudflare

### 2.1 Подготовка директорий для SSL-сертификатов

```bash
# Создание директории для сертификатов Cloudflare
sudo mkdir -p /etc/ssl/cloudflare
sudo chmod 700 /etc/ssl/cloudflare
```

### 2.2 Получение Origin сертификатов от Cloudflare

Для получения Origin сертификатов необходимо перейти в панель управления Cloudflare:

1. Войдите в аккаунт Cloudflare
2. Выберите нужный домен (quer.us)
3. Перейдите в раздел SSL/TLS > Origin Server
4. Нажмите "Create Certificate"
5. Выберите следующие параметры:
   - Private key type: RSA (2048)
   - Hostnames: quer.us, *.quer.us, s.quer.us
   - Validity period: 15 years (или максимальный доступный период)
6. Нажмите "Create"
7. Скопируйте содержимое сертификата и приватного ключа

```bash
# Создание файла сертификата для quer.us
sudo nano /etc/ssl/cloudflare/quer.us.pem
# Вставьте содержимое Origin Certificate (сертификата)

# Создание файла ключа для quer.us
sudo nano /etc/ssl/cloudflare/quer.us.key
# Вставьте содержимое Private key (приватного ключа)

# Повторить то же самое для s.quer.us или скопировать те же файлы
sudo cp /etc/ssl/cloudflare/quer.us.pem /etc/ssl/cloudflare/s.quer.us.pem
sudo cp /etc/ssl/cloudflare/quer.us.key /etc/ssl/cloudflare/s.quer.us.key
```

### 2.3 Настройка прав доступа для сертификатов

```bash
# Установка правильных прав доступа для сертификатов
sudo chmod 644 /etc/ssl/cloudflare/*.pem
sudo chmod 600 /etc/ssl/cloudflare/*.key
sudo chown root:root /etc/ssl/cloudflare/*
```

Если не работает, то попробовать вот так:

```bash
# Установка правильных прав доступа для сертификатов (указываем файлы явно)
sudo chmod 644 /etc/ssl/cloudflare/quer.us.pem /etc/ssl/cloudflare/s.quer.us.pem
sudo chmod 600 /etc/ssl/cloudflare/quer.us.key /etc/ssl/cloudflare/s.quer.us.key
sudo chown root:root /etc/ssl/cloudflare/quer.us.pem /etc/ssl/cloudflare/s.quer.us.pem /etc/ssl/cloudflare/quer.us.key /etc/ssl/cloudflare/s.quer.us.key
```

### 2.4 Создание скрипта для мониторинга срока действия сертификатов

```bash
# Создание скрипта для проверки срока действия сертификатов
sudo nano /usr/local/bin/check-ssl-expiry
```

Содержимое скрипта `check-ssl-expiry`:

```bash
#!/bin/bash

# Скрипт для проверки срока истечения SSL-сертификатов

CERTS=("/etc/ssl/cloudflare/quer.us.pem" "/etc/ssl/cloudflare/s.quer.us.pem")
THRESHOLD_DAYS=30
TELEGRAM_BOT_TOKEN="**********************************************"
TELEGRAM_CHAT_ID="506972189"

# Функция для отправки уведомлений в Telegram
send_telegram_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/sendMessage" \
        -d chat_id="$TELEGRAM_CHAT_ID" \
        -d text="$message" \
        -d parse_mode="HTML"
}

for CERT in "${CERTS[@]}"; do
    if [ ! -f "$CERT" ]; then
        echo "Сертификат не найден: $CERT"
        continue
    fi
    
    # Получаем дату истечения сертификата
    EXPIRY_DATE=$(openssl x509 -enddate -noout -in "$CERT" | cut -d= -f2)
    EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_EPOCH=$(date +%s)
    
    # Вычисляем оставшееся количество дней
    DAYS_LEFT=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))
    
    echo "Сертификат $CERT истекает через $DAYS_LEFT дней"
    
    # Если остается меньше указанного количества дней, отправляем уведомление
    if [ $DAYS_LEFT -lt $THRESHOLD_DAYS ]; then
        MESSAGE="⚠️ <b>Внимание!</b> SSL сертификат $CERT истекает через $DAYS_LEFT дней!"
        send_telegram_message "$MESSAGE"
        echo "$MESSAGE"
    fi
done
```

```bash
# Делаем скрипт исполняемым
sudo chmod +x /usr/local/bin/check-ssl-expiry

# Добавляем скрипт в cron для регулярной проверки
sudo nano /etc/cron.weekly/check-ssl
```

Содержимое файла `/etc/cron.weekly/check-ssl`:
```bash
#!/bin/bash
/usr/local/bin/check-ssl-expiry
```

```bash
# Делаем cron скрипт исполняемым
sudo chmod +x /etc/cron.weekly/check-ssl
```

### 2.5 Настройка автоматического обновления сертификатов (опционально)

Если вы обновляете сертификаты Cloudflare реже чем раз в год, вы можете настроить напоминания через Telegram/Email, как в скрипте выше. Если вы хотите автоматизировать процесс обновления через Cloudflare API, необходимо создать API токен:

```bash
# Создание скрипта для автоматического обновления сертификатов через API
sudo nano /usr/local/bin/update-cloudflare-certs.py
```

Содержимое скрипта `update-cloudflare-certs.py` (требует настройки API токена Cloudflare):
```python
#!/usr/bin/env python3

import requests
import json
import os
import subprocess
from datetime import datetime

# Настройки
CLOUDFLARE_API_TOKEN = "ВАШ_API_ТОКЕН"  # Токен с доступом к Origin Certificates
ZONE_ID = "ВАШ_ZONE_ID"  # ID зоны для домена quer.us
HOSTNAMES = ["quer.us", "*.quer.us", "s.quer.us"]
CERT_VALIDITY = 5475  # 15 лет в днях
CERT_PATH = "/etc/ssl/cloudflare/"

# Заголовки для запросов API
headers = {
    "Authorization": f"Bearer {CLOUDFLARE_API_TOKEN}",
    "Content-Type": "application/json"
}

# Создание нового сертификата
def create_new_certificate():
    url = f"https://api.cloudflare.com/client/v4/zones/{ZONE_ID}/origin_certificates"
    
    payload = {
        "hostnames": HOSTNAMES,
        "requested_validity": CERT_VALIDITY,
        "request_type": "origin-rsa",
        "csr": None
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        data = response.json()
        if data["success"]:
            cert = data["result"]["certificate"]
            key = data["result"]["private_key"]
            
            # Сохранение сертификата и ключа
            with open(f"{CERT_PATH}quer.us.pem.new", "w") as f:
                f.write(cert)
            
            with open(f"{CERT_PATH}quer.us.key.new", "w") as f:
                f.write(key)
            
            # Настройка прав доступа
            os.chmod(f"{CERT_PATH}quer.us.pem.new", 0o644)
            os.chmod(f"{CERT_PATH}quer.us.key.new", 0o600)
            
            # Копирование для s.quer.us
            subprocess.run(f"cp {CERT_PATH}quer.us.pem.new {CERT_PATH}s.quer.us.pem.new", shell=True)
            subprocess.run(f"cp {CERT_PATH}quer.us.key.new {CERT_PATH}s.quer.us.key.new", shell=True)
            
            # Перемещение новых файлов
            subprocess.run(f"mv {CERT_PATH}quer.us.pem.new {CERT_PATH}quer.us.pem", shell=True)
            subprocess.run(f"mv {CERT_PATH}quer.us.key.new {CERT_PATH}quer.us.key", shell=True)
            subprocess.run(f"mv {CERT_PATH}s.quer.us.pem.new {CERT_PATH}s.quer.us.pem", shell=True)
            subprocess.run(f"mv {CERT_PATH}s.quer.us.key.new {CERT_PATH}s.quer.us.key", shell=True)
            
            # Перезапуск Nginx
            subprocess.run("systemctl reload nginx", shell=True)
            
            print(f"Certificate renewed successfully - {datetime.now()}")
            return True
        else:
            print(f"Failed to renew certificate: {data['errors']}")
            return False
    else:
        print(f"Request failed with status code {response.status_code}: {response.text}")
        return False

if __name__ == "__main__":
    create_new_certificate()
```

```bash
# Делаем скрипт исполняемым
sudo chmod +x /usr/local/bin/update-cloudflare-certs.py

# Устанавливаем необходимые зависимости
sudo apt-get install -y python3-requests

# Создаем cron задачу для запуска обновления раз в год
sudo nano /etc/cron.d/update-certs
```

Содержимое файла `/etc/cron.d/update-certs`:
```
# Запуск обновления сертификатов в первый день года в 3 часа ночи
0 3 1 1 * root /usr/local/bin/update-cloudflare-certs.py >> /var/log/update-cloudflare-certs.log 2>&1
```

## 3. Оптимизация производительности Nginx

### 3.1 Настройка общих параметров Nginx

```bash
# Редактирование основного файла конфигурации Nginx
sudo nano /etc/nginx/nginx.conf
```

Оптимизированная конфигурация `nginx.conf`:
```nginx
user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

# Оптимизация количества соединений
events {
    worker_connections 1024;
    multi_accept on;
    use epoll;
}

http {
    # Базовые настройки
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    server_tokens off;
    
    # Настройки таймаутов
    keepalive_timeout 65;
    keepalive_requests 100;
    client_body_timeout 10;
    client_header_timeout 10;
    send_timeout 10;
    
    # Настройки буферов
    client_max_body_size 100m;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Типы MIME
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Настройки логирования
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Включение сжатия
    include /etc/nginx/conf.d/includes/compression.conf;
    
    # Настройки кэширования для FastCGI, uwsgi и SCGI
    fastcgi_cache_path /var/cache/nginx levels=1:2 keys_zone=microcache:10m max_size=1000m inactive=60m;
    fastcgi_cache_key "$scheme$request_method$host$request_uri";
    
    # Настройки кэширования для проксирования
    proxy_cache_path /var/cache/nginx/proxy levels=1:2 keys_zone=proxy_cache:10m max_size=1000m inactive=60m;
    proxy_temp_path /var/cache/nginx/proxy_temp;
    
    # Настройки файла конфигурации кэширования
    map $sent_http_content_type $expires {
        default                    off;
        text/html                  epoch;
        text/css                   max;
        application/javascript     max;
        ~image/                    max;
        ~font/                     max;
    }
    
    # Включение виртуальных хостов
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
```

### 3.2 Создание структуры для кэширования

```bash
# Создание директорий для кэширования
sudo mkdir -p /var/cache/nginx/proxy
sudo chown -R www-data:www-data /var/cache/nginx
```

### 3.3 Настройка Brotli для более эффективного сжатия (опционально)

```bash
# Установка модуля Brotli
sudo apt install -y nginx-module-brotli

# Создание конфигурации Brotli
sudo nano /etc/nginx/conf.d/includes/brotli.conf
```

Содержимое файла `brotli.conf`:
```nginx
# Загрузка модуля Brotli (если установлен динамически)
load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;

# Настройки Brotli сжатия
brotli on;
brotli_comp_level 6;
brotli_static on;
brotli_types
    application/atom+xml
    application/javascript
    application/json
    application/ld+json
    application/manifest+json
    application/rss+xml
    application/vnd.geo+json
    application/vnd.ms-fontobject
    application/wasm
    application/x-font-ttf
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/eot
    font/otf
    font/ttf
    image/bmp
    image/svg+xml
    text/cache-manifest
    text/calendar
    text/css
    text/javascript
    text/markdown
    text/plain
    text/xml
    text/vcard
    text/vnd.rim.location.xloc
    text/vtt
    text/x-component
    text/x-cross-domain-policy;
```

```bash
# Включение Brotli в основном конфигурационном файле
sudo nano /etc/nginx/nginx.conf
```

Добавьте строку для включения Brotli в раздел `http` после строки включения compression.conf:
```nginx
# Включение сжатия
include /etc/nginx/conf.d/includes/compression.conf;
include /etc/nginx/conf.d/includes/brotli.conf;  # Добавьте эту строку
```

### 3.4 Настройка оптимального кэширования различных типов контента

```bash
# Создание расширенного файла настроек кэширования
sudo nano /etc/nginx/conf.d/includes/cache-control.conf
```

Содержимое файла `cache-control.conf`:
```nginx
# Настройки Cache-Control заголовков для разных типов контента

# HTML файлы - небольшое время кэширования
location ~* \.(?:html|htm)$ {
    expires 1h;
    add_header Cache-Control "public, max-age=3600";
}

# CSS и JavaScript с версионированием (_v1, .min, хеш в URL)
location ~* \.(?:css|js)(?:$|\?|#|_v[0-9]+) {
    expires 1y;
    add_header Cache-Control "public, max-age=31536000, immutable";
}

# Изображения и медиа
location ~* \.(?:jpg|jpeg|gif|png|ico|cur|webp|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
    expires 1M;
    add_header Cache-Control "public, max-age=2592000";
}

# Веб-шрифты
location ~* \.(?:ttf|ttc|otf|eot|woff|woff2)$ {
    expires 1y;
    add_header Cache-Control "public, max-age=31536000, immutable";
}

# Данные и другие файлы
location ~* \.(?:json|xml)$ {
    expires 1d;
    add_header Cache-Control "public, max-age=86400";
}

# PDF документы
location ~* \.pdf$ {
    expires 1M;
    add_header Cache-Control "public, max-age=2592000";
}

# Специфичные настройки для Next.js
location /_next/static/ {
    expires 1y;
    add_header Cache-Control "public, max-age=31536000, immutable";
}

location /_next/image {
    expires 1w;
    add_header Cache-Control "public, max-age=604800";
}

location /_next/data/ {
    expires 1h;
    add_header Cache-Control "public, max-age=3600";
}
```

Добавьте включение этого файла в конфигурации виртуальных хостов:
```bash
# Редактирование конфигурации для staging
sudo nano /etc/nginx/sites-available/s.quer.us.conf
```

Добавьте строку перед блоком `location /`:
```nginx
# Включаем настройки Cache-Control
include /etc/nginx/conf.d/includes/cache-control.conf;
```

```bash
# То же самое для production
sudo nano /etc/nginx/sites-available/quer.us.conf
```

### 3.5 Настройка микрокэширования для динамического контента

```bash
# Создание конфигурации микрокэширования
sudo nano /etc/nginx/conf.d/includes/microcaching.conf
```

Содержимое файла `microcaching.conf`:
```nginx
# Настройки микрокэширования динамического контента (за исключением админ-панели)

# Настройка кэша для proxy
proxy_cache proxy_cache;
proxy_cache_valid 200 302 10m;
proxy_cache_valid 404 1m;

# Исключения для кэша (не кэшировать API и запросы, требующие авторизации)
map $request_uri $skip_cache {
    default              0;
    ~/api/               1;
    ~/subscribe          1;
    ~/sign-in            1;
    ~/sign-up            1;
    ~/profile            1;
}

# Установка ключа кэша
proxy_cache_key "$scheme$host$request_uri$cookie_user";

# Настройка bypass и использования устаревшего кэша при ошибках
proxy_cache_bypass $skip_cache;
proxy_no_cache $skip_cache;
proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
```

```bash
# Включение микрокэширования в конфигурациях виртуальных хостов
sudo nano /etc/nginx/sites-available/s.quer.us.conf
```

Добавьте строку перед блоком настроек проксирования фронтенда:
```nginx
# Включение микрокэширования
include /etc/nginx/conf.d/includes/microcaching.conf;
```

```bash
# То же самое для production
sudo nano /etc/nginx/sites-available/quer.us.conf
```

### 3.6 Применение конфигурации и тестирование

```bash
# Проверка конфигурации
sudo nginx -t

# Применение настроек
sudo systemctl reload nginx

# Проверка статуса Nginx
sudo systemctl status nginx
```
########################################## До сюда выполнено ##########################################
## 4. Проверка и тестирование конфигурации

### 4.1 Проверка SSL сертификатов

```bash
# Проверка сертификатов через OpenSSL
sudo openssl x509 -in /etc/ssl/cloudflare/quer.us.pem -text -noout | grep -A2 Validity
sudo openssl x509 -in /etc/ssl/cloudflare/s.quer.us.pem -text -noout | grep -A2 Validity
```

### 4.2 Проверка настроек и заголовков

С помощью внешних инструментов можно проверить конфигурацию: [SSL Labs](https://www.ssllabs.com/ssltest/) и [Security Headers](https://securityheaders.com/)

```bash
# Локальная проверка заголовков
curl -s -I https://quer.us | grep -E '(^HTTP|Cache-Control|Content-Encoding|Strict-Transport-Security|X-Content|X-Frame|X-XSS)'
```

### 4.3 Настройка мониторинга для SSL сертификатов

```bash
# Запуск ручной проверки SSL сертификатов
sudo /usr/local/bin/check-ssl-expiry
```

## 5. Доработка дополнительных настроек безопасности

### 5.1 Настройка общих параметров безопасности Nginx (опционально)

```bash
# Создание файла с настройками безопасности
sudo nano /etc/nginx/conf.d/includes/security.conf
```

Содержимое файла `security.conf`:
```nginx
# Блокировка доступа к файлам .htaccess и конфигурациям
location ~ /\.(?!well-known\/) {
    deny all;
}

# Блокировка доступа к .git и другим служебным директориям
location ~ /(\.git|\.svn|\.hg|\.DS_Store) {
    deny all;
}

# Блокировка выполнения PHP в директориях загрузок
location ~* ^/(uploads|files)/.*\.php$ {
    deny all;
}

# Дополнительные заголовки безопасности
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;
```

```bash
# Включение файла безопасности в конфигурацию
sudo nano /etc/nginx/sites-available/s.quer.us.conf
sudo nano /etc/nginx/sites-available/quer.us.conf
```

Добавьте строку в раздел server:
```nginx
# Настройки безопасности
include /etc/nginx/conf.d/includes/security.conf;
```

### 5.2 Настройка защиты от атак с перебором паролей (Rate Limiting)

```bash
# Создание конфигурации для ограничения частоты запросов
sudo nano /etc/nginx/conf.d/includes/rate-limits.conf
```

Содержимое файла `rate-limits.conf`:
```nginx
# Зоны для хранения состояний ограничений
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;
limit_req_zone $binary_remote_addr zone=global:10m rate=10r/s;

# Общее ограничение для IP-адресов
limit_conn_zone $binary_remote_addr zone=addr:10m;
limit_conn addr 100;

# Настройка лимитов для конкретных URL
# API ограничения
location ~* ^/api/(?!webhook) {
    limit_req zone=api burst=10 nodelay;
}

# Аутентификация ограничения
location ~* ^/(sign-in|sign-up|subscribe) {
    limit_req zone=auth burst=5 nodelay;
}

# Не применять ограничения для API webhook
location ~* ^/api/webhook {
    limit_req_status 429;
    limit_req zone=global burst=20 nodelay;
}
```

Добавьте включение этого файла в конфигурации виртуальных хостов:
```bash
# Редактирование конфигурации для staging и production
sudo nano /etc/nginx/sites-available/s.quer.us.conf
sudo nano /etc/nginx/sites-available/quer.us.conf
```

Добавьте строку перед настройками локаций:
```nginx
# Включение ограничения частоты запросов
include /etc/nginx/conf.d/includes/rate-limits.conf;
```

## 6. Заключительная проверка и применение конфигурации

```bash
# Проверка конфигурации
sudo nginx -t

# Применение настроек
sudo systemctl reload nginx

# Проверка статуса Nginx
sudo systemctl status nginx
```

## Заключение

Теперь у вас настроена оптимизированная инфраструктура Nginx с поддержкой SSL через Cloudflare для проекта quer-calc. 

Основные компоненты настройки:
1. Оптимизированные виртуальные хосты для staging и production окружений
2. Настроенные SSL-сертификаты от Cloudflare для безопасного соединения
3. Оптимизация производительности с помощью:
   - Сжатия контента (Gzip и Brotli)
   - HTTP/2 для более быстрой загрузки
   - Интеллектуального кэширования разных типов ресурсов
   - Микрокэширования для динамического контента
4. Дополнительные настройки безопасности и ограничения

Регулярно проверяйте срок действия сертификатов с помощью настроенного скрипта и обновляйте их по мере необходимости. Также рекомендуется периодически проверять настройки и производительность с помощью инструментов как SSL Labs, Security Headers и PageSpeed Insights.

## 7. Временная защита

Добавление HTTP Basic Authentication (запрос логина и пароля в браузере) позволит вам протестировать SSL-сертификаты и заголовки безопасности, но при этом ограничит доступ к сайту. Это хорошее временное решение, пока вы настраиваете DNS в Cloudflare.

Вот как настроить базовую аутентификацию в Nginx:

### 1. Создание файла с паролем

```bash
# Установка пакета apache2-utils для команды htpasswd
sudo apt install apache2-utils

# Создание директории для хранения файлов с паролями
sudo mkdir -p /etc/nginx/auth

# Создание пользователя (замените "admin" на желаемое имя пользователя)
sudo htpasswd -c /etc/nginx/auth/.htpasswd xxx
# Система попросит ввести и подтвердить пароль (eBqN28tyP)
```

### 2. Настройка аутентификации в конфигурации Nginx

Отредактируйте оба файла конфигурации:

```bash
sudo nano /etc/nginx/sites-available/quer.us.conf
sudo nano /etc/nginx/sites-available/s.quer.us.conf
```

В каждом файле добавьте настройки аутентификации в блок `location /`:

```nginx
location / {
    # Добавьте эти строки для аутентификации
    auth_basic "Restricted Area";
    auth_basic_user_file /etc/nginx/auth/.htpasswd;
    
    # Существующие настройки прокси
    proxy_pass http://localhost:3000;  # или 3100 для production
    proxy_http_version 1.1;
    # ... остальные настройки прокси ...
}
```

### 3. Применение изменений

```bash
# Проверка конфигурации
sudo nginx -t

# Если всё в порядке, перезагрузите Nginx
sudo systemctl reload nginx
```

Теперь при попытке доступа к вашему сайту браузер будет запрашивать имя пользователя и пароль. Это не помешает тестированию SSL-сертификатов и заголовков безопасности, так как аутентификация происходит уже после установления защищенного соединения.

Когда вы будете готовы открыть сайт для публичного доступа, просто удалите строки `auth_basic` и `auth_basic_user_file` из конфигурации.
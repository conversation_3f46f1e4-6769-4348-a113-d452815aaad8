# План резервного копирования и обслуживания

## 1. Настройка бэкапов

### 1.1. Резервное копирование базы данных

#### Для SQL баз данных (PostgreSQL, MySQL)

```bash
#!/bin/bash
# backup-database.sh

# Настройка переменных
DB_USER="имя_пользователя"
DB_NAME="имя_базы_данных"
BACKUP_DIR="/path/to/backups/db"
DATE=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILE="$BACKUP_DIR/$DB_NAME-$DATE.sql"
RETAIN_DAYS=30

# Создание директории для резервных копий, если она не существует
mkdir -p $BACKUP_DIR

# Выполнение резервного копирования
if [ "$DB_TYPE" = "postgres" ]; then
    PGPASSWORD="$DB_PASSWORD" pg_dump -U $DB_USER $DB_NAME > $BACKUP_FILE
elif [ "$DB_TYPE" = "mysql" ]; then
    mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_FILE
fi

# Сжатие файла резервной копии
gzip $BACKUP_FILE

# Удаление старых резервных копий
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETAIN_DAYS -delete

# Логирование результата
echo "Backup completed: $BACKUP_FILE.gz" >> $BACKUP_DIR/backup.log
```

**Настройка cron-задания для ежедневного выполнения:**

```
# Запуск в 3:00 утра каждый день
0 3 * * * /path/to/backup-database.sh
```

#### Для NoSQL баз данных (MongoDB)

```bash
#!/bin/bash
# backup-mongodb.sh

# Настройка переменных
MONGO_URI="*******************************************"
DB_NAME="имя_базы_данных"
BACKUP_DIR="/path/to/backups/mongo"
DATE=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILE="$BACKUP_DIR/$DB_NAME-$DATE"
RETAIN_DAYS=30

# Создание директории для резервных копий, если она не существует
mkdir -p $BACKUP_DIR

# Выполнение резервного копирования
mongodump --uri=$MONGO_URI --db=$DB_NAME --out=$BACKUP_FILE

# Сжатие файла резервной копии
tar -czf "$BACKUP_FILE.tar.gz" -C $BACKUP_DIR "$DB_NAME-$DATE"
rm -rf $BACKUP_FILE

# Удаление старых резервных копий
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETAIN_DAYS -delete

# Логирование результата
echo "Backup completed: $BACKUP_FILE.tar.gz" >> $BACKUP_DIR/backup.log
```

### 1.2. Бэкапы конфигурационных файлов и переменных окружения

```bash
#!/bin/bash
# backup-configs.sh

# Настройка переменных
APP_DIR="/path/to/application"
CONFIG_DIR="$APP_DIR/config"
ENV_FILES=(".env" ".env.production" ".env.local")
BACKUP_DIR="/path/to/backups/configs"
DATE=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILE="$BACKUP_DIR/configs-$DATE.tar.gz"
RETAIN_DAYS=60

# Создание директории для резервных копий, если она не существует
mkdir -p $BACKUP_DIR

# Создание временной директории для сбора конфигурационных файлов
TEMP_DIR=$(mktemp -d)

# Копирование конфигурационных файлов
if [ -d "$CONFIG_DIR" ]; then
    cp -r $CONFIG_DIR $TEMP_DIR/
fi

# Копирование .env файлов
for ENV_FILE in "${ENV_FILES[@]}"; do
    if [ -f "$APP_DIR/$ENV_FILE" ]; then
        cp "$APP_DIR/$ENV_FILE" "$TEMP_DIR/"
    fi
done

# Копирование других важных файлов конфигурации
if [ -f "$APP_DIR/next.config.js" ]; then
    cp "$APP_DIR/next.config.js" "$TEMP_DIR/"
fi

if [ -f "$APP_DIR/backend-rust/Cargo.toml" ]; then
    cp "$APP_DIR/backend-rust/Cargo.toml" "$TEMP_DIR/"
fi

# Создание архива с конфигурационными файлами
tar -czf $BACKUP_FILE -C $TEMP_DIR .

# Удаление временной директории
rm -rf $TEMP_DIR

# Удаление старых резервных копий
find $BACKUP_DIR -name "configs-*.tar.gz" -mtime +$RETAIN_DAYS -delete

# Логирование результата
echo "Config backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
```

**Настройка cron-задания для еженедельного выполнения:**

```
# Запуск в 4:00 утра каждое воскресенье
0 4 * * 0 /path/to/backup-configs.sh
```

### 1.3. Резервное копирование пользовательских данных

```bash
#!/bin/bash
# backup-user-data.sh

# Настройка переменных
APP_DIR="/path/to/application"
DATA_DIR="$APP_DIR/user_data"
BACKUP_DIR="/path/to/backups/user_data"
DATE=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILE="$BACKUP_DIR/user_data-$DATE.tar.gz"
RETAIN_DAYS=30

# Создание директории для резервных копий, если она не существует
mkdir -p $BACKUP_DIR

# Проверка существования директории с пользовательскими данными
if [ ! -d "$DATA_DIR" ]; then
    echo "User data directory not found: $DATA_DIR" >> $BACKUP_DIR/backup.log
    exit 1
fi

# Создание архива с пользовательскими данными
tar -czf $BACKUP_FILE -C $APP_DIR user_data

# Удаление старых резервных копий
find $BACKUP_DIR -name "user_data-*.tar.gz" -mtime +$RETAIN_DAYS -delete

# Логирование результата
echo "User data backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
```

### 1.4. Резервное копирование всего проекта (полный бэкап)

```bash
#!/bin/bash
# backup-full-project.sh

# Настройка переменных
APP_DIR="/path/to/application"
BACKUP_DIR="/path/to/backups/full"
DATE=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILE="$BACKUP_DIR/full-$DATE.tar.gz"
EXCLUDE_DIRS=("node_modules" "target" ".next" "dist" "backup")
RETAIN_DAYS=90

# Создание директории для резервных копий, если она не существует
mkdir -p $BACKUP_DIR

# Формирование параметров исключения директорий
EXCLUDE_PARAMS=""
for DIR in "${EXCLUDE_DIRS[@]}"; do
    EXCLUDE_PARAMS="$EXCLUDE_PARAMS --exclude=$DIR"
done

# Создание полной резервной копии проекта
tar -czf $BACKUP_FILE $EXCLUDE_PARAMS -C $(dirname $APP_DIR) $(basename $APP_DIR)

# Удаление старых резервных копий
find $BACKUP_DIR -name "full-*.tar.gz" -mtime +$RETAIN_DAYS -delete

# Логирование результата
echo "Full project backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
```

**Настройка cron-задания для ежемесячного выполнения:**

```
# Запуск в 2:00 утра в первый день каждого месяца
0 2 1 * * /path/to/backup-full-project.sh
```

### 1.5. Загрузка резервных копий в облачное хранилище

```bash
#!/bin/bash
# upload-backups-to-cloud.sh

# Настройка переменных
BACKUP_DIR="/path/to/backups"
CLOUD_TYPE="s3" # Допустимые значения: s3, gcloud, azure

# Проверка наличия свежих резервных копий (созданных в последние 24 часа)
RECENT_BACKUPS=$(find $BACKUP_DIR -type f -name "*.tar.gz" -o -name "*.sql.gz" -mtime -1)

if [ -z "$RECENT_BACKUPS" ]; then
    echo "No recent backups found to upload" >> $BACKUP_DIR/cloud-upload.log
    exit 0
fi

# Загрузка резервных копий в облачное хранилище
for BACKUP in $RECENT_BACKUPS; do
    BACKUP_FILENAME=$(basename $BACKUP)
    
    case $CLOUD_TYPE in
        s3)
            # Загрузка в AWS S3
            aws s3 cp $BACKUP s3://your-bucket-name/backups/$BACKUP_FILENAME
            RESULT=$?
            ;;
        gcloud)
            # Загрузка в Google Cloud Storage
            gsutil cp $BACKUP gs://your-bucket-name/backups/$BACKUP_FILENAME
            RESULT=$?
            ;;
        azure)
            # Загрузка в Azure Blob Storage
            az storage blob upload --container-name your-container --file $BACKUP --name backups/$BACKUP_FILENAME
            RESULT=$?
            ;;
        *)
            echo "Unknown cloud type: $CLOUD_TYPE" >> $BACKUP_DIR/cloud-upload.log
            exit 1
    esac
    
    if [ $RESULT -eq 0 ]; then
        echo "Successfully uploaded $BACKUP to cloud storage" >> $BACKUP_DIR/cloud-upload.log
    else
        echo "Failed to upload $BACKUP to cloud storage" >> $BACKUP_DIR/cloud-upload.log
    fi
done
```

**Настройка cron-задания для загрузки резервных копий:**

```
# Запуск в 6:00 утра каждый день
0 6 * * * /path/to/upload-backups-to-cloud.sh
```

## 2. Автоматизация обслуживания

### 2.1. Скрипты для очистки временных файлов

```bash
#!/bin/bash
# cleanup-temp-files.sh

# Настройка переменных
APP_DIR="/path/to/application"
LOG_DIR="$APP_DIR/logs"
TEMP_DIRS=("$APP_DIR/tmp" "$APP_DIR/.next/cache" "$APP_DIR/backend-rust/target/debug")
LOG_RETENTION_DAYS=14

# Очистка временных директорий
for DIR in "${TEMP_DIRS[@]}"; do
    if [ -d "$DIR" ]; then
        echo "Cleaning temporary directory: $DIR"
        find "$DIR" -type f -mtime +1 -delete
        find "$DIR" -type d -empty -delete
    fi
done

# Очистка старых файлов логов
if [ -d "$LOG_DIR" ]; then
    echo "Cleaning old log files older than $LOG_RETENTION_DAYS days"
    find "$LOG_DIR" -type f -name "*.log" -mtime +$LOG_RETENTION_DAYS -delete
fi

# Очистка артефактов сборки, которые не нужны в production
if [ -d "$APP_DIR/.next/cache" ]; then
    echo "Cleaning Next.js build cache"
    rm -rf "$APP_DIR/.next/cache/images"
fi

echo "Temporary files cleanup completed at $(date)" >> "$APP_DIR/maintenance.log"
```

**Настройка cron-задания для очистки:**

```
# Запуск в 1:00 утра каждый день
0 1 * * * /path/to/cleanup-temp-files.sh
```

### 2.2. Автоматическое обновление зависимостей

```bash
#!/bin/bash
# update-dependencies.sh

# Настройка переменных
APP_DIR="/path/to/application"
FRONTEND_DIR="$APP_DIR"
BACKEND_DIR="$APP_DIR/backend-rust"
LOG_FILE="$APP_DIR/updates.log"
UPDATE_BRANCH="dependency-updates-$(date +%Y-%m-%d)"

# Функция для логирования
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> $LOG_FILE
}

# Переход в директорию приложения
cd $APP_DIR || { log "Failed to change directory to $APP_DIR"; exit 1; }

# Создание новой ветки Git для обновлений
git checkout -b $UPDATE_BRANCH || { log "Failed to create new Git branch"; exit 1; }

# Обновление зависимостей фронтенда
log "Updating frontend dependencies..."
cd $FRONTEND_DIR || { log "Failed to change directory to $FRONTEND_DIR"; exit 1; }

# Проверка наличия устаревших зависимостей и их обновление
npm outdated --json > outdated.json
if [ -s outdated.json ]; then
    log "Outdated NPM packages found, updating..."
    npm update || { log "Failed to update NPM packages"; exit 1; }
    log "NPM packages updated successfully"
else
    log "No outdated NPM packages found"
fi
rm outdated.json

# Обновление зависимостей бэкенда
log "Updating backend dependencies..."
cd $BACKEND_DIR || { log "Failed to change directory to $BACKEND_DIR"; exit 1; }

# Обновление Cargo.toml с помощью cargo-edit
if command -v cargo-edit >/dev/null 2>&1; then
    cargo update || { log "Failed to update Rust dependencies"; exit 1; }
    log "Rust dependencies updated successfully"
else
    log "cargo-edit not found, install it with: cargo install cargo-edit"
fi

# Возврат в директорию приложения
cd $APP_DIR || { log "Failed to change directory to $APP_DIR"; exit 1; }

# Запуск тестов, чтобы проверить работоспособность после обновления
log "Running tests after dependency updates..."

# Тесты фронтенда
cd $FRONTEND_DIR
if npm test; then
    log "Frontend tests passed successfully"
else
    log "Frontend tests failed after dependency updates"
    git checkout -- package.json package-lock.json
    log "Reverted frontend dependency updates"
fi

# Тесты бэкенда
cd $BACKEND_DIR
if cargo test; then
    log "Backend tests passed successfully"
else
    log "Backend tests failed after dependency updates"
    git checkout -- Cargo.toml Cargo.lock
    log "Reverted backend dependency updates"
fi

# Коммит изменений, если есть что коммитить
cd $APP_DIR
if git diff --quiet; then
    log "No changes to commit"
else
    git add .
    git commit -m "Update dependencies (automated)" || { log "Failed to commit changes"; exit 1; }
    log "Changes committed to branch $UPDATE_BRANCH"
    
    # Отправляем изменения в удаленный репозиторий
    # Раскомментируйте для автоматической отправки:
    # git push origin $UPDATE_BRANCH
    # log "Changes pushed to remote repository"
    
    log "Please review the changes in branch $UPDATE_BRANCH and merge them manually"
fi
```

**Настройка cron-задания для еженедельного обновления зависимостей:**

```
# Запуск в 3:00 утра каждую субботу
0 3 * * 6 /path/to/update-dependencies.sh
```

### 2.3. Периодическая проверка на уязвимости

```bash
#!/bin/bash
# security-scan.sh

# Настройка переменных
APP_DIR="/path/to/application"
FRONTEND_DIR="$APP_DIR"
BACKEND_DIR="$APP_DIR/backend-rust"
REPORT_DIR="$APP_DIR/security_reports"
DATE=$(date +"%Y-%m-%d")
LOG_FILE="$REPORT_DIR/security_scan_$DATE.log"

# Создание директории для отчетов, если она не существует
mkdir -p $REPORT_DIR

# Функция для логирования
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" | tee -a $LOG_FILE
}

log "Starting security scan for application"

# Проверка зависимостей NPM на уязвимости
log "Scanning frontend dependencies for vulnerabilities..."
cd $FRONTEND_DIR || { log "Failed to change directory to $FRONTEND_DIR"; exit 1; }
npm audit --json > "$REPORT_DIR/npm_audit_$DATE.json"
npm_vulns=$(grep -c "severity" "$REPORT_DIR/npm_audit_$DATE.json")
if [ "$npm_vulns" -gt 0 ]; then
    log "Found $npm_vulns potential vulnerabilities in NPM packages. See report for details."
else
    log "No vulnerabilities found in NPM packages."
fi

# Проверка зависимостей Rust на уязвимости с помощью cargo-audit
log "Scanning Rust dependencies for vulnerabilities..."
cd $BACKEND_DIR || { log "Failed to change directory to $BACKEND_DIR"; exit 1; }
if command -v cargo-audit >/dev/null 2>&1; then
    cargo audit > "$REPORT_DIR/cargo_audit_$DATE.txt"
    rust_vulns=$(grep -c "RUSTSEC" "$REPORT_DIR/cargo_audit_$DATE.txt")
    if [ "$rust_vulns" -gt 0 ]; then
        log "Found $rust_vulns potential vulnerabilities in Rust packages. See report for details."
    else
        log "No vulnerabilities found in Rust packages."
    fi
else
    log "cargo-audit not found, install it with: cargo install cargo-audit"
fi

# Проверка на наличие секретов в коде с помощью git-secrets
log "Scanning for secrets in codebase..."
cd $APP_DIR || { log "Failed to change directory to $APP_DIR"; exit 1; }
if command -v git-secrets >/dev/null 2>&1; then
    git-secrets --scan > "$REPORT_DIR/git_secrets_$DATE.txt" 2>&1
    if [ $? -ne 0 ]; then
        log "Potential secrets found in repository. See report for details."
    else
        log "No secrets found in repository."
    fi
else
    log "git-secrets not found, install it from: https://github.com/awslabs/git-secrets"
fi

# Статический анализ кода (опционально, если установлены инструменты)
log "Running static code analysis..."

# Для JavaScript/TypeScript (ESLint)
if command -v eslint >/dev/null 2>&1; then
    cd $FRONTEND_DIR
    eslint . -o "$REPORT_DIR/eslint_report_$DATE.txt" -f unix || log "ESLint found issues"
fi

# Для Rust (Clippy)
if command -v cargo-clippy >/dev/null 2>&1; then
    cd $BACKEND_DIR
    cargo clippy --message-format=json > "$REPORT_DIR/clippy_report_$DATE.json" 2>&1
fi

log "Security scan completed. Reports saved to $REPORT_DIR"

# Отправка отчета по электронной почте (опционально)
if command -v mail >/dev/null 2>&1; then
    cat $LOG_FILE | mail -s "Security Scan Report - $DATE" <EMAIL>
    log "Security report sent via email"
fi
```

**Настройка cron-задания для ежемесячной проверки безопасности:**

```
# Запуск в 2:00 утра в первое воскресенье каждого месяца
0 2 1-7 * 0 [ "$(date +\%w)" = "0" ] && /path/to/security-scan.sh
```

### 2.4. Мониторинг состояния сервера и приложения

```bash
#!/bin/bash
# system-health-check.sh

# Настройка переменных
APP_DIR="/path/to/application"
LOG_DIR="$APP_DIR/logs"
HEALTH_LOG="$LOG_DIR/health_check.log"
API_ENDPOINT="http://localhost:8080/api/health"
APP_PORT=3000
SLACK_WEBHOOK_URL="https://hooks.slack.com/services/xxx/yyy/zzz"  # Опционально

# Создание директории для логов, если она не существует
mkdir -p $LOG_DIR

# Функция для логирования
log() {
    echo "[$(date +"%Y-%m-%d %H:%M:%S")] $1" >> $HEALTH_LOG
}

# Функция для отправки уведомлений в Slack
send_slack_notification() {
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$1\"}" $SLACK_WEBHOOK_URL
    fi
}

log "Starting system health check"

# Проверка использования CPU
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')
log "CPU Usage: $CPU_USAGE%"
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    log "WARNING: High CPU usage detected"
    send_slack_notification "Warning: High CPU usage ($CPU_USAGE%) on server"
fi

# Проверка использования памяти
MEMORY_USAGE=$(free | grep Mem | awk '{print $3/$2 * 100.0}')
log "Memory Usage: $MEMORY_USAGE%"
if (( $(echo "$MEMORY_USAGE > 85" | bc -l) )); then
    log "WARNING: High memory usage detected"
    send_slack_notification "Warning: High memory usage ($MEMORY_USAGE%) on server"
fi

# Проверка использования диска
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
log "Disk Usage: $DISK_USAGE%"
if [ "$DISK_USAGE" -gt 85 ]; then
    log "WARNING: High disk usage detected"
    send_slack_notification "Warning: High disk usage ($DISK_USAGE%) on server"
fi

# Проверка доступности API
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $API_ENDPOINT)
log "API Status Code: $API_STATUS"
if [ "$API_STATUS" != "200" ]; then
    log "ERROR: API is not responding properly"
    send_slack_notification "Error: API is not responding properly (Status code: $API_STATUS)"
fi

# Проверка доступности приложения
APP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$APP_PORT)
log "App Status Code: $APP_STATUS"
if [ "$APP_STATUS" != "200" ]; then
    log "ERROR: Application is not responding properly"
    send_slack_notification "Error: Application is not responding properly (Status code: $APP_STATUS)"
fi

# Проверка количества процессов
PROCESS_COUNT=$(ps aux | wc -l)
log "Process Count: $PROCESS_COUNT"
if [ "$PROCESS_COUNT" -gt 500 ]; then
    log "WARNING: High number of processes"
    send_slack_notification "Warning: High number of processes ($PROCESS_COUNT) on server"
fi

# Проверка логов на наличие критических ошибок
if [ -d "$LOG_DIR" ]; then
    ERROR_COUNT=$(grep -i "error\|exception\|fatal" $LOG_DIR/*.log 2>/dev/null | wc -l)
    log "Error Count in Logs: $ERROR_COUNT"
    if [ "$ERROR_COUNT" -gt 50 ]; then
        log "WARNING: High number of errors in logs"
        send_slack_notification "Warning: High number of errors ($ERROR_COUNT) in application logs"
    fi
fi

log "System health check completed"
```

**Настройка cron-задания для периодической проверки состояния:**

```
# Запуск каждый час
0 * * * * /path/to/system-health-check.sh
```

## 3. Документирование процессов

### 3.1. Создание руководства по деплою

**Структура руководства по деплою (deployment-guide.md):**

```markdown
# Руководство по деплою приложения

## Оглавление
1. Требования к системе
2. Установка зависимостей
3. Настройка окружения
4. Деплой бэкенда
5. Деплой фронтенда
6. Настройка Nginx и SSL
7. Проверка после деплоя
8. Устранение распространенных проблем

## 1. Требования к системе

- Linux сервер (рекомендуется Ubuntu 20.04 LTS или новее)
- Минимум 2 ГБ RAM, рекомендуется 4 ГБ
- Минимум 20 ГБ свободного места на диске
- Node.js 18.x или новее
- Rust 1.70 или новее
- Nginx 1.18 или новее
- Git

## 2. Установка зависимостей

### Установка Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Установка Rust
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source $HOME/.cargo/env
```

### Установка Nginx
```bash
sudo apt-get install -y nginx
```

### Установка PM2 для управления Node.js процессами
```bash
sudo npm install -g pm2
```

## 3. Настройка окружения

### Клонирование репозитория
```bash
git clone https://github.com/your-organization/your-repo.git
cd your-repo
```

### Создание файла .env
```bash
cp .env.example .env
nano .env
```

Отредактируйте значения переменных окружения согласно вашей среде.

## 4. Деплой бэкенда

### Сборка Rust-бэкенда
```bash
cd backend-rust
cargo build --release
```

### Создание systemd-сервиса для бэкенда

Создайте файл `/etc/systemd/system/your-app-backend.service`:

```ini
[Unit]
Description=Your App Backend Service
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/application/backend-rust
ExecStart=/path/to/application/backend-rust/target/release/your-app
Restart=always
Environment=RUST_ENV=production
Environment=PORT=8080

[Install]
WantedBy=multi-user.target
```

Активируйте и запустите сервис:

```bash
sudo systemctl enable your-app-backend
sudo systemctl start your-app-backend
```

## 5. Деплой фронтенда

### Установка зависимостей фронтенда
```bash
cd /path/to/application
npm install
```

### Сборка фронтенда
```bash
npm run build
```

### Запуск приложения с PM2
```bash
pm2 start npm --name "your-app-frontend" -- start
pm2 save
pm2 startup
```

## 6. Настройка Nginx и SSL

### Конфигурация Nginx

Создайте файл `/etc/nginx/sites-available/your-app`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /api {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Активируйте конфигурацию:

```bash
sudo ln -s /etc/nginx/sites-available/your-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Настройка SSL с Certbot

```bash
sudo apt-get install -y certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 7. Проверка после деплоя

После деплоя приложения, проведите следующие проверки:

1. Доступность фронтенда по URL: https://your-domain.com
2. Доступность API: https://your-domain.com/api/health
3. Проверка логов:
   ```bash
   sudo journalctl -u your-app-backend -f
   pm2 logs your-app-frontend
   ```
4. Мониторинг ресурсов:
   ```bash
   htop
   ```

## 8. Устранение распространенных проблем

### Приложение не запускается
- Проверьте логи для получения информации об ошибках
- Убедитесь, что переменные окружения настроены правильно
- Проверьте права доступа к файлам и директориям

### Проблемы с CORS
- Убедитесь, что в бэкенде правильно настроены заголовки CORS
- Проверьте, что домены в CORS_ORIGINS совпадают с фактическим доменом приложения

### Проблемы с SSL
- Проверьте срок действия сертификатов
- Обновите сертификаты при необходимости:
  ```bash
  sudo certbot renew
  ```
```

### 3.2. Документирование процедур восстановления

**Структура руководства по восстановлению (recovery-guide.md):**

```markdown
# Руководство по восстановлению приложения

## Оглавление
1. Восстановление из резервных копий
2. Восстановление после сбоя сервера
3. Восстановление после нарушения безопасности
4. Повторное развертывание приложения
5. Откат к предыдущей версии
6. Аварийные контакты и процедуры эскалации

## 1. Восстановление из резервных копий

### 1.1. Восстановление базы данных

#### Для PostgreSQL
```bash
# Распаковка бэкапа
gunzip /path/to/backups/db/database-name-YYYY-MM-DD.sql.gz

# Восстановление базы данных
psql -U username -d database_name -f /path/to/backups/db/database-name-YYYY-MM-DD.sql
```

#### Для MongoDB
```bash
# Распаковка бэкапа
tar -xzf /path/to/backups/mongo/database-name-YYYY-MM-DD.tar.gz -C /tmp

# Восстановление базы данных
mongorestore --uri="*******************************************" --db=database_name /tmp/database-name-YYYY-MM-DD
```

### 1.2. Восстановление конфигурационных файлов

```bash
# Распаковка бэкапа конфигурационных файлов
mkdir -p /tmp/config-restore
tar -xzf /path/to/backups/configs/configs-YYYY-MM-DD.tar.gz -C /tmp/config-restore

# Восстановление конфигурационных файлов
cp -r /tmp/config-restore/* /path/to/application/
```

### 1.3. Восстановление пользовательских данных

```bash
# Распаковка бэкапа пользовательских данных
tar -xzf /path/to/backups/user_data/user_data-YYYY-MM-DD.tar.gz -C /tmp

# Восстановление пользовательских данных
cp -r /tmp/user_data /path/to/application/
```

### 1.4. Восстановление полного проекта

```bash
# Распаковка полного бэкапа
mkdir -p /tmp/full-restore
tar -xzf /path/to/backups/full/full-YYYY-MM-DD.tar.gz -C /tmp/full-restore

# Перемещение восстановленного проекта
mv /path/to/application /path/to/application.old
mv /tmp/full-restore/your-repo /path/to/application

# Установка зависимостей
cd /path/to/application
npm install

# Сборка фронтенда
npm run build

# Сборка бэкенда
cd backend-rust
cargo build --release

# Перезапуск сервисов
sudo systemctl restart your-app-backend
pm2 restart your-app-frontend
```

## 2. Восстановление после сбоя сервера

### 2.1. Проверка состояния сервера

```bash
# Проверка общей информации о системе
uname -a
uptime

# Проверка использования диска
df -h

# Проверка использования памяти
free -h

# Проверка логов системы
sudo journalctl -p err | tail
```

### 2.2. Перезапуск сервисов

```bash
# Перезапуск бэкенда
sudo systemctl restart your-app-backend

# Перезапуск фронтенда
pm2 restart your-app-frontend

# Перезапуск Nginx
sudo systemctl restart nginx
```

### 2.3. Восстановление после критического сбоя

1. Подготовка нового сервера с необходимыми зависимостями (см. руководство по деплою)
2. Восстановление данных из резервных копий (см. раздел 1)
3. Настройка DNS для переключения на новый сервер
4. Проверка функционирования приложения на новом сервере

## 3. Восстановление после нарушения безопасности

### 3.1. Изоляция скомпрометированной системы

```bash
# Отключение от сети (в крайнем случае)
sudo ifconfig eth0 down

# Остановка всех сервисов
sudo systemctl stop your-app-backend
pm2 stop all
sudo systemctl stop nginx
```

### 3.2. Оценка масштаба нарушения

```bash
# Проверка логов авторизации
sudo cat /var/log/auth.log | grep "Failed password"

# Проверка запущенных процессов
ps aux | grep -v -E "(root|USERNAME)"

# Проверка открытых соединений
sudo netstat -tulpn
```

### 3.3. Восстановление системы

1. Создание полной резервной копии текущего состояния для последующего анализа
2. Восстановление из чистого образа системы
3. Применение актуальных обновлений безопасности:
   ```bash
   sudo apt-get update
   sudo apt-get upgrade
   ```
4. Восстановление приложения из проверенной резервной копии
5. Изменение всех учетных данных, паролей и ключей доступа
6. Настройка усиленных политик безопасности

## 4. Повторное развертывание приложения

Если требуется полное переразвертывание приложения:

1. Остановите текущие экземпляры:
   ```bash
   sudo systemctl stop your-app-backend
   pm2 stop your-app-frontend
   ```

2. Удалите старые файлы:
   ```bash
   mv /path/to/application /path/to/application.old
   ```

3. Клонируйте репозиторий и настройте приложение заново:
   ```bash
   git clone https://github.com/your-organization/your-repo.git /path/to/application
   cd /path/to/application
   ```

4. Следуйте стандартной процедуре деплоя (см. руководство по деплою)

5. Внесите необходимые настройки и восстановите данные из резервных копий

## 5. Откат к предыдущей версии

### 5.1. Откат фронтенда

```bash
# Переход в директорию проекта
cd /path/to/application

# Переключение на предыдущую версию в Git
git checkout v1.x.x  # или конкретный тег/коммит

# Установка зависимостей и сборка
npm install
npm run build

# Перезапуск приложения
pm2 restart your-app-frontend
```

### 5.2. Откат бэкенда

```bash
# Переход в директорию бэкенда
cd /path/to/application/backend-rust

# Переключение на предыдущую версию в Git
git checkout v1.x.x  # или конкретный тег/коммит

# Сборка
cargo build --release

# Перезапуск сервиса
sudo systemctl restart your-app-backend
```

### 5.3. Восстановление базы данных к предыдущему состоянию

Следуйте инструкциям в разделе 1.1 для восстановления базы данных из резервной копии, сделанной перед обновлением.

## 6. Аварийные контакты и процедуры эскалации

### 6.1. Контактная информация ответственных лиц

| Роль | Имя | Контакт | Время доступности |
|------|-----|---------|-------------------|
| Главный разработчик | Имя Фамилия | <EMAIL>, +1234567890 | 9:00-18:00 GMT+3 |
| DevOps-инженер | Имя Фамилия | <EMAIL>, +1234567890 | 8:00-20:00 GMT+3 |
| Руководитель проекта | Имя Фамилия | <EMAIL>, +1234567890 | 9:00-19:00 GMT+3 |

### 6.2. Процедура эскалации

1. **Уровень 1**: Попытка решения проблемы DevOps-инженером (время реакции: 30 минут, время решения: 2 часа)
2. **Уровень 2**: Вовлечение главного разработчика (время реакции: 1 час, время решения: 4 часа)
3. **Уровень 3**: Эскалация до руководителя проекта (время реакции: 2 часа, время решения: 8 часов)
4. **Критический инцидент**: Немедленное уведомление всей команды, формирование кризисной группы

### 6.3. Шаблон отчета об инциденте

```
## Отчет об инциденте

Дата и время инцидента: [ДАТА] [ВРЕМЯ]
Обнаружено: [КЕМ]
Затронутые системы: [СИСТЕМЫ]
Описание проблемы: [ОПИСАНИЕ]

### Хронология
- [ВРЕМЯ]: [СОБЫТИЕ]
- [ВРЕМЯ]: [СОБЫТИЕ]

### Предпринятые действия
1. [ДЕЙСТВИЕ]
2. [ДЕЙСТВИЕ]

### Первопричина
[ОБЪЯСНЕНИЕ ПРИЧИНЫ]

### Предлагаемые меры предотвращения
1. [МЕРА]
2. [МЕРА]

### Извлеченные уроки
[УРОКИ]
```
```

### 3.3. Создание чек-листов для обновления приложения

**Чек-лист для планового обновления приложения (update-checklist.md):**

```markdown
# Чек-лист для обновления приложения

## Подготовка к обновлению

- [ ] Проверьте наличие последней версии документации
- [ ] Уведомите пользователей о предстоящем обновлении и возможных простоях
- [ ] Создайте ветку для обновления в репозитории
- [ ] Убедитесь, что все текущие изменения закоммичены
- [ ] Создайте полную резервную копию приложения и базы данных
- [ ] Убедитесь, что у вас есть все необходимые учетные данные и доступы

## Подготовка среды разработки

- [ ] Выполните слияние изменений из основной ветки в ветку обновления
- [ ] Установите новые зависимости (при необходимости)
- [ ] Обновите конфигурационные файлы (при необходимости)
- [ ] Выполните миграции базы данных на тестовой среде (при необходимости)
- [ ] Запустите все тесты для проверки совместимости

## Обновление тестовой среды

- [ ] Разверните обновление на тестовом сервере
- [ ] Проверьте работоспособность основных функций
- [ ] Проверьте производительность приложения
- [ ] Выполните тесты безопасности (при необходимости)
- [ ] Устраните выявленные проблемы
- [ ] Обновите документацию (при необходимости)

## Обновление production-среды

- [ ] Включите режим обслуживания или уведомление о проведении работ
- [ ] Создайте финальную резервную копию перед обновлением
- [ ] Обновите бэкенд:
  - [ ] Остановите бэкенд-сервис: `sudo systemctl stop your-app-backend`
  - [ ] Обновите код: `git pull origin main`
  - [ ] Соберите приложение: `cargo build --release`
  - [ ] Запустите бэкенд-сервис: `sudo systemctl start your-app-backend`
  - [ ] Проверьте статус: `sudo systemctl status your-app-backend`
- [ ] Обновите фронтенд:
  - [ ] Остановите фронтенд: `pm2 stop your-app-frontend`
  - [ ] Обновите код: `git pull origin main`
  - [ ] Установите зависимости: `npm install`
  - [ ] Соберите приложение: `npm run build`
  - [ ] Запустите фронтенд: `pm2 start your-app-frontend`
  - [ ] Проверьте статус: `pm2 status`
- [ ] Выполните миграции базы данных (при необходимости)
- [ ] Обновите конфигурации Nginx (при необходимости)
- [ ] Перезагрузите Nginx: `sudo systemctl reload nginx`
- [ ] Отключите режим обслуживания

## Проверка после обновления

- [ ] Проверьте доступность приложения
- [ ] Выполните проверку всех критичных бизнес-процессов
- [ ] Проверьте производительность после обновления
- [ ] Проверьте логи на наличие ошибок:
  - [ ] Логи бэкенда: `sudo journalctl -u your-app-backend -f`
  - [ ] Логи фронтенда: `pm2 logs your-app-frontend`
  - [ ] Логи Nginx: `sudo tail -f /var/log/nginx/error.log`

## Действия при обнаружении проблем

- [ ] Соберите информацию о проблеме (логи, скриншоты, данные мониторинга)
- [ ] Оцените критичность проблемы
- [ ] При критических проблемах выполните откат (см. руководство по восстановлению)
- [ ] При некритических проблемах создайте задачу в трекере и назначьте ответственного
- [ ] Обновите документацию с учетом выявленных проблем

## Коммуникация

- [ ] Уведомите команду о завершении обновления
- [ ] Уведомите пользователей о завершении работ и новых функциях
- [ ] Обновите информацию о версии в документации
- [ ] Создайте отчет о проведенном обновлении

## Финальные шаги

- [ ] Создайте тег для новой версии в репозитории
- [ ] Обновите changelog
- [ ] Проверьте и обновите мониторинг и оповещения
- [ ] Запланируйте следующее обновление (при необходимости)
```

**Чек-лист для экстренного обновления безопасности (security-update-checklist.md):**

```markdown
# Чек-лист для экстренного обновления безопасности

## Оценка уязвимости

- [ ] Определите источник информации об уязвимости
- [ ] Оцените критичность уязвимости (критическая, высокая, средняя, низкая)
- [ ] Проверьте, затрагивает ли уязвимость ваше приложение
- [ ] Определите компоненты, которые нуждаются в обновлении
- [ ] Оцените риски, связанные с уязвимостью и обновлением

## Подготовка к обновлению

- [ ] Создайте отдельную ветку для патча безопасности
- [ ] Загрузите патч или обновление компонента
- [ ] Примените патч к исходному коду
- [ ] Протестируйте исправление в изолированной среде
- [ ] Подготовьте скрипты отката на случай проблем
- [ ] Создайте полную резервную копию приложения и данных

## Развертывание обновления

- [ ] Создайте минимальное уведомление о техническом обслуживании (без раскрытия деталей уязвимости)
- [ ] Обновите код в репозитории
- [ ] Разверните обновление с минимальным временем простоя
  - [ ] Для бэкенда:
    ```bash
    sudo systemctl stop your-app-backend
    git pull origin security-patch
    cargo build --release
    sudo systemctl start your-app-backend
    ```
  - [ ] Для фронтенда:
    ```bash
    pm2 stop your-app-frontend
    git pull origin security-patch
    npm install
    npm run build
    pm2 start your-app-frontend
    ```
- [ ] Проверьте работоспособность приложения
- [ ] Проверьте, устранена ли уязвимость

## Проверка после обновления

- [ ] Мониторинг логов на наличие ошибок
- [ ] Проверка основной функциональности
- [ ] Проверка производительности
- [ ] Убедитесь, что патч безопасности эффективен

## Действия при возникновении проблем

- [ ] Оцените, перевешивает ли риск уязвимости проблемы обновления
- [ ] При необходимости выполните откат к предыдущей версии
- [ ] Изолируйте проблему и подготовьте новый патч
- [ ] Повторно протестируйте и разверните исправление

## Коммуникация и отчетность

- [ ] Уведомите команду о выполненном обновлении безопасности
- [ ] Подготовьте отчет об инциденте (не раскрывая технические детали уязвимости)
- [ ] Обновите внутреннюю документацию по безопасности
- [ ] Уведомите пользователей о выполненном обновлении безопасности (в общих чертах)

## Последующие действия

- [ ] Актуализируйте зависимости
- [ ] Обновите методы сканирования для обнаружения подобных уязвимостей
- [ ] Рассмотрите возможность автоматизации процесса применения патчей безопасности
- [ ] Обновите план реагирования на инциденты, если необходимо
```

## 4. Интеграция с системами CI/CD

### 4.1. Автоматизация бэкапов в CI/CD пайплайне

**GitHub Actions Workflow для автоматического бэкапа при деплое (.github/workflows/backup-before-deploy.yml):**

```yaml
name: Backup Before Deploy

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  backup:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
    
    - name: Create pre-deploy backup
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "bash -s" << 'EOF'
          # Настройка переменных
          APP_DIR="/path/to/application"
          BACKUP_DIR="/path/to/backups/deploy"
          DATE=$(date +"%Y-%m-%d_%H-%M-%S")
          BACKUP_FILE="$BACKUP_DIR/pre-deploy-$DATE.tar.gz"
          
          # Создание директории для резервных копий, если она не существует
          mkdir -p $BACKUP_DIR
          
          # Создание архива перед деплоем
          tar -czf $BACKUP_FILE --exclude="node_modules" --exclude=".next" --exclude="target" -C $(dirname $APP_DIR) $(basename $APP_DIR)
          
          # Логирование результата
          echo "Pre-deploy backup completed: $BACKUP_FILE" >> $BACKUP_DIR/backup.log
          
          # Удаление старых резервных копий (хранить только последние 5)
          ls -t $BACKUP_DIR/pre-deploy-*.tar.gz | tail -n +6 | xargs -r rm
        EOF

  deploy:
    needs: backup
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    # Дальнейшие шаги по деплою приложения
```

### 4.2. Интеграция проверок безопасности в CI/CD

**GitHub Actions Workflow для проверки безопасности (.github/workflows/security-checks.yml):**

```yaml
name: Security Checks

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 0 * * 1' # Запуск каждый понедельник

jobs:
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Set up Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: Install cargo-audit
      uses: actions-rs/cargo@v1
      with:
        command: install
        args: cargo-audit
    
    - name: Check NPM dependencies for vulnerabilities
      run: |
        npm audit --json > npm_audit_report.json || true
        echo "NPM Audit Completed"
    
    - name: Check Rust dependencies for vulnerabilities
      working-directory: ./backend-rust
      run: |
        cargo audit > cargo_audit_report.txt || true
        echo "Cargo Audit Completed"
    
    - name: Run ESLint security plugin
      run: |
        npm install eslint eslint-plugin-security
        npx eslint --plugin security --ext .js,.jsx src/ -f json > eslint_security_report.json || true
    
    - name: Scan for secrets
      uses: gitleaks/gitleaks-action@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Upload security reports as artifacts
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          npm_audit_report.json
          cargo_audit_report.txt
          eslint_security_report.json
```

## 5. Шаблоны мониторинга и отчетности

### 5.1. Шаблон ежедневного отчета о состоянии системы

```markdown
# Ежедневный отчет о состоянии системы - {{ date }}

## Основные показатели
- Время бесперебойной работы: {{ uptime }}
- Использование CPU: {{ cpu_usage }}%
- Использование памяти: {{ memory_usage }}%
- Использование диска: {{ disk_usage }}%
- Количество активных пользователей: {{ active_users }}

## Выполненные бэкапы
- База данных: {{ db_backup_status }}
- Конфигурационные файлы: {{ config_backup_status }}
- Пользовательские данные: {{ user_data_backup_status }}

## Проблемы и предупреждения
{{ issues_and_warnings }}

## Рекомендации
{{ recommendations }}

## Предстоящие задачи обслуживания
{{ upcoming_maintenance_tasks }}
```

### 5.2. Шаблон для мониторинга в Prometheus (prometheus.yml)

```yaml
# Конфигурация Prometheus для мониторинга вашего приложения

global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
  - static_configs:
    - targets: ['localhost:9093']

rule_files:
  - "alerts.yml"

scrape_configs:
  - job_name: 'node'
    static_configs:
    - targets: ['localhost:9100']
    
  - job_name: 'backend'
    metrics_path: '/metrics'
    static_configs:
    - targets: ['localhost:8080']
    
  - job_name: 'frontend'
    metrics_path: '/api/metrics'
    static_configs:
    - targets: ['localhost:3000']
```

### 5.3. Правила оповещений для Prometheus (alerts.yml)

```yaml
groups:
- name: Alerts
  rules:
  - alert: HighCPUUsage
    expr: avg(rate(node_cpu_seconds_total{mode!="idle"}[5m])) by (instance) > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

  - alert: HighMemoryUsage
    expr: node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Less than 10% memory available on {{ $labels.instance }}"

  - alert: HighDiskUsage
    expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High disk usage detected"
      description: "Less than 10% disk space available on {{ $labels.instance }}"

  - alert: InstanceDown
    expr: up == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Instance {{ $labels.instance }} down"
      description: "{{ $labels.instance }} has been down for more than 5 minutes"

  - alert: BackendHighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate on backend"
      description: "Error rate is higher than 5% on {{ $labels.instance }}"
```

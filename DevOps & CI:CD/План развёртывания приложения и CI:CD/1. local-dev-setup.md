# Подробный план подготовки локального окружения (Development на Mac OS)

## 1. Установка базовых инструментов разработки

### 1.1 Установка Homebrew
```bash
# Проверьте, установлен ли Homebrew
brew --version

# Если не установлен, выполните установку
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Добавьте Homebrew в PATH, если это требуется (следуйте инструкциям в выводе команды установки)
```

### 1.2 Установка Git и базовых утилит
```bash
# Установка Git, если он еще не установлен
brew install git

# Установка дополнительных утилит
brew install wget curl jq
```

### 1.3 Настройка Git
```bash
# Настройка имени и email для Git
git config --global user.name "Ваше Имя"
git config --global user.email "ваш.email@пример.com"

# Настройка редактора по умолчанию (опционально)
git config --global core.editor "code --wait"  # для VS Code
```

## 2. Установка Node.js и npm для Next.js

### 2.1 Установка Node Version Manager (nvm)
```bash
# Установка nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash

# Добавление nvm в текущую сессию
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# Проверка установки
nvm --version
```

### 2.2 Установка Node.js и npm
```bash
# Просмотр доступных версий Node.js
nvm ls-remote --lts

# Установка последней LTS версии Node.js (рекомендуется для Next.js)
nvm install --lts

# Проверка установки
node --version
npm --version

# Установка глобальных npm пакетов
npm install -g npm@latest
npm install -g yarn
```

### 2.3 Настройка npm
```bash
# Создание или обновление .npmrc с полезными настройками
cat > ~/.npmrc << EOF
save-exact=true
fund=false
audit=false
EOF
```

## 3. Установка Rust и Cargo для бэкенда

### 3.1 Установка Rust через rustup
```bash
# Установка Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Добавление Rust в текущую сессию
source "$HOME/.cargo/env"

# Проверка установки
rustc --version
cargo --version
```

### 3.2 Установка дополнительных компонентов Rust
```bash
# Установка rustfmt для форматирования кода
rustup component add rustfmt

# Установка clippy для статического анализа
rustup component add clippy

# Установка инструментов для документирования
rustup component add rust-docs
```

### 3.3 Установка дополнительных инструментов для Rust
```bash
# Установка cargo-watch для автоматической перекомпиляции
cargo install cargo-watch

# Установка инструментов для тестирования API
cargo install cargo-insta  # для snapshot-тестирования
```

## 4. Настройка локальных SSL-сертификатов

### 4.1 Установка mkcert
```bash
# Установка mkcert через Homebrew
brew install mkcert

# Установка локального CA
mkcert -install
```

### 4.2 Создание SSL-сертификатов для локальной разработки
```bash
# Создание директории для сертификатов
mkdir -p ~/.local-certs

# Генерация сертификатов
cd ~/.local-certs
mkcert localhost 127.0.0.1 ::1 dev.quer.us

# Проверка созданных сертификатов
ls -la
# Вы должны увидеть файлы localhost+3.pem (сертификат) и localhost+3-key.pem (ключ)
```

## 5. Настройка локального прокси для HTTPS

### 5.1 Установка local-ssl-proxy
```bash
# Установка через npm
npm install -g local-ssl-proxy

# Или альтернативно, установка caddy через Homebrew
brew install caddy
```

### 5.2 Создание скрипта для запуска локального SSL-прокси
```bash
# Создаем директорию для скриптов
mkdir -p ~/scripts

# Создаем скрипт запуска прокси
cat > ~/scripts/start-ssl-proxy.sh << 'EOF'
#!/bin/bash

# Проверка наличия сертификатов
if [ ! -f ~/.local-certs/localhost+3.pem ] || [ ! -f ~/.local-certs/localhost+3-key.pem ]; then
  echo "SSL certificates not found. Please run mkcert first."
  exit 1
fi

# Запуск прокси для фронтенда
local-ssl-proxy --source 3001 --target 3000 --cert ~/.local-certs/localhost+3.pem --key ~/.local-certs/localhost+3-key.pem &
echo "Frontend SSL proxy started: https://localhost:3001 -> http://localhost:3000"

# Запуск прокси для бэкенда
local-ssl-proxy --source 8081 --target 8080 --cert ~/.local-certs/localhost+3.pem --key ~/.local-certs/localhost+3-key.pem &
echo "Backend SSL proxy started: https://localhost:8081 -> http://localhost:8080"

# Вывод информации о запущенных процессах
echo "Proxies started. Press Ctrl+C to stop."
wait
EOF

# Делаем скрипт исполняемым
chmod +x ~/scripts/start-ssl-proxy.sh
```

## 6. Клонирование и настройка проекта

### 6.1 Клонирование репозитория
```bash
# Создание рабочей директории
mkdir -p ~/projects
cd ~/projects

# Клонирование репозитория
git clone https://github.com/yourusername/quer-calc.git
cd quer-calc
```
или

# Перейдите в директорию проекта, если вы ещё не в ней
cd /Users/<USER>/quer-calc-next

# Инициализируйте Git
git init

# Создайте первый коммит с существующим .gitignore + добавленными правилами для Rust
git add .
git commit -m "Initial commit"

# Создайте develop ветку как основную для начала работы
git branch -M develop

# Создайте репозиторий на GitHub через веб-интерфейс

# Затем свяжите локальный репозиторий с удаленным
git remote add origin https://github.com/yourusername/quer-calc.git
git push -u origin develop

# Создайте main ветку как пустую базовую ветку для production
git checkout --orphan main
git rm -rf .
echo "# Quer Calculator" > README.md
git add README.md
git commit -m "Initial main branch setup"
git push -u origin main
git checkout develop

### 6.2 Установка зависимостей для фронтенда
```bash
# Переход в директорию проекта
cd /Users/<USER>/quer-calc-next

# Установка зависимостей
npm install
```

### 6.3 Сборка бэкенда
```bash
# Переход в директорию бэкенда
cd /Users/<USER>/quer-calc-next/backend-rust

# Сборка проекта в debug-режиме
cargo build
```

## 7. Настройка переменных окружения

### 7.1 Создание файлов окружения
```bash
# Переход в корневую директорию проекта
cd /Users/<USER>/quer-calc-next

# Создание .env.local для Next.js
cat > .env.local << EOF
# Базовые настройки
NODE_ENV=development
NEXT_PUBLIC_API_URL=https://localhost:8081

# Настройки Clerk
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_WEBHOOK_SECRET=your_webhook_secret

# Настройки Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_monthly_id
NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_annual_id
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# URL сайта для локальной разработки
NEXT_PUBLIC_SITE_URL=https://localhost:3001

# Дополнительные настройки
ALLOW_DIRECT_USER_ID=true
EOF
```

### 7.2 Создание переменных окружения для бэкенда
```bash
# Переход в директорию бэкенда
cd /Users/<USER>/quer-calc-next/backend-rust

# Создание .env файла
cat > .env << EOF
CORS_ORIGINS=http://localhost:3000,https://localhost:3001
RATE_LIMIT_BURST=100
RATE_LIMIT_PER_SEC=50
EOF
```

## 8. Создание скриптов для удобной разработки

### 8.1 Создание скрипта для запуска всего проекта
```bash
# Создание скрипта в корневой директории проекта
cd /Users/<USER>/quer-calc-next

# Добавление скриптов в package.json
cat > dev-scripts.json << EOF
{
  "scripts": {
    "dev:front": "next dev",
    "dev:back": "cd backend-rust && cargo watch -x run",
    "dev:ssl": "~/scripts/start-ssl-proxy.sh",
    "dev": "concurrently \"npm run dev:front\" \"npm run dev:back\" \"npm run dev:ssl\""
  }
}
EOF

# Обновление package.json
npm install --save-dev concurrently
jq -s '.[0] * .[1]' package.json dev-scripts.json > package.json.new
mv package.json.new package.json
rm dev-scripts.json
```

## 9. Настройка инструментов разработки для Next.js

### 9.1 Настройка ESLint
```bash
# Установка ESLint и плагинов
npm install --save-dev eslint eslint-plugin-react eslint-plugin-react-hooks eslint-plugin-next

# Создание конфигурации ESLint
cat > .eslintrc.json << EOF
{
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:next/recommended"
  ],
  "rules": {
    "react/react-in-jsx-scope": "off"
  },
  "env": {
    "browser": true,
    "node": true,
    "es6": true
  }
}
EOF
```

### 9.2 Настройка Prettier
```bash
# Установка Prettier
npm install --save-dev prettier

# Создание конфигурации Prettier
cat > .prettierrc << EOF
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "printWidth": 100
}
EOF
```

## 10. Настройка удобной отладки

### 10.1 Настройка отладки для Next.js
```bash
# Создание конфигурации отладки для VS Code
mkdir -p .vscode
cat > .vscode/launch.json << EOF
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev:front"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "https://localhost:3001"
    },
    {
      "name": "Next.js: debug full stack",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev:front",
      "serverReadyAction": {
        "pattern": "started server on .+, url: (https?://.+)",
        "uriFormat": "https://localhost:3001",
        "action": "debugWithChrome"
      }
    }
  ]
}
EOF
```

### 10.2 Настройка отладки для Rust
```bash
# Добавление конфигурации отладки для Rust в VS Code
cat >> .vscode/launch.json << EOF
,
  {
    "name": "Debug Rust Backend",
    "type": "lldb",
    "request": "launch",
    "cargo": {
      "args": ["build", "--bin", "quer-calc"],
      "filter": {
        "name": "quer-calc",
        "kind": "bin"
      }
    },
    "args": [],
    "cwd": "${workspaceFolder}/backend-rust"
  }
EOF
```

## 11. Проверка установки и тестовый запуск

### 11.1 Проверка установленных инструментов
```bash
# Проверка Node.js и npm
node --version
npm --version

# Проверка Rust и Cargo
rustc --version
cargo --version

# Проверка SSL-сертификатов
ls -la ~/.local-certs
```

### 11.2 Запуск проекта
```bash
# Переход в директорию проекта
cd /Users/<USER>/quer-calc-next

# Запуск проекта
npm run dev

# В браузере можно будет открыть:
# - Фронтенд: https://localhost:3001
# - Бэкенд API: https://localhost:8081
```

## 12. Дополнительные инструменты и настройки (опционально)

### 12.1 Настройка Postman для тестирования API
```bash
# Установка Postman через Homebrew
brew install --cask postman

# Импорт сертификатов в Postman:
# 1. Откройте Postman
# 2. Перейдите в Settings > Certificates
# 3. Выключите SSL certificate verification
# 4. Или добавьте CA сертификат из ~/.local-certs
```

### 12.2 Настройка MongoDB (если используется)
```bash
# Установка MongoDB через Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Запуск MongoDB
brew services start mongodb-community

# Установка MongoDB Compass (GUI)
brew install --cask mongodb-compass
```

### 12.3 Установка редактора (если еще не установлен)
```bash
# Установка VS Code
brew install --cask visual-studio-code

# Установка полезных расширений для проекта
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
code --install-extension bradlc.vscode-tailwindcss
code --install-extension rust-lang.rust-analyzer
code --install-extension svelte.svelte-vscode
```

### 12.4 Настройка Docker (опционально, для совместимости с CI)
```bash
# Установка Docker Desktop
brew install --cask docker

# Запуск Docker
open -a Docker
```

## Заключение

После выполнения всех шагов у вас будет полностью настроенное локальное окружение для разработки проекта quer-calc, включающее:

- Node.js и npm для разработки фронтенда на Next.js
- Rust и Cargo для разработки бэкенда
- Локальные SSL-сертификаты для безопасной разработки
- Правильно настроенные переменные окружения
- Удобные скрипты для запуска и отладки
- Инструменты для повышения качества кода (ESLint, Prettier)

Теперь вы можете начать разработку, запустив команду `npm run dev` в корневой директории проекта. Фронтенд будет доступен по адресу https://localhost:3001, а бэкенд API — по адресу https://localhost:8081.

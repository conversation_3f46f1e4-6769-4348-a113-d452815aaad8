# План настройки мониторинга и логирования

## 1. Настройка логирования приложения

### 1.1 Направление логов Next.js в определенные файлы

**Шаг 1: Установка необходимых зависимостей**
```bash
cd /path/to/frontend
npm install --save winston pino pino-pretty rotating-file-stream
```

**Шаг 2: Создание модуля логирования**
Создайте файл `src/utils/logger.js`:

```javascript
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Создаем директорию для логов, если она не существует
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Настройка форматирования
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.metadata({ fillExcept: ['message', 'level', 'timestamp'] }),
  winston.format.printf(({ timestamp, level, message, metadata }) => {
    return `[${timestamp}] ${level.toUpperCase()}: ${message} ${Object.keys(metadata).length ? JSON.stringify(metadata) : ''}`;
  })
);

// Создание логгера
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: logFormat,
  transports: [
    // Логи для консоли
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        logFormat
      )
    }),
    // Логи ошибок в отдельный файл
    new winston.transports.File({ 
      filename: path.join(logDir, 'error.log'), 
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5
    }),
    // Общие логи
    new winston.transports.File({ 
      filename: path.join(logDir, 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 5
    }),
  ],
  // Обрабатывать исключения и отказы
  exceptionHandlers: [
    new winston.transports.File({ filename: path.join(logDir, 'exceptions.log') })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: path.join(logDir, 'rejections.log') })
  ]
});

// Экспорт логгера
module.exports = logger;
```

**Шаг 3: Интеграция логгера с API роутами Next.js**
Создайте middleware для логирования в `src/middleware.js`:

```javascript
import logger from './utils/logger';
import { NextResponse } from 'next/server';

export function middleware(request) {
  const requestStartTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 10);
  
  // Логирование входящего запроса
  logger.info(`${request.method} ${request.nextUrl.pathname}`, {
    requestId,
    url: request.nextUrl.toString(),
    method: request.method,
    ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  });

  // Обогащение запроса и обработка ответа
  const response = NextResponse.next();
  
  // Добавление заголовка для отслеживания запроса
  response.headers.set('X-Request-Id', requestId);

  // Логирование после обработки
  response.on('finish', () => {
    const duration = Date.now() - requestStartTime;
    logger.info(`${request.method} ${request.nextUrl.pathname} completed`, {
      requestId,
      duration,
      statusCode: response.statusCode
    });
  });

  return response;
}

export const config = {
  matcher: ['/api/:path*']
};
```

**Шаг 4: Логирование глобальных ошибок**
Добавьте в `src/pages/_app.js` или `src/app/layout.jsx`:

```javascript
import logger from '../utils/logger';
import { useEffect } from 'react';

export default function App({ Component, pageProps }) {
  useEffect(() => {
    // Глобальные обработчики ошибок
    const handleError = (error, info) => {
      logger.error('Unhandled client error', { error: error.toString(), info });
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleError);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleError);
    };
  }, []);

  return <Component {...pageProps} />;
}
```

### 1.2 Настройка структурированного логирования для Rust бэкенда

**Шаг 1: Добавление зависимостей**
Добавьте в `Cargo.toml`:

```toml
[dependencies]
# Существующие зависимости...
env_logger = "0.10.0"
log = "0.4"
serde_json = "1.0"
chrono = "0.4"
```

**Шаг 2: Создание структурированного логгера**
Создайте файл `src/logging.rs`:

```rust
use actix_web::middleware::Logger;
use env_logger::Env;
use log::{LevelFilter, info, error};
use std::fs::{File, OpenOptions};
use std::io::Write;
use chrono::Local;
use std::path::Path;

pub fn init_logger(log_dir: &str) -> std::io::Result<()> {
    // Создаем директорию для логов, если она не существует
    if !Path::new(log_dir).exists() {
        std::fs::create_dir_all(log_dir)?;
    }

    // Устанавливаем логирование
    env_logger::Builder::from_env(Env::default().default_filter_or("info"))
        .format(|buf, record| {
            let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string();
            let level = record.level();
            let target = record.target();
            let args = record.args();
            
            // Если это сообщение от actix-web, логируем только info и ниже
            if target.starts_with("actix_web") && level > LevelFilter::Info {
                return Ok(());
            }
            
            writeln!(
                buf,
                "{{\"timestamp\":\"{}\",\"level\":\"{}\",\"target\":\"{}\",\"message\":\"{}\"}}",
                timestamp, level, target, args
            )
        })
        .init();

    // Логирование в файл
    let error_log_file = format!("{}/error.log", log_dir);
    let info_log_file = format!("{}/info.log", log_dir);
    
    // Настройка файла для ошибок
    let error_file = OpenOptions::new()
        .create(true)
        .write(true)
        .append(true)
        .open(error_log_file)?;
        
    // Настройка файла для info-логов
    let info_file = OpenOptions::new()
        .create(true)
        .write(true)
        .append(true)
        .open(info_log_file)?;
    
    // Логирование инициализации
    info!("Logger initialized");
    
    Ok(())
}

// Создаем кастомный FormatterFn для actix-web Logger
pub fn get_logger() -> Logger {
    Logger::new("%a \"%r\" %s %b \"%{Referer}i\" \"%{User-Agent}i\" %T")
}

// Перехват паники и их логирование
pub fn setup_panic_handler() {
    std::panic::set_hook(Box::new(|panic_info| {
        let backtrace = std::backtrace::Backtrace::capture();
        let timestamp = Local::now().format("%Y-%m-%d %H:%M:%S").to_string();
        let panic_message = match panic_info.payload().downcast_ref::<&str>() {
            Some(s) => *s,
            None => "Unknown panic",
        };
        
        let location = panic_info.location()
            .map(|l| format!("{}:{}", l.file(), l.line()))
            .unwrap_or_else(|| "unknown".to_string());
            
        error!("PANIC at {}: {} - {}\n{:?}", location, timestamp, panic_message, backtrace);
    }));
}
```

**Шаг 3: Интеграция логгера в основное приложение**
В файле `src/main.rs`:

```rust
mod logging;

use actix_web::{App, HttpServer};
use std::io;

#[actix_web::main]
async fn main() -> io::Result<()> {
    // Инициализация логгера
    logging::init_logger("./logs")?;
    logging::setup_panic_handler();
    
    println!("Backend server starting up...");
    
    // Остальной код main функции
    HttpServer::new(move || {
        App::new()
            .wrap(logging::get_logger())
            // Остальные middleware и сервисы
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}
```

### 1.3 Настройка ротации логов

**Шаг 1: Настройка logrotate для системы**
Создайте файл `/etc/logrotate.d/quer-calc`:

```
/path/to/project/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
    sharedscripts
    postrotate
        systemctl reload quer-calc-next >/dev/null 2>&1 || true
        systemctl reload quer-calc-backend >/dev/null 2>&1 || true
    endscript
}

/path/to/project/backend-rust/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
    sharedscripts
    postrotate
        systemctl reload quer-calc-backend >/dev/null 2>&1 || true
    endscript
}
```

**Шаг 2: Проверка конфигурации logrotate**
```bash
sudo logrotate -d /etc/logrotate.d/quer-calc
```

**Шаг 3: Принудительный запуск ротации (для тестирования)**
```bash
sudo logrotate -f /etc/logrotate.d/quer-calc
```

**Шаг 4: Настройка скрипта для мониторинга размера логов**
Создайте файл `/usr/local/bin/check-logs-size.sh`:

```bash
#!/bin/bash

LOGS_DIR="/path/to/project/logs"
BACKEND_LOGS_DIR="/path/to/project/backend-rust/logs"
MAX_SIZE_MB=500

# Функция для проверки размера директории
check_dir_size() {
  local dir=$1
  local size=$(du -sm "$dir" | cut -f1)
  
  if [ "$size" -gt "$MAX_SIZE_MB" ]; then
    echo "WARNING: Log directory $dir is larger than ${MAX_SIZE_MB}MB ($size MB)"
    
    # Дополнительно можно добавить отправку уведомления
    # telegram-send "WARNING: Log directory $dir is larger than ${MAX_SIZE_MB}MB ($size MB)"
    
    # Запуск принудительной ротации
    logrotate -f /etc/logrotate.d/quer-calc
  fi
}

check_dir_size "$LOGS_DIR"
check_dir_size "$BACKEND_LOGS_DIR"
```

**Шаг 5: Назначение прав и добавление в cron**
```bash
sudo chmod +x /usr/local/bin/check-logs-size.sh
sudo crontab -e
```

Добавьте строку:
```
0 */4 * * * /usr/local/bin/check-logs-size.sh
```

## 2. Настройка мониторинга

### 2.1 Установка простого мониторинга (Netdata)

**Шаг 1: Установка Netdata**
```bash
bash <(curl -Ss https://get.netdata.cloud/kickstart.sh)
```

**Шаг 2: Проверка установки**
```bash
systemctl status netdata
```

**Шаг 3: Настройка доступа к панели Netdata**
Откройте файл `/etc/netdata/netdata.conf`:

```ini
[web]
    allow connections from = localhost 192.168.0.* 10.0.0.*
    allow dashboard from = localhost 192.168.0.* 10.0.0.*
```

Чтобы защитить доступ к Netdata, установите nginx как прокси с базовой аутентификацией:

```bash
sudo apt-get install apache2-utils
sudo htpasswd -c /etc/nginx/.htpasswd admin
```

Настройте nginx:

```nginx
server {
    listen 80;
    server_name monitor.yourdomain.com;
    
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name monitor.yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/monitor.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/monitor.yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    
    auth_basic "Netdata Access";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    location / {
        proxy_pass http://localhost:19999;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**Шаг 4: Настройка мониторинга приложения**
Добавьте в `/etc/netdata/python.d/web_log.conf` для мониторинга логов nginx:

```yaml
nginx_log:
  name: 'nginx'
  path: '/var/log/nginx/access.log'
```

**Шаг 5: Рестарт Netdata**
```bash
sudo systemctl restart netdata
```

### 2.2 Настройка оповещений через Telegram

**Шаг 1: Создание Telegram бота через BotFather**
1. Откройте Telegram, найдите @BotFather
2. Отправьте команду `/newbot`
3. Следуйте инструкциям и получите API токен
4. Создайте группу и добавьте бота в нее
5. Узнайте ID группы с помощью бота @RawDataBot

**Шаг 2: Настройка скрипта для отправки уведомлений**
Создайте файл `/usr/local/bin/telegram-notify.sh`:

```bash
#!/bin/bash

TELEGRAM_BOT_TOKEN="YOUR_BOT_TOKEN"
TELEGRAM_CHAT_ID="YOUR_CHAT_ID"

send_message() {
    local message="$1"
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
         -d chat_id="${TELEGRAM_CHAT_ID}" \
         -d text="${message}" \
         -d parse_mode="HTML"
}

# Если скрипт вызван напрямую
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Проверяем, передано ли сообщение как аргумент
    if [[ $# -eq 0 ]]; then
        echo "Usage: $0 'Your message'"
        exit 1
    fi
    
    # Отправляем сообщение
    send_message "$1"
fi
```

**Шаг 3: Сделать скрипт исполняемым**
```bash
sudo chmod +x /usr/local/bin/telegram-notify.sh
```

**Шаг 4: Тестирование оповещений**
```bash
/usr/local/bin/telegram-notify.sh "Тестовое сообщение системы мониторинга"
```

**Шаг 5: Интеграция с Netdata**
Создайте файл `/etc/netdata/health_alarm_notify.conf`:

```
# Включаем уведомления для Telegram
SEND_TELEGRAM="YES"

# Токен бота
TELEGRAM_BOT_TOKEN="YOUR_BOT_TOKEN"

# ID чата
DEFAULT_RECIPIENT_TELEGRAM="YOUR_CHAT_ID"

# Формат сообщений
TELEGRAM_MESSAGE_PLAINTEXT="${status} ${host} - ${chart} - ${info}"
```

### 2.3 Мониторинг использования ресурсов и производительности

**Шаг 1: Настройка аппаратного мониторинга в Netdata**
Создайте файл `/etc/netdata/health.d/cpu_usage.conf`:

```
template: cpu_usage
      on: system.cpu
    calc: $user + $system
   every: 1m
    warn: $this > (($status >= $WARNING) ? (75) : (85))
    crit: $this > (($status >= $CRITICAL) ? (85) : (95))
   units: %
    info: CPU utilization
      to: sysadmin
```

Создайте файл `/etc/netdata/health.d/ram_usage.conf`:

```
template: ram_usage
      on: system.ram
    calc: $used * 100 / ($used + $free)
   every: 1m
    warn: $this > (($status >= $WARNING) ? (80) : (90))
    crit: $this > (($status >= $CRITICAL) ? (90) : (95))
   units: %
    info: RAM utilization
      to: sysadmin
```

Создайте файл `/etc/netdata/health.d/disk_usage.conf`:

```
template: disk_usage
      on: disk.space
    calc: $used * 100 / ($used + $avail)
   every: 1m
    warn: $this > (($status >= $WARNING) ? (80) : (90))
    crit: $this > (($status >= $CRITICAL) ? (90) : (95))
   units: %
    info: Disk space utilization
      to: sysadmin
```

**Шаг 2: Мониторинг открытых файлов и сетевых соединений**
Добавьте в crontab:

```
*/5 * * * * /usr/bin/lsof | wc -l | awk '{print "Open files: " $1}' | /usr/local/bin/telegram-notify.sh
*/10 * * * * netstat -an | grep -c ESTABLISHED | awk '{print "Active connections: " $1}' > /var/log/connections.log
```

**Шаг 3: Мониторинг журналов приложения**
Создайте файл `/usr/local/bin/check-app-errors.sh`:

```bash
#!/bin/bash

ERROR_THRESHOLD=10
LOG_FILE="/path/to/project/logs/error.log"
RUST_LOG_FILE="/path/to/project/backend-rust/logs/error.log"

# Подсчет ошибок за последний час в Next.js приложении
next_errors=$(grep -c "ERROR" $LOG_FILE | tail -n 1)

# Подсчет ошибок за последний час в Rust бэкенде
rust_errors=$(grep -c "ERROR" $RUST_LOG_FILE | tail -n 1)

# Проверка превышения порога
if [ "$next_errors" -gt "$ERROR_THRESHOLD" ] || [ "$rust_errors" -gt "$ERROR_THRESHOLD" ]; then
    message="ВНИМАНИЕ: Высокий уровень ошибок в приложении!
- Next.js: $next_errors ошибок
- Rust: $rust_errors ошибок
Проверьте логи для дополнительной информации."
    
    /usr/local/bin/telegram-notify.sh "$message"
fi
```

Добавьте скрипт в crontab:
```
*/10 * * * * /usr/local/bin/check-app-errors.sh
```

## 3. Мониторинг доступности

### 3.1 Настройка регулярных проверок доступности

**Шаг 1: Создание эндпоинтов для проверки состояния**
В Next.js приложении создайте файл `src/app/api/health/route.js`:

```javascript
import { NextResponse } from 'next/server';
import logger from '../../../utils/logger';

export async function GET() {
  try {
    // Здесь можно добавить проверки внешних зависимостей
    // например, базы данных или API
    const status = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.APP_VERSION || 'unknown'
    };
    
    return NextResponse.json(status);
  } catch (error) {
    logger.error('Health check failed', { error: error.toString() });
    return NextResponse.json(
      { status: 'error', message: error.message },
      { status: 500 }
    );
  }
}
```

В Rust бэкенде:

```rust
use actix_web::{web, HttpResponse, Responder};
use serde_json::json;
use std::time::{SystemTime, UNIX_EPOCH};
use std::env;

pub async fn health_check() -> impl Responder {
    let start = SystemTime::now();
    let since_epoch = start
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards");
        
    HttpResponse::Ok().json(json!({
        "status": "ok",
        "timestamp": since_epoch.as_secs(),
        "version": env::var("APP_VERSION").unwrap_or_else(|_| "unknown".to_string())
    }))
}
```

Добавьте в `src/main.rs`:

```rust
app.service(
    web::scope("/api")
        .route("/health", web::get().to(health_check))
        // другие маршруты
)
```

**Шаг 2: Настройка скрипта проверки доступности**
Создайте файл `/usr/local/bin/check-availability.sh`:

```bash
#!/bin/bash

# Настройки
FRONTEND_URL="https://yourdomain.com/api/health"
BACKEND_URL="https://yourdomain.com/api/calculate"
NOTIFICATION_SCRIPT="/usr/local/bin/telegram-notify.sh"
LOG_FILE="/var/log/availability-checks.log"
TIMEOUT=10

# Проверка доступности с измерением времени отклика
check_endpoint() {
    local url=$1
    local start_time=$(date +%s.%N)
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url")
    local end_time=$(date +%s.%N)
    local response_time=$(echo "$end_time - $start_time" | bc)
    
    # Логирование
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $url - HTTP: $http_code - Time: ${response_time}s" >> $LOG_FILE
    
    # Проверка статус-кода
    if [ "$http_code" != "200" ]; then
        $NOTIFICATION_SCRIPT "❌ Сервис недоступен: $url возвращает код $http_code"
        return 1
    fi
    
    # Проверка времени отклика
    if (( $(echo "$response_time > 5.0" | bc -l) )); then
        $NOTIFICATION_SCRIPT "⚠️ Медленный отклик: $url - ${response_time}s"
    fi
    
    return 0
}

# Проверка frontend
check_endpoint "$FRONTEND_URL"
frontend_status=$?

# Проверка backend
check_endpoint "$BACKEND_URL"
backend_status=$?

# Если оба сервиса недоступны, отправляем дополнительное уведомление
if [ $frontend_status -ne 0 ] && [ $backend_status -ne 0 ]; then
    $NOTIFICATION_SCRIPT "🔥 КРИТИЧНО: Оба сервиса (frontend и backend) недоступны!"
fi
```

**Шаг 3: Добавление в crontab**
```bash
chmod +x /usr/local/bin/check-availability.sh
crontab -e
```

Добавьте:
```
*/5 * * * * /usr/local/bin/check-availability.sh
```

### 3.2 Мониторинг времени отклика эндпоинтов API

**Шаг 1: Создание скрипта для мониторинга API**
Создайте файл `/usr/local/bin/check-api-performance.sh`:

```bash
#!/bin/bash

# Настройки
API_CALCULATE_URL="https://yourdomain.com/api/calculate"
API_HEALTH_URL="https://yourdomain.com/api/health"
LOG_FILE="/var/log/api-performance.log"
ALERT_THRESHOLD=2.0  # секунд
TIMEOUT=15

# Функция для тестирования API
test_api() {
    local endpoint=$1
    local payload=$2
    local method=${3:-"GET"}
    
    local start_time=$(date +%s.%N)
    
    if [ "$method" == "POST" ]; then
        response=$(curl -s -X POST -H "Content-Type: application/json" -d "$payload" --max-time $TIMEOUT "$endpoint")
        status=$?
    else
        response=$(curl -s --max-time $TIMEOUT "$endpoint")
        status=$?
    fi
    
    local end_time=$(date +%s.%N)
    local response_time=$(echo "$end_time - $start_time" | bc)
    
    # Логирование
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $method $endpoint - Time: ${response_time}s - Status: $status" >> $LOG_FILE
    
    # Если запрос не выполнен успешно
    if [ $status -ne 0 ]; then
        /usr/local/bin/telegram-notify.sh "❌ API недоступен: $endpoint (ошибка curl: $status)"
        return 1
    fi
    
    # Если время ответа превышает порог
    if (( $(echo "$response_time > $ALERT_THRESHOLD" | bc -l) )); then
        /usr/local/bin/telegram-notify.sh "⚠️ Медленный API: $endpoint - ${response_time}s"
    fi
    
    # Возвращаем время ответа для анализа
    echo "$response_time"
}

# Тестируем API health
health_time=$(test_api "$API_HEALTH_URL")

# Тестируем API calculate с тестовыми данными
calculate_payload='{"entryPrice":100,"stopLoss":90,"risk":1,"riskUnit":"percent","accountBalance":10000,"nominalRR":2,"maxLeverage":10,"entryFee":0.1,"exitFee":0.1,"maintenanceMargin":0.5,"safetyBuffer":2}'
calculate_time=$(test_api "$API_CALCULATE_URL" "$calculate_payload" "POST")

# Сохраняем данные для построения графиков
echo "$(date +'%s'),health,$health_time" >> /var/log/api-performance-data.csv
echo "$(date +'%s'),calculate,$calculate_time" >> /var/log/api-performance-data.csv
```

**Шаг 2: Настройка графиков в Netdata**
Создайте модуль для анализа логов API:

```bash
cat > /etc/netdata/python.d/custom_api_logs.conf << EOL
api_performance:
  name: 'API Performance'
  path: '/var/log/api-performance-data.csv'
  update_every: 60
  autodetection_retry: 1
EOL
```

**Шаг 3: Автоматизация через crontab**
```bash
chmod +x /usr/local/bin/check-api-performance.sh
crontab -e
```

Добавьте:
```
*/10 * * * * /usr/local/bin/check-api-performance.sh
```

### 3.3 Мониторинг SSL-сертификатов и их сроков действия

**Шаг 1: Создание скрипта для проверки сертификатов**
Создайте файл `/usr/local/bin/check-ssl-certs.sh`:

```bash
#!/bin/bash

# Настройки
DOMAINS=("yourdomain.com" "api.yourdomain.com" "monitor.yourdomain.com")
DAYS_WARNING=14
LOG_FILE="/var/log/ssl-checks.log"

# Проверка сертификата
check_ssl() {
    local domain=$1
    
    echo "Проверка $domain..." >> $LOG_FILE
    
    # Получение данных сертификата
    cert_info=$(timeout 10 openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -enddate -subject -issuer)
    
    if [ $? -ne 0 ]; then
        echo "Ошибка получения данных сертификата для $domain" >> $LOG_FILE
        /usr/local/bin/telegram-notify.sh "❌ Ошибка проверки SSL для $domain"
        return 1
    fi
    
    # Извлечение даты истечения
    end_date=$(echo "$cert_info" | grep 'notAfter=' | cut -d'=' -f2)
    
    # Преобразование в формат Unix timestamp
    end_timestamp=$(date -d "$end_date" +%s)
    current_timestamp=$(date +%s)
    
    # Расчет оставшихся дней
    days_left=$(( ($end_timestamp - $current_timestamp) / 86400 ))
    
    # Логирование результатов
    echo "Домен: $domain, Срок действия: $end_date, Осталось дней: $days_left" >> $LOG_FILE
    
    # Проверка срока действия
    if [ $days_left -le 0 ]; then
        /usr/local/bin/telegram-notify.sh "🔴 КРИТИЧНО: SSL-сертификат для $domain ИСТЕК!"
        return 2
    elif [ $days_left -le $DAYS_WARNING ]; then
        /usr/local/bin/telegram-notify.sh "🟠 ВНИМАНИЕ: SSL-сертификат для $domain истекает через $days_left дней!"
        return 3
    fi
    
    return 0
}

# Проверка всех доменов
echo "=== Проверка SSL-сертификатов $(date) ===" >> $LOG_FILE

for domain in "${DOMAINS[@]}"; do
    check_ssl $domain
done

echo "Проверка завершена" >> $LOG_FILE
echo "" >> $LOG_FILE
```

**Шаг 2: Настройка автоматического обновления Let's Encrypt**
```bash
sudo crontab -e
```

Добавьте:
```
0 3 * * * /usr/bin/certbot renew --quiet --post-hook "systemctl reload nginx"
```

**Шаг 3: Добавление проверки SSL в crontab**
```bash
chmod +x /usr/local/bin/check-ssl-certs.sh
crontab -e
```

Добавьте:
```
0 9 * * * /usr/local/bin/check-ssl-certs.sh
```

## 4. Интеграция и Тестирование

### 4.1 Тестирование системы логирования

**Шаг 1: Тестирование Frontend логирования**
```bash
# Проверка записи логов
tail -f /path/to/project/logs/combined.log

# Генерация тестовой ошибки
curl -v http://localhost:3000/api/non-existent-endpoint
```

**Шаг 2: Тестирование Backend логирования**
```bash
# Проверка записи логов
tail -f /path/to/project/backend-rust/logs/info.log

# Генерация тестовой ошибки
curl -v -X POST http://localhost:8080/api/calculate -H "Content-Type: application/json" -d '{}'
```

**Шаг 3: Тестирование ротации логов**
```bash
# Ручной запуск logrotate для тестирования
sudo logrotate -f /etc/logrotate.d/quer-calc

# Проверка создания новых файлов логов
ls -la /path/to/project/logs/
```

### 4.2 Тестирование системы мониторинга

**Шаг 1: Тестирование Netdata**
```bash
# Проверка статуса сервиса
systemctl status netdata

# Проверка доступности веб-интерфейса
curl -v http://localhost:19999
```

**Шаг 2: Тестирование оповещений Telegram**
```bash
# Отправка тестового уведомления
/usr/local/bin/telegram-notify.sh "Тестовое сообщение системы мониторинга"

# Проверка настроек алармов Netdata
sudo netdatacli reload-health
sudo netdatacli health
```

**Шаг 3: Тестирование мониторинга доступности**
```bash
# Ручной запуск проверки доступности
/usr/local/bin/check-availability.sh

# Проверка логов
tail -f /var/log/availability-checks.log
```

**Шаг 4: Тестирование мониторинга SSL**
```bash
# Ручной запуск проверки сертификатов
/usr/local/bin/check-ssl-certs.sh

# Проверка логов
tail -f /var/log/ssl-checks.log
```

## 5. Документация и обслуживание

### 5.1 Документация настроек

**Шаг 1: Создание документации мониторинга**
Создайте файл `/path/to/project/docs/monitoring.md`:

```markdown
# Документация по мониторингу и логированию

## Логирование

### Frontend (Next.js)
- **Директория логов:** `/path/to/project/logs/`
- **Основные файлы:**
  - `combined.log` - все логи
  - `error.log` - только ошибки
  - `exceptions.log` - необработанные исключения

### Backend (Rust)
- **Директория логов:** `/path/to/project/backend-rust/logs/`
- **Основные файлы:**
  - `info.log` - информационные сообщения
  - `error.log` - ошибки и критические события

### Ротация логов
- Настроена ежедневная ротация с сохранением 7 последних архивов
- Логи старше 7 дней автоматически удаляются
- Ручной запуск: `sudo logrotate -f /etc/logrotate.d/quer-calc`

## Мониторинг

### Netdata
- **Адрес панели:** `https://monitor.yourdomain.com`
- **Учетные данные:** обратитесь к администратору
- **Настройки алармов:** `/etc/netdata/health.d/`

### Оповещения
- Настроены уведомления через Telegram
- Группа мониторинга: "Quer-Calc Мониторинг"
- Скрипт отправки: `/usr/local/bin/telegram-notify.sh`

### Проверки доступности
- Проверка каждые 5 минут
- Эндпоинты:
  - `https://yourdomain.com/api/health`
  - `https://yourdomain.com/api/calculate`
- Логи: `/var/log/availability-checks.log`

### SSL-мониторинг
- Ежедневная проверка сертификатов
- Уведомление за 14 дней до истечения срока
- Автоматическое обновление с Let's Encrypt
```

### 5.2 Настройка автоматических проверок

**Шаг 1: Скрипт общей проверки системы мониторинга**
Создайте файл `/usr/local/bin/monitoring-healthcheck.sh`:

```bash
#!/bin/bash

# Проверка работы служб логирования и мониторинга
check_service() {
    local service=$1
    if ! systemctl is-active --quiet $service; then
        /usr/local/bin/telegram-notify.sh "⚠️ Сервис $service не работает!"
        systemctl restart $service
        echo "$(date) - Перезапуск службы $service" >> /var/log/monitoring-healthcheck.log
    fi
}

# Проверка размера лог-файлов
check_log_size() {
    local log_file=$1
    local max_size_mb=$2
    
    if [ -f "$log_file" ]; then
        size_mb=$(du -m "$log_file" | cut -f1)
        if [ "$size_mb" -gt "$max_size_mb" ]; then
            /usr/local/bin/telegram-notify.sh "⚠️ Лог-файл $log_file превысил $max_size_mb MB (текущий размер: $size_mb MB)"
        fi
    fi
}

# Проверка дискового пространства
check_disk_space() {
    local mount_point=$1
    local threshold=$2
    
    usage=$(df -h "$mount_point" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$usage" -gt "$threshold" ]; then
        /usr/local/bin/telegram-notify.sh "⚠️ Недостаточно места на диске $mount_point: использовано $usage%"
    fi
}

# Проверка служб
check_service "netdata"
check_service "nginx"

# Проверка лог-файлов
check_log_size "/path/to/project/logs/combined.log" 100
check_log_size "/path/to/project/logs/error.log" 50
check_log_size "/path/to/project/backend-rust/logs/info.log" 100
check_log_size "/path/to/project/backend-rust/logs/error.log" 50

# Проверка дискового пространства
check_disk_space "/" 80
```

**Шаг 2: Добавление в crontab**
```bash
chmod +x /usr/local/bin/monitoring-healthcheck.sh
crontab -e
```

Добавьте:
```
0 */6 * * * /usr/local/bin/monitoring-healthcheck.sh
```

## 6. Оптимизация и масштабирование

### 6.1 Оптимизация хранения логов

**Шаг 1: Настройка сжатия и архивации старых логов**
Создайте файл `/usr/local/bin/archive-old-logs.sh`:

```bash
#!/bin/bash

LOGS_DIR="/path/to/project/logs"
BACKEND_LOGS_DIR="/path/to/project/backend-rust/logs"
ARCHIVE_DIR="/path/to/archive/logs"
DAYS_TO_KEEP=30

# Создание архивной директории
mkdir -p "$ARCHIVE_DIR"

# Функция архивации
archive_old_logs() {
    local source_dir=$1
    local name=$2
    
    # Находим и архивируем старые логи
    find "$source_dir" -name "*.log.*" -type f -mtime +7 | while read log_file; do
        base_name=$(basename "$log_file")
        dated_name="${name}-$(date +%Y%m%d)-${base_name}"
        gzip -c "$log_file" > "${ARCHIVE_DIR}/${dated_name}.gz"
        rm "$log_file"
        echo "Архивирован: $log_file -> ${ARCHIVE_DIR}/${dated_name}.gz"
    done
    
    # Удаляем очень старые архивы
    find "$ARCHIVE_DIR" -name "${name}-*.gz" -type f -mtime +$DAYS_TO_KEEP -delete
}

# Архивация логов
archive_old_logs "$LOGS_DIR" "frontend"
archive_old_logs "$BACKEND_LOGS_DIR" "backend"
```

**Шаг 2: Добавление в crontab**
```bash
chmod +x /usr/local/bin/archive-old-logs.sh
crontab -e
```

Добавьте:
```
0 2 * * 0 /usr/local/bin/archive-old-logs.sh
```

### 6.2 Масштабирование мониторинга

**Шаг 1: Настройка агрегации логов для нескольких инстансов**
Создайте файл `/etc/rsyslog.d/10-quer-calc.conf`:

```
# Шаблон для логов приложения
template(name="QuerCalcLogs" type="string" string="/var/log/quer-calc/%PROGRAMNAME%.log")

# Правила для Next.js
if $programname startswith 'quer-calc-next' then {
    action(type="omfile" DynaFile="QuerCalcLogs")
    stop
}

# Правила для Rust бэкенда
if $programname startswith 'quer-calc-backend' then {
    action(type="omfile" DynaFile="QuerCalcLogs")
    stop
}
```

**Шаг 2: Настройка агрегации метрик**
Если в будущем потребуется масштабирование, настройте центральный сервер мониторинга:

```bash
# На центральном сервере мониторинга
mkdir -p /etc/netdata/stream.d/
cat > /etc/netdata/stream.d/stream.conf << EOL
[stream]
  enabled = yes
  api key = 00000000-0000-0000-0000-000000000000
  destination = 127.0.0.1:19999
EOL

# На каждом клиентском сервере
mkdir -p /etc/netdata/stream.d/
cat > /etc/netdata/stream.d/stream.conf << EOL
[stream]
  enabled = yes
  api key = 00000000-0000-0000-0000-000000000000
  destination = monitoring-server-ip:19999
EOL
```

**Настройка завершена! В этом документе представлен полный план настройки мониторинга и логирования для вашего проекта Quer Calculator.**

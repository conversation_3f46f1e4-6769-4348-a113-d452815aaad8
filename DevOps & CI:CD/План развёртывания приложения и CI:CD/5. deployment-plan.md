# План настройки процесса деплоя

## 1. Создание скриптов деплоя

### 1.1. Скрипт для сборки, тестирования и деплоя Next.js приложения

```bash
#!/bin/bash
# deploy-frontend.sh

set -e  # Останавливаться при любой ошибке

# Переменные окружения
ENV=${1:-production}
DEPLOY_ID=$(date +%Y%m%d%H%M%S)
APP_NAME="quer-calc-next"
DEPLOY_PATH="/var/www/$APP_NAME"
RELEASE_PATH="$DEPLOY_PATH/releases/$DEPLOY_ID"
CURRENT_PATH="$DEPLOY_PATH/current"
PREVIOUS_PATH="$DEPLOY_PATH/previous"

echo "🚀 Начинаем деплой фронтенда ($ENV) с ID: $DEPLOY_ID"

# Шаг 1: Подготовка директорий
echo "📁 Подготовка директорий"
mkdir -p $RELEASE_PATH

# Шаг 2: Установка зависимостей и сборка
echo "📦 Установка зависимостей"
npm ci

echo "🔍 Запуск линтера"
npm run lint

echo "🧪 Запуск тестов"
npm test

echo "🔨 Сборка приложения"
NODE_ENV=$ENV npm run build

# Шаг 3: Копирование файлов в директорию релиза
echo "📋 Копирование файлов для деплоя"
cp -R .next $RELEASE_PATH/
cp -R public $RELEASE_PATH/
cp -R node_modules $RELEASE_PATH/
cp package.json $RELEASE_PATH/
cp next.config.js $RELEASE_PATH/

# Шаг 4: Создание .env файла
echo "🔧 Создание .env файла для $ENV"
envsubst < .env.$ENV > $RELEASE_PATH/.env

# Шаг 5: Переключение на новую версию (атомарно)
echo "🔄 Переключение на новую версию"
if [ -L $CURRENT_PATH ]; then
  if [ -L $PREVIOUS_PATH ]; then
    rm $PREVIOUS_PATH
  fi
  mv $CURRENT_PATH $PREVIOUS_PATH
fi
ln -s $RELEASE_PATH $CURRENT_PATH

# Шаг 6: Перезапуск сервиса
echo "🔄 Перезапуск сервиса"
sudo systemctl restart $APP_NAME

# Шаг 7: Проверка работоспособности
echo "🔍 Проверка работоспособности"
sleep 5  # Даем сервису время на запуск

health_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/healthcheck)
if [ $health_status -ne 200 ]; then
  echo "❌ Деплой не прошел проверку работоспособности. Выполняем откат."
  rm $CURRENT_PATH
  ln -s $PREVIOUS_PATH $CURRENT_PATH
  sudo systemctl restart $APP_NAME
  exit 1
fi

# Шаг 8: Очистка старых релизов (оставляем последние 5)
echo "🧹 Очистка старых релизов"
ls -dt $DEPLOY_PATH/releases/* | tail -n +6 | xargs -r rm -rf

echo "✅ Деплой фронтенда успешно завершен"
```

### 1.2. Скрипт для компиляции и деплоя Rust бэкенда

```bash
#!/bin/bash
# deploy-backend.sh

set -e  # Останавливаться при любой ошибке

# Переменные окружения
ENV=${1:-production}
DEPLOY_ID=$(date +%Y%m%d%H%M%S)
APP_NAME="quer-calc-backend"
DEPLOY_PATH="/var/www/$APP_NAME"
RELEASE_PATH="$DEPLOY_PATH/releases/$DEPLOY_ID"
CURRENT_PATH="$DEPLOY_PATH/current"
PREVIOUS_PATH="$DEPLOY_PATH/previous"
CONFIG_PATH="/etc/$APP_NAME"

echo "🚀 Начинаем деплой бэкенда ($ENV) с ID: $DEPLOY_ID"

# Шаг 1: Подготовка директорий
echo "📁 Подготовка директорий"
mkdir -p $RELEASE_PATH

# Шаг 2: Компиляция и тестирование
echo "🧪 Запуск тестов"
cargo test

echo "🔨 Сборка приложения в режиме release"
cargo build --release

# Шаг 3: Копирование бинарного файла в директорию релиза
echo "📋 Копирование файлов для деплоя"
cp target/release/$APP_NAME $RELEASE_PATH/
mkdir -p $RELEASE_PATH/config

# Шаг 4: Создание конфигурационного файла
echo "🔧 Создание конфигурационного файла для $ENV"
envsubst < config.$ENV.toml > $RELEASE_PATH/config/config.toml

# Шаг 5: Переключение на новую версию (атомарно)
echo "🔄 Переключение на новую версию"
if [ -L $CURRENT_PATH ]; then
  if [ -L $PREVIOUS_PATH ]; then
    rm $PREVIOUS_PATH
  fi
  mv $CURRENT_PATH $PREVIOUS_PATH
fi
ln -s $RELEASE_PATH $CURRENT_PATH

# Шаг 6: Запуск новой версии сервиса
echo "🔄 Перезапуск сервиса с использованием systemd socket activation для zero-downtime"
sudo systemctl reload $APP_NAME

# Шаг 7: Проверка работоспособности
echo "🔍 Проверка работоспособности"
sleep 5  # Даем сервису время на запуск

health_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/healthcheck)
if [ $health_status -ne 200 ]; then
  echo "❌ Деплой не прошел проверку работоспособности. Выполняем откат."
  rm $CURRENT_PATH
  ln -s $PREVIOUS_PATH $CURRENT_PATH
  sudo systemctl restart $APP_NAME
  exit 1
fi

# Шаг 8: Очистка старых релизов (оставляем последние 5)
echo "🧹 Очистка старых релизов"
ls -dt $DEPLOY_PATH/releases/* | tail -n +6 | xargs -r rm -rf

echo "✅ Деплой бэкенда успешно завершен"
```

### 1.3. Скрипт для выполнения миграций базы данных

```bash
#!/bin/bash
# database-migrate.sh

set -e  # Останавливаться при любой ошибке

# Переменные окружения
ENV=${1:-production}
MIGRATIONS_DIR="./migrations"
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d%H%M%S)

echo "🚀 Начинаем миграцию базы данных ($ENV)"

# Шаг 1: Загрузка переменных окружения
echo "📋 Загрузка переменных окружения"
if [ -f .env.$ENV ]; then
  source .env.$ENV
else
  echo "❌ Файл .env.$ENV не найден"
  exit 1
fi

# Шаг 2: Создание резервной копии перед миграцией
echo "💾 Создание резервной копии базы данных"
mkdir -p $BACKUP_DIR
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME -F c -f $BACKUP_DIR/backup_$TIMESTAMP.dump

# Шаг 3: Проверка состояния миграций
echo "🔍 Проверка состояния миграций"
# Используем инструмент sqlx для миграций (или другой по вашему выбору)
sqlx migrate info --database-url $DATABASE_URL

# Шаг 4: Применение миграций
echo "🔄 Применение миграций"
sqlx migrate run --database-url $DATABASE_URL

# Шаг 5: Проверка после миграции
echo "✅ Проверка состояния после миграции"
sqlx migrate info --database-url $DATABASE_URL

echo "✅ Миграция базы данных успешно завершена"
```

### 1.4. Скрипт для отката миграций

```bash
#!/bin/bash
# database-rollback.sh

set -e  # Останавливаться при любой ошибке

# Переменные окружения
ENV=${1:-production}
BACKUP_DIR="./backups"
TIMESTAMP=${2:-$(ls -t $BACKUP_DIR | head -1 | sed 's/backup_\(.*\).dump/\1/')}

echo "🔄 Начинаем откат базы данных ($ENV) к версии $TIMESTAMP"

# Шаг 1: Загрузка переменных окружения
echo "📋 Загрузка переменных окружения"
if [ -f .env.$ENV ]; then
  source .env.$ENV
else
  echo "❌ Файл .env.$ENV не найден"
  exit 1
fi

# Шаг 2: Проверка наличия резервной копии
BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.dump"
if [ ! -f $BACKUP_FILE ]; then
  echo "❌ Резервная копия $BACKUP_FILE не найдена"
  exit 1
fi

# Шаг 3: Восстановление из резервной копии
echo "🔄 Восстановление из резервной копии"
pg_restore -h $DB_HOST -U $DB_USER -d $DB_NAME -c -F c $BACKUP_FILE

echo "✅ Откат базы данных успешно завершен"
```

## 2. Настройка CI/CD с GitHub Actions

### 2.1. Пайплайн для тестирования при Pull Request (`.github/workflows/pull-request.yml`)

```yaml
name: Pull Request Checks

on:
  pull_request:
    branches: [ main, staging, develop ]

jobs:
  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run tests
        run: npm test
      
      - name: Build check
        run: npm run build

  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
          components: rustfmt, clippy
      
      - name: Rust cache
        uses: Swatinem/rust-cache@v2
      
      - name: Run cargo fmt
        uses: actions-rs/cargo@v1
        with:
          command: fmt
          args: --all -- --check
      
      - name: Run cargo clippy
        uses: actions-rs/cargo@v1
        with:
          command: clippy
          args: -- -D warnings
      
      - name: Run tests
        uses: actions-rs/cargo@v1
        with:
          command: test
      
      - name: Build check
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release
```

### 2.2. Пайплайн для автоматического деплоя на staging (`.github/workflows/staging-deploy.yml`)

```yaml
name: Staging Deployment

on:
  push:
    branches: [ staging ]

jobs:
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
      - uses: actions/checkout@v3
      
      # Общие шаги сборки и тестирования для фронтенда и бэкенда
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      
      - name: Build and test Frontend
        run: |
          npm ci
          npm run lint
          npm test
          npm run build
      
      - name: Build and test Backend
        run: |
          cd backend-rust
          cargo test
          cargo build --release
      
      # Деплой на Staging-сервер с использованием rolling release
      - name: Deploy to Staging
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script_stop: true
          script: |
            # Создаем уникальный ID для деплоя
            DEPLOY_ID=$(date +%Y%m%d%H%M%S)
            
            # Клонируем репозиторий во временную директорию
            git clone --depth 1 -b staging ${{ github.repository }} /tmp/deployment-$DEPLOY_ID
            cd /tmp/deployment-$DEPLOY_ID
            
            # Деплоим бэкенд с zero-downtime
            cd backend-rust
            ./deploy-backend.sh staging
            
            # Проверяем работоспособность бэкенда
            backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/healthcheck)
            if [ $backend_status -ne 200 ]; then
              echo "Backend deployment failed health check. Rolling back."
              ./rollback.sh
              exit 1
            fi
            
            # Деплоим фронтенд с zero-downtime
            cd ../
            ./deploy-frontend.sh staging
            
            # Проверяем работоспособность фронтенда
            frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/healthcheck)
            if [ $frontend_status -ne 200 ]; then
              echo "Frontend deployment failed health check. Rolling back."
              ./rollback.sh
              exit 1
            fi
            
            # Очистка
            cd /
            rm -rf /tmp/deployment-$DEPLOY_ID
      
      # Автоматические интеграционные тесты после деплоя
      - name: Run Integration Tests
        run: |
          npm run test:integration:staging
```

### 2.3. Пайплайн для деплоя на production (`.github/workflows/production-deploy.yml`)

```yaml
name: Production Deployment

on:
  release:
    types: [published]

jobs:
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - uses: actions/checkout@v3
      
      # Общие шаги сборки и тестирования для фронтенда и бэкенда
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
      
      - name: Build and test Frontend
        run: |
          npm ci
          npm run lint
          npm test
          npm run build
      
      - name: Build and test Backend
        run: |
          cd backend-rust
          cargo test
          cargo build --release
      
      # Деплой на Production-сервер с использованием rolling release
      - name: Deploy to Production
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script_stop: true
          script: |
            # Создаем уникальный ID для деплоя
            DEPLOY_ID=$(date +%Y%m%d%H%M%S)
            
            # Клонируем репозиторий во временную директорию
            git clone --depth 1 -b main ${{ github.repository }} /tmp/deployment-$DEPLOY_ID
            cd /tmp/deployment-$DEPLOY_ID
            
            # Выполняем миграцию базы данных (с резервным копированием)
            ./database-migrate.sh production
            
            # Деплоим бэкенд с zero-downtime на первый сервер
            cd backend-rust
            
            # Rolling release - деплой на 50% серверов
            for server in $(cat /etc/quer-calc/backend-servers-group1.txt); do
              ssh $server "./deploy-backend.sh production"
              
              # Проверяем работоспособность
              backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:8080/api/healthcheck)
              if [ $backend_status -ne 200 ]; then
                echo "Backend deployment failed health check on $server. Rolling back."
                ssh $server "./rollback.sh backend"
                exit 1
              fi
            done
            
            # Деплоим фронтенд с zero-downtime на первую группу серверов
            cd ../
            
            # Rolling release - деплой на 50% серверов
            for server in $(cat /etc/quer-calc/frontend-servers-group1.txt); do
              ssh $server "./deploy-frontend.sh production"
              
              # Проверяем работоспособность
              frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:3000/api/healthcheck)
              if [ $frontend_status -ne 200 ]; then
                echo "Frontend deployment failed health check on $server. Rolling back."
                ssh $server "./rollback.sh frontend"
                exit 1
              fi
            done
            
            # Если первая группа успешно обновлена, переходим ко второй группе
            for server in $(cat /etc/quer-calc/backend-servers-group2.txt); do
              ssh $server "./deploy-backend.sh production"
              backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:8080/api/healthcheck)
              if [ $backend_status -ne 200 ]; then
                echo "Backend deployment failed health check on $server. Rolling back all servers."
                for rollback_server in $(cat /etc/quer-calc/backend-servers-all.txt); do
                  ssh $rollback_server "./rollback.sh backend"
                done
                exit 1
              fi
            done
            
            for server in $(cat /etc/quer-calc/frontend-servers-group2.txt); do
              ssh $server "./deploy-frontend.sh production"
              frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://$server:3000/api/healthcheck)
              if [ $frontend_status -ne 200 ]; then
                echo "Frontend deployment failed health check on $server. Rolling back all servers."
                for rollback_server in $(cat /etc/quer-calc/frontend-servers-all.txt); do
                  ssh $rollback_server "./rollback.sh frontend"
                done
                exit 1
              fi
            done
            
            # Очистка
            cd /
            rm -rf /tmp/deployment-$DEPLOY_ID
      
      # Запуск smoke-тестов после полного деплоя
      - name: Run Smoke Tests
        run: |
          npm run test:smoke:production
      
      # Мониторинг производительности после деплоя
      - name: Performance Monitoring
        run: |
          npm run monitor:performance
```

## 3. Скрипты для post-deploy проверок

### 3.1. Скрипт для проверки доступности (`health-check.sh`)

```bash
#!/bin/bash
# health-check.sh

set -e  # Останавливаться при любой ошибке

# Переменные окружения
ENV=${1:-production}
FRONTEND_URL=${2:-"http://localhost:3000"}
BACKEND_URL=${3:-"http://localhost:8080"}
TIMEOUT=5

echo "🔍 Начинаем проверку доступности ($ENV)"

# Шаг 1: Проверка Frontend health endpoint
echo "🌐 Проверка доступности фронтенда"
frontend_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $FRONTEND_URL/api/healthcheck)
if [ $frontend_status -ne 200 ]; then
  echo "❌ Фронтенд недоступен (статус: $frontend_status)"
  exit 1
else
  echo "✅ Фронтенд доступен"
fi

# Шаг 2: Проверка Backend health endpoint
echo "🌐 Проверка доступности бэкенда"
backend_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $BACKEND_URL/api/healthcheck)
if [ $backend_status -ne 200 ]; then
  echo "❌ Бэкенд недоступен (статус: $backend_status)"
  exit 1
else
  echo "✅ Бэкенд доступен"
fi

# Шаг 3: Проверка основных маршрутов фронтенда
echo "🌐 Проверка основных маршрутов фронтенда"
routes=("/" "/calculator" "/sign-in" "/sign-up")
for route in "${routes[@]}"; do
  route_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $FRONTEND_URL$route)
  if [ $route_status -ne 200 ]; then
    echo "❌ Маршрут $route недоступен (статус: $route_status)"
    exit 1
  else
    echo "✅ Маршрут $route доступен"
  fi
done

# Шаг 4: Проверка API эндпоинтов
echo "🌐 Проверка API эндпоинтов"
api_status=$(curl -s -o /dev/null -w "%{http_code}" -m $TIMEOUT $BACKEND_URL/api/calculate -X POST -H "Content-Type: application/json" -d '{"entryPrice":"100","stopLoss":"90","risk":"1","riskUnit":"percent","accountBalance":"10000","nominalRR":"2","maxLeverage":"10","entryFee":"0.1","exitFee":"0.1","maintenanceMargin":"0.5","safetyBuffer":"2"}')
if [ $api_status -ne 200 ]; then
  echo "❌ API calculate недоступен (статус: $api_status)"
  exit 1
else
  echo "✅ API calculate доступен"
fi

echo "✅ Все проверки доступности успешно пройдены"
```

### 3.2. Скрипт для автоматических smoke-тестов (`smoke-tests.js`)

```javascript
// smoke-tests.js
const puppeteer = require('puppeteer');
const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'https://app.quercalc.com';
const API_URL = process.env.API_URL || 'https://api.quercalc.com';
const TIMEOUT = 30000;

(async () => {
  console.log('🔍 Запуск smoke-тестов');
  
  // Тест 1: Проверка API calculate
  console.log('🧪 Тест 1: Проверка API calculate');
  try {
    const apiResponse = await axios.post(`${API_URL}/api/calculate`, {
      entryPrice: '100',
      stopLoss: '90',
      risk: '1',
      riskUnit: 'percent',
      accountBalance: '10000',
      nominalRR: '2',
      maxLeverage: '10',
      entryFee: '0.1',
      exitFee: '0.1',
      maintenanceMargin: '0.5',
      safetyBuffer: '2'
    }, {
      timeout: TIMEOUT
    });
    
    if (apiResponse.status !== 200 || !apiResponse.data.positionSize) {
      throw new Error(`Некорректный ответ API: ${JSON.stringify(apiResponse.data)}`);
    }
    console.log('✅ Тест 1 пройден успешно');
  } catch (error) {
    console.error(`❌ Тест 1 не пройден: ${error.message}`);
    process.exit(1);
  }
  
  // Тест 2: Проверка загрузки страниц через Puppeteer
  console.log('🧪 Тест 2: Проверка загрузки страниц');
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Проверка главной страницы
    await page.goto(`${BASE_URL}`, { waitUntil: 'networkidle2', timeout: TIMEOUT });
    console.log('✅ Главная страница загружена');
    
    // Проверка страницы калькулятора
    await page.goto(`${BASE_URL}/calculator`, { waitUntil: 'networkidle2', timeout: TIMEOUT });
    const calculatorVisible = await page.evaluate(() => {
      return document.querySelector('.calculator-container') !== null;
    });
    
    if (!calculatorVisible) {
      throw new Error('Калькулятор не найден на странице');
    }
    console.log('✅ Страница калькулятора загружена');
    
    // Проверка форм ввода калькулятора
    await page.type('input[name="entryPrice"]', '100');
    await page.type('input[name="stopLoss"]', '90');
    
    // Дожидаемся результатов расчета
    await page.waitForSelector('.metrics-card', { timeout: TIMEOUT });
    const metricsVisible = await page.evaluate(() => {
      return document.querySelector('.metrics-card') !== null;
    });
    
    if (!metricsVisible) {
      throw new Error('Результаты расчета не отображаются');
    }
    console.log('✅ Калькулятор работает корректно');
    
    console.log('✅ Тест 2 пройден успешно');
  } catch (error) {
    console.error(`❌ Тест 2 не пройден: ${error.message}`);
    await browser.close();
    process.exit(1);
  }
  
  await browser.close();
  console.log('✅ Все smoke-тесты пройдены успешно');
})();
```

### 3.3. Скрипт для системы отката при неудачном деплое (`rollback.sh`)

```bash
#!/bin/bash
# rollback.sh

set -e  # Останавливаться при любой ошибке

# Переменные окружения
SERVICE=${1:-"all"}  # frontend, backend или all
ENV=${2:-production}

echo "🔄 Начинаем откат деплоя ($SERVICE в $ENV)"

# Шаг 1: Определяем пути
FRONTEND_APP="quer-calc-next"
BACKEND_APP="quer-calc-backend"
FRONTEND_PATH="/var/www/$FRONTEND_APP"
BACKEND_PATH="/var/www/$BACKEND_APP"

# Шаг 2: Откат фронтенда, если требуется
if [ "$SERVICE" = "frontend" ] || [ "$SERVICE" = "all" ]; then
  echo "🔄 Откат фронтенда"
  
  if [ ! -L "$FRONTEND_PATH/previous" ]; then
    echo "❌ Предыдущая версия фронтенда не найдена"
  else
    if [ -L "$FRONTEND_PATH/current" ]; then
      rm "$FRONTEND_PATH/current"
    fi
    ln -s "$(readlink "$FRONTEND_PATH/previous")" "$FRONTEND_PATH/current"
    sudo systemctl restart $FRONTEND_APP
    echo "✅ Откат фронтенда выполнен успешно"
  fi
fi

# Шаг 3: Откат бэкенда, если требуется
if [ "$SERVICE" = "backend" ] || [ "$SERVICE" = "all" ]; then
  echo "🔄 Откат бэкенда"
  
  if [ ! -L "$BACKEND_PATH/previous" ]; then
    echo "❌ Предыдущая версия бэкенда не найдена"
  else
    if [ -L "$BACKEND_PATH/current" ]; then
      rm "$BACKEND_PATH/current"
    fi
    ln -s "$(readlink "$BACKEND_PATH/previous")" "$BACKEND_PATH/current"
    sudo systemctl restart $BACKEND_APP
    echo "✅ Откат бэкенда выполнен успешно"
  fi
fi

# Шаг 4: Проверка после отката
echo "🔍 Проверка после отката"

# Проверяем фронтенд, если он был откачен
if [ "$SERVICE" = "frontend" ] || [ "$SERVICE" = "all" ]; then
  sleep 5  # Даем время на запуск
  frontend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/healthcheck)
  if [ $frontend_status -ne 200 ]; then
    echo "❌ Фронтенд недоступен после отката (статус: $frontend_status)"
    exit 1
  else
    echo "✅ Фронтенд доступен после отката"
  fi
fi

# Проверяем бэкенд, если он был откачен
if [ "$SERVICE" = "backend" ] || [ "$SERVICE" = "all" ]; then
  sleep 5  # Даем время на запуск
  backend_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/healthcheck)
  if [ $backend_status -ne 200 ]; then
    echo "❌ Бэкенд недоступен после отката (статус: $backend_status)"
    exit 1
  else
    echo "✅ Бэкенд доступен после отката"
  fi
fi

echo "✅ Откат деплоя успешно завершен"
```

### 3.4. Скрипт для мониторинга производительности после деплоя (`performance-monitor.js`)

```javascript
// performance-monitor.js
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_URL = process.env.API_URL || 'https://api.quercalc.com';
const FRONTEND_URL = process.env.FRONTEND_URL || 'https://app.quercalc.com';
const THRESHOLD_MULTIPLIER = 1.5; // Допустимое увеличение времени ответа в 1.5 раза
const MONITORING_DURATION = 10 * 60 * 1000; // 10 минут мониторинга
const CHECK_INTERVAL = 30 * 1000; // Проверка каждые 30 секунд
const METRICS_FILE = path.join(__dirname, 'baseline_metrics.json');

// Тестовый запрос для API
const TEST_PAYLOAD = {
  entryPrice: '100',
  stopLoss: '90',
  risk: '1',
  riskUnit: 'percent',
  accountBalance: '10000',
  nominalRR: '2',
  maxLeverage: '10',
  entryFee: '0.1',
  exitFee: '0.1',
  maintenanceMargin: '0.5',
  safetyBuffer: '2'
};

// Функция для замера времени ответа API
async function measureApiResponseTime() {
  const start = Date.now();
  try {
    await axios.post(`${API_URL}/api/calculate`, TEST_PAYLOAD, { timeout: 10000 });
    return Date.now() - start;
  } catch (error) {
    console.error(`API Error: ${error.message}`);
    return -1; // Ошибка
  }
}

// Функция для замера времени загрузки фронтенда
async function measureFrontendLoadTime() {
  const start = Date.now();
  try {
    await axios.get(FRONTEND_URL, { timeout: 10000 });
    return Date.now() - start;
  } catch (error) {
    console.error(`Frontend Error: ${error.message}`);
    return -1; // Ошибка
  }
}

// Загрузка базовых метрик
function loadBaselineMetrics() {
  try {
    if (fs.existsSync(METRICS_FILE)) {
      const data = fs.readFileSync(METRICS_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error(`Error loading baseline metrics: ${error.message}`);
  }
  
  // Если файл не существует или произошла ошибка, возвращаем значения по умолчанию
  return {
    apiResponseTime: 500, // 500 мс
    frontendLoadTime: 1000 // 1 секунда
  };
}

// Сохранение текущих метрик как базовых
function saveBaselineMetrics(metrics) {
  try {
    fs.writeFileSync(METRICS_FILE, JSON.stringify(metrics, null, 2));
    console.log('✅ Baseline metrics saved');
  } catch (error) {
    console.error(`Error saving baseline metrics: ${error.message}`);
  }
}

// Функция мониторинга
async function monitorPerformance() {
  console.log('🔍 Начинаем мониторинг производительности');
  
  const baseline = loadBaselineMetrics();
  console.log(`Базовые метрики: API ${baseline.apiResponseTime}ms, Frontend ${baseline.frontendLoadTime}ms`);
  
  const maxApiTime = baseline.apiResponseTime * THRESHOLD_MULTIPLIER;
  const maxFrontendTime = baseline.frontendLoadTime * THRESHOLD_MULTIPLIER;
  
  console.log(`Пороговые значения: API ${maxApiTime}ms, Frontend ${maxFrontendTime}ms`);
  
  let failures = 0;
  const maxFailures = 3; // Максимальное количество последовательных провалов
  
  const endTime = Date.now() + MONITORING_DURATION;
  
  while (Date.now() < endTime) {
    // Замеряем текущие метрики
    const apiTime = await measureApiResponseTime();
    const frontendTime = await measureFrontendLoadTime();
    
    console.log(`[${new Date().toISOString()}] API: ${apiTime}ms, Frontend: ${frontendTime}ms`);
    
    // Проверяем, не превышены ли пороги
    if (apiTime === -1 || frontendTime === -1 || apiTime > maxApiTime || frontendTime > maxFrontendTime) {
      failures++;
      console.warn(`⚠️ Порог превышен (${failures}/${maxFailures})`);
      
      if (failures >= maxFailures) {
        console.error('❌ Слишком много последовательных провалов! Рекомендуется откат.');
        process.exit(1);
      }
    } else {
      failures = 0; // Сбрасываем счетчик при успешной проверке
    }
    
    // Ждем до следующей проверки
    await new Promise(resolve => setTimeout(resolve, CHECK_INTERVAL));
  }
  
  console.log('✅ Мониторинг производительности завершен успешно');
  
  // Сохраняем текущие метрики как базовые (если они лучше предыдущих)
  const finalApiTime = await measureApiResponseTime();
  const finalFrontendTime = await measureFrontendLoadTime();
  
  if (finalApiTime !== -1 && finalFrontendTime !== -1) {
    // Используем среднее значение между текущими базовыми и новыми измерениями
    const newBaseline = {
      apiResponseTime: Math.min((baseline.apiResponseTime + finalApiTime) / 2, baseline.apiResponseTime),
      frontendLoadTime: Math.min((baseline.frontendLoadTime + finalFrontendTime) / 2, baseline.frontendLoadTime)
    };
    
    saveBaselineMetrics(newBaseline);
  }
}

monitorPerformance().catch(error => {
  console.error(`Ошибка мониторинга: ${error.message}`);
  process.exit(1);
});
```

## 4. Файлы конфигурации для настройки zero-downtime деплоя

### 4.1. Конфигурация Nginx для балансировки нагрузки (`/etc/nginx/sites-available/quer-calc`)

```nginx
upstream backend {
    server backend1.quer-calc.internal:8080;
    server backend2.quer-calc.internal:8080;
    keepalive 64;
}

upstream frontend {
    server frontend1.quer-calc.internal:3000;
    server frontend2.quer-calc.internal:3000;
    keepalive 64;
}

server {
    listen 80;
    server_name app.quercalc.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name app.quercalc.com;

    ssl_certificate /etc/letsencrypt/live/app.quercalc.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/app.quercalc.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Настройки для фронтенда Next.js
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Настройки для zero-downtime и rolling updates
        proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
    }
}

server {
    listen 80;
    server_name api.quercalc.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name api.quercalc.com;

    ssl_certificate /etc/letsencrypt/live/api.quercalc.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.quercalc.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # Настройки для бэкенда Rust
    location / {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Настройки для zero-downtime и rolling updates
        proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
    }
}
```

### 4.2. Systemd Socket Activation для Zero-Downtime Restart (`/etc/systemd/system/quer-calc-backend.socket`)

```ini
[Unit]
Description=Socket for Quer Calc Backend
PartOf=quer-calc-backend.service

[Socket]
ListenStream=8080
NoDelay=true
ReusePort=true
Service=quer-calc-backend.service

[Install]
WantedBy=sockets.target
```

### 4.3. Systemd Service для Backend (`/etc/systemd/system/quer-calc-backend.service`)

```ini
[Unit]
Description=Quer Calc Backend Service
After=network.target
Requires=quer-calc-backend.socket

[Service]
Type=notify
User=quer-calc
Group=quer-calc
WorkingDirectory=/var/www/quer-calc-backend/current
ExecStart=/var/www/quer-calc-backend/current/quer-calc-backend
Restart=on-failure
RestartSec=5s
TimeoutStartSec=5
TimeoutStopSec=5

# Secure service settings
PrivateTmp=true
ProtectSystem=full
NoNewPrivileges=true

# Environment variables
Environment=ENV=production

[Install]
WantedBy=multi-user.target
```

### 4.4. Systemd Service для Frontend (`/etc/systemd/system/quer-calc-next.service`)

```ini
[Unit]
Description=Quer Calc Frontend Service
After=network.target

[Service]
Type=simple
User=quer-calc
Group=quer-calc
WorkingDirectory=/var/www/quer-calc-next/current
ExecStart=/usr/bin/node /var/www/quer-calc-next/current/node_modules/.bin/next start
Restart=on-failure
RestartSec=5s

# Secure service settings
PrivateTmp=true
ProtectSystem=full
NoNewPrivileges=true

# Environment variables
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
```

## 5. План внедрения

1. **Подготовительные работы (1-2 дня):**
   - Настройка серверов (staging и production)
   - Установка необходимого ПО (Nginx, Docker, базы данных)
   - Настройка DNS и SSL-сертификатов

2. **Настройка CI/CD инфраструктуры (1-2 дня):**
   - Создание репозитория секретов в GitHub
   - Настройка GitHub Actions workflow файлов
   - Настройка окружений в GitHub (staging и production)

3. **Создание и тестирование скриптов деплоя (2-3 дня):**
   - Написание и отладка скриптов для деплоя фронтенда
   - Написание и отладка скриптов для деплоя бэкенда
   - Написание и отладка скриптов для миграций БД
   - Тестирование системы отката

4. **Настройка системы мониторинга и оповещений (1-2 дня):**
   - Настройка health-check эндпоинтов
   - Настройка алертов при падении сервисов
   - Интеграция с системой мониторинга (например, Grafana + Prometheus)

5. **Тестирование полного цикла деплоя (1-2 дня):**
   - Тестирование на staging-окружении
   - Симуляция ошибок и проверка работы системы отката
   - Проверка zero-downtime обновлений

6. **Документация и обучение (1 день):**
   - Создание документации по процессу деплоя
   - Обучение команды процессу релиза и отката

7. **Запуск в production (1 день):**
   - Первоначальный деплой на production-окружение
   - Мониторинг и оптимизация процесса

## 6. Мониторинг и поддержка процесса деплоя

1. **Система мониторинга:**
   - Интеграция с Prometheus для сбора метрик
   - Настройка Grafana дашбордов для визуализации
   - Создание алертов на критические ошибки

2. **Логирование:**
   - Централизованный сбор логов (ELK Stack или Loki)
   - Настройка ротации логов
   - Поиск и анализ ошибок

3. **Оповещения:**
   - Интеграция с Slack/Discord/Email для оповещений о деплоях
   - Настройка оповещений при неудачном деплое
   - Ежедневный отчет о состоянии сервисов

4. **Регулярное обновление:**
   - Ежемесячный пересмотр процесса деплоя
   - Оптимизация скриптов и workflow
   - Обновление зависимостей и инфраструктуры

# План управления секретами и конфигурацией

## 1. Настройка переменных окружения

### 1.1. Организация структуры директории /etc/secrets

```bash
# Создание основной структуры директорий
sudo mkdir -p /etc/secrets/{staging,production}
sudo mkdir -p /etc/secrets/common

# Установка правильных разрешений
sudo chmod 750 /etc/secrets
sudo chmod 750 /etc/secrets/{staging,production,common}

# Создание группы для доступа к секретам
sudo groupadd secrets-access
sudo chown root:secrets-access /etc/secrets
sudo chown root:secrets-access /etc/secrets/{staging,production,common}

# Добавление сервисных пользователей в группу secrets-access
sudo usermod -a -G secrets-access nodejs-app-user
```

### 1.2. Создание файлов с секретами для разных окружений

#### Общие секреты (для всех окружений)

Создаем файл `/etc/secrets/common/app.env`:

```env
# Общие переменные для всех окружений
APP_PORT=8080
NODE_ENV=production
RATE_LIMIT_BURST=100
RATE_LIMIT_PER_SEC=50
```

#### Секреты для staging

Создаем файл `/etc/secrets/staging/app.env`:

```env
# Stripe API ключи (тестовые)
STRIPE_SECRET_KEY=sk_test_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
STRIPE_PUBLISHABLE_KEY=pk_test_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
STRIPE_WEBHOOK_SECRET=whsec_test_1234567890abcdefghijklmnopqrstuvwxyz

# Clerk API ключи (тестовые)
CLERK_SECRET_KEY=sk_test_clerk1234567890abcdefghijklmnopqrstuvwxyz
CLERK_PUBLISHABLE_KEY=pk_test_clerk1234567890abcdefghijklmnopqrstuvwxyz
CLERK_WEBHOOK_SECRET=whsec_test_clerk1234567890abcdefghijklmnopqrstuvwxyz

# Stripe тестовые product/price IDs
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_test_1234567890monthly
NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_test_1234567890annual

# URL-адреса для окружения
NEXT_PUBLIC_SITE_URL=https://staging.quercalc.com
CORS_ORIGINS=https://staging.quercalc.com,http://localhost:3000

# Флаг для отладки API
ALLOW_DIRECT_USER_ID=true
```

#### Секреты для production

Создаем файл `/etc/secrets/production/app.env`:

```env
# Stripe API ключи (боевые)
STRIPE_SECRET_KEY=sk_live_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
STRIPE_PUBLISHABLE_KEY=pk_live_51XyZabCdefGhIjKlMnOpQrStUvWxYzAbCdEfGhIj
STRIPE_WEBHOOK_SECRET=whsec_live_1234567890abcdefghijklmnopqrstuvwxyz

# Clerk API ключи (боевые)
CLERK_SECRET_KEY=sk_live_clerk1234567890abcdefghijklmnopqrstuvwxyz
CLERK_PUBLISHABLE_KEY=pk_live_clerk1234567890abcdefghijklmnopqrstuvwxyz
CLERK_WEBHOOK_SECRET=whsec_live_clerk1234567890abcdefghijklmnopqrstuvwxyz

# Stripe боевые product/price IDs
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_live_1234567890monthly
NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_live_1234567890annual

# URL-адреса для окружения
NEXT_PUBLIC_SITE_URL=https://quercalc.com
CORS_ORIGINS=https://quercalc.com

# Флаг для API (выключен в production)
ALLOW_DIRECT_USER_ID=false
```

### 1.3. Настройка загрузки секретов в systemd-сервисы

#### Модификация systemd-сервиса для frontend (Next.js)

Создаем/редактируем файл `/etc/systemd/system/quercalc-frontend.service`:

```ini
[Unit]
Description=Quer Calculator Frontend
After=network.target

[Service]
Type=simple
User=nodejs-app-user
WorkingDirectory=/opt/quercalc/frontend
EnvironmentFile=/etc/secrets/common/app.env
EnvironmentFile=/etc/secrets/%i/app.env
ExecStart=/usr/bin/node /opt/quercalc/frontend/.next/server/app.js
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### Модификация systemd-сервиса для backend (Rust)

Создаем/редактируем файл `/etc/systemd/system/quercalc-backend.service`:

```ini
[Unit]
Description=Quer Calculator Backend
After=network.target

[Service]
Type=simple
User=nodejs-app-user
WorkingDirectory=/opt/quercalc/backend
EnvironmentFile=/etc/secrets/common/app.env
EnvironmentFile=/etc/secrets/%i/app.env
ExecStart=/opt/quercalc/backend/target/release/quer-calc
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### Создание множества инстансов для разных окружений

Переименовываем файлы сервисов, чтобы использовать шаблоны systemd:

```bash
sudo mv /etc/systemd/system/quercalc-frontend.service /etc/systemd/system/quercalc-frontend@.service
sudo mv /etc/systemd/system/quercalc-backend.service /etc/systemd/system/quercalc-backend@.service
```

#### Запуск и включение сервисов для staging и production

```bash
# Перезагрузка конфигурации systemd
sudo systemctl daemon-reload

# Staging окружение
sudo systemctl enable --now <EMAIL>
sudo systemctl enable --now <EMAIL>

# Production окружение
sudo systemctl enable --now <EMAIL>
sudo systemctl enable --now <EMAIL>
```

### 1.4. Скрипт для безопасной загрузки секретов в процесс

Создаем файл `/opt/quercalc/scripts/load-secrets.sh`:

```bash
#!/bin/bash
# Скрипт для безопасной загрузки переменных окружения

ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment>"
    echo "Available environments: staging, production"
    exit 1
fi

# Загрузка общих переменных
if [ -f "/etc/secrets/common/app.env" ]; then
    source "/etc/secrets/common/app.env"
fi

# Загрузка переменных окружения
if [ -f "/etc/secrets/$ENVIRONMENT/app.env" ]; then
    source "/etc/secrets/$ENVIRONMENT/app.env"
else
    echo "Error: Environment file for $ENVIRONMENT not found"
    exit 1
fi

# Запуск предоставленной команды с загруженными переменными
exec "${@:2}"
```

```bash
# Устанавливаем правильные разрешения
sudo chmod 750 /opt/quercalc/scripts/load-secrets.sh
sudo chown root:secrets-access /opt/quercalc/scripts/load-secrets.sh
```

## 2. Настройка интеграций

### 2.1. Настройка Stripe

#### 2.1.1. Создание тестовых и боевых продуктов в Stripe

1. **Регистрация/авторизация в Stripe Dashboard**:
   * Перейдите на [dashboard.stripe.com](https://dashboard.stripe.com)
   * Создайте аккаунт или войдите в существующий

2. **Создание тестовых продуктов и цен** (в тестовом режиме):
   * Перейдите в раздел "Products" и нажмите "Add product"
   * Создайте два продукта:
     * "Monthly Subscription" с ценой $49/месяц (recurring)
     * "Annual Subscription" с ценой $348/год (recurring)
   * Сохраните ID продуктов и цен в `/etc/secrets/staging/app.env`

3. **Создание боевых продуктов и цен** (в боевом режиме):
   * Переключитесь в боевой режим (toggle в верхней части Dashboard)
   * Повторите шаги создания продуктов, как для тестового режима
   * Сохраните ID продуктов и цен в `/etc/secrets/production/app.env`

#### 2.1.2. Настройка веб-хуков Stripe

1. **Настройка тестовых вебхуков**:
   * В Stripe Dashboard перейдите в "Developers" -> "Webhooks"
   * Добавьте эндпоинт: `https://staging.quercalc.com/api/webhook/stripe`
   * Выберите события для отслеживания:
     * `checkout.session.completed`
     * `customer.subscription.updated`
     * `customer.subscription.deleted`
   * Запишите сгенерированный "Signing Secret" в `STRIPE_WEBHOOK_SECRET` в файле `/etc/secrets/staging/app.env`

2. **Настройка боевых вебхуков**:
   * Переключитесь в боевой режим и повторите шаги
   * Используйте эндпоинт: `https://quercalc.com/api/webhook/stripe`
   * Сохраните "Signing Secret" в `/etc/secrets/production/app.env`

### 2.2. Настройка Clerk

#### 2.2.1. Создание приложений в Clerk для разных окружений

1. **Вход в Clerk Dashboard**:
   * Перейдите на [dashboard.clerk.dev](https://dashboard.clerk.dev)
   * Авторизуйтесь или создайте аккаунт

2. **Создание тестового приложения**:
   * Нажмите "Add Application"
   * Назовите его "QuercCalc Staging"
   * Выберите "Web application"
   * Укажите домен: `staging.quercalc.com`
   * Настройте редиректы:
     * Sign-in URL: `https://staging.quercalc.com/sign-in`
     * Sign-up URL: `https://staging.quercalc.com/sign-up`
     * After Sign-in URL: `https://staging.quercalc.com/calculator`
   * Сохраните API ключи в `/etc/secrets/staging/app.env`

3. **Создание продакшн приложения**:
   * Повторите шаги, но с названием "QuercCalc Production"
   * Используйте домен: `quercalc.com`
   * Сохраните API ключи в `/etc/secrets/production/app.env`

#### 2.2.2. Настройка веб-хуков Clerk

1. **Настройка вебхуков для тестового приложения**:
   * В Clerk Dashboard выберите приложение "QuercCalc Staging"
   * Перейдите в раздел "Webhooks"
   * Добавьте эндпоинт: `https://staging.quercalc.com/api/webhook/clerk`
   * Выберите события:
     * `user.created`
     * `user.deleted`
   * Сохраните "Signing Secret" в `CLERK_WEBHOOK_SECRET` в файле `/etc/secrets/staging/app.env`

2. **Настройка для продакшн приложения**:
   * Повторите шаги для "QuercCalc Production"
   * Используйте эндпоинт: `https://quercalc.com/api/webhook/clerk`
   * Сохраните секрет в `/etc/secrets/production/app.env`

### 2.3. Настройка дополнительных сервисов

#### 2.3.1. Email-уведомления (SendGrid)

1. **Создание аккаунта**:
   * Зарегистрируйтесь на [SendGrid](https://sendgrid.com)
   * Верифицируйте домен и создайте API-ключ

2. **Добавление секретов**:
   * Добавьте в файлы секретов:

```bash
# Добавление в /etc/secrets/staging/app.env
echo "SENDGRID_API_KEY=SG.staging_key_1234567890abcdef" | sudo tee -a /etc/secrets/staging/app.env
echo "SENDGRID_FROM_EMAIL=<EMAIL>" | sudo tee -a /etc/secrets/staging/app.env

# Добавление в /etc/secrets/production/app.env
echo "SENDGRID_API_KEY=SG.production_key_1234567890abcdef" | sudo tee -a /etc/secrets/production/app.env
echo "SENDGRID_FROM_EMAIL=<EMAIL>" | sudo tee -a /etc/secrets/production/app.env
```

#### 2.3.2. Аналитика (Google Analytics)

1. **Создание проектов в Google Analytics**:
   * Создайте отдельные проекты для staging и production

2. **Добавление ID в конфигурацию**:
   * Добавьте в файлы секретов:

```bash
# Добавление в /etc/secrets/staging/app.env
echo "NEXT_PUBLIC_GA_MEASUREMENT_ID=G-STAG1234567" | sudo tee -a /etc/secrets/staging/app.env

# Добавление в /etc/secrets/production/app.env
echo "NEXT_PUBLIC_GA_MEASUREMENT_ID=G-PROD1234567" | sudo tee -a /etc/secrets/production/app.env
```

## 3. Аудит безопасности

### 3.1. Проверка файловых разрешений для секретов

Создаем скрипт аудита `/opt/quercalc/scripts/security-audit.sh`:

```bash
#!/bin/bash
# Скрипт для проверки безопасности секретов

echo "===== Проверка файловых разрешений для /etc/secrets ====="

# Проверка разрешений основной директории
echo "Основная директория:"
ls -ld /etc/secrets

# Проверка поддиректорий
echo "Поддиректории:"
ls -ld /etc/secrets/common /etc/secrets/staging /etc/secrets/production

# Проверка файлов секретов
echo "Файлы секретов:"
ls -la /etc/secrets/common/
ls -la /etc/secrets/staging/
ls -la /etc/secrets/production/

# Проверка пользователей с доступом
echo "Пользователи с доступом к группе secrets-access:"
grep secrets-access /etc/group

# Проверка очистки секретов в логах
echo "Проверка логов на предмет утечки секретов:"
cd /var/log
grep -r "SECRET_KEY\|API_KEY" --include="*.log" .

echo "===== Завершение проверки ====="
```

```bash
# Устанавливаем права на скрипт
sudo chmod 700 /opt/quercalc/scripts/security-audit.sh
sudo chown root:root /opt/quercalc/scripts/security-audit.sh
```

### 3.2. Настройка механизма ротации секретов

#### 3.2.1. Создание скрипта для ротации секретов

Создаем файл `/opt/quercalc/scripts/rotate-secrets.sh`:

```bash
#!/bin/bash
# Скрипт для ротации секретов

ENVIRONMENT=$1
SECRET_TYPE=$2

if [ -z "$ENVIRONMENT" ] || [ -z "$SECRET_TYPE" ]; then
    echo "Usage: $0 <environment> <secret_type>"
    echo "Available environments: staging, production"
    echo "Available secret types: stripe, clerk, sendgrid"
    exit 1
fi

# Проверка существования директории секретов
if [ ! -d "/etc/secrets/$ENVIRONMENT" ]; then
    echo "Error: Environment directory not found"
    exit 1
fi

# Резервное копирование текущих секретов
BACKUP_DIR="/etc/secrets/backups"
TIMESTAMP=$(date +%Y%m%d%H%M%S)
sudo mkdir -p "$BACKUP_DIR"
sudo cp "/etc/secrets/$ENVIRONMENT/app.env" "$BACKUP_DIR/app.env.$ENVIRONMENT.$TIMESTAMP"

# Функция для редактирования переменных окружения
update_env_var() {
    local file=$1
    local key=$2
    local value=$3
    
    if grep -q "^$key=" "$file"; then
        sudo sed -i "s|^$key=.*|$key=$value|" "$file"
    else
        echo "$key=$value" | sudo tee -a "$file" > /dev/null
    fi
}

# Обновление секретов в зависимости от типа
case "$SECRET_TYPE" in
    stripe)
        echo "Rotating Stripe API keys for $ENVIRONMENT..."
        read -p "Enter new Stripe Secret Key: " STRIPE_SECRET_KEY
        read -p "Enter new Stripe Publishable Key: " STRIPE_PUBLISHABLE_KEY
        read -p "Enter new Stripe Webhook Secret: " STRIPE_WEBHOOK_SECRET
        
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "STRIPE_SECRET_KEY" "$STRIPE_SECRET_KEY"
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "STRIPE_PUBLISHABLE_KEY" "$STRIPE_PUBLISHABLE_KEY"
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "STRIPE_WEBHOOK_SECRET" "$STRIPE_WEBHOOK_SECRET"
        ;;
        
    clerk)
        echo "Rotating Clerk API keys for $ENVIRONMENT..."
        read -p "Enter new Clerk Secret Key: " CLERK_SECRET_KEY
        read -p "Enter new Clerk Publishable Key: " CLERK_PUBLISHABLE_KEY
        read -p "Enter new Clerk Webhook Secret: " CLERK_WEBHOOK_SECRET
        
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "CLERK_SECRET_KEY" "$CLERK_SECRET_KEY"
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "CLERK_PUBLISHABLE_KEY" "$CLERK_PUBLISHABLE_KEY"
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "CLERK_WEBHOOK_SECRET" "$CLERK_WEBHOOK_SECRET"
        ;;
        
    sendgrid)
        echo "Rotating SendGrid API key for $ENVIRONMENT..."
        read -p "Enter new SendGrid API Key: " SENDGRID_API_KEY
        
        update_env_var "/etc/secrets/$ENVIRONMENT/app.env" "SENDGRID_API_KEY" "$SENDGRID_API_KEY"
        ;;
        
    *)
        echo "Error: Unknown secret type"
        exit 1
        ;;
esac

echo "Secret rotation completed. Restarting services..."

# Перезапуск связанных сервисов
sudo systemctl restart quercalc-frontend@$ENVIRONMENT.service
sudo systemctl restart quercalc-backend@$ENVIRONMENT.service

echo "Services restarted successfully!"
```

```bash
# Устанавливаем правильные разрешения
sudo chmod 700 /opt/quercalc/scripts/rotate-secrets.sh
sudo chown root:root /opt/quercalc/scripts/rotate-secrets.sh
```

#### 3.2.2. Документация по ротации секретов

**Зачем нужна ротация секретов:**

1. **Минимизация возможного ущерба:** Даже если секреты были скомпрометированы, их регулярная замена ограничивает время, в течение которого они могут быть использованы злоумышленниками.

2. **Соответствие требованиям безопасности:** Многие стандарты безопасности (например, PCI DSS) требуют регулярной ротации секретов.

3. **Предотвращение неавторизованного доступа:** Бывшие сотрудники и подрядчики, которые имели доступ к секретам, не смогут использовать их после ротации.

4. **Повышение защиты от утечек:** При долговременном использовании одних и тех же секретов повышается риск их случайного раскрытия.

**Рекомендации по ротации секретов:**

- **API ключи (Stripe, Clerk):** Ротация каждые 90 дней
- **Webhook секреты:** Ротация каждые 180 дней
- **Сессионные ключи:** Ротация каждые 30 дней
- **Ключи шифрования базы данных:** Ротация каждые 365 дней

### 3.3. Настройка мониторинга доступа к секретам

#### 3.3.1. Установка auditd для мониторинга доступа к файлам

```bash
# Установка auditd
sudo apt-get update
sudo apt-get install -y auditd

# Настройка правил аудита для директории с секретами
sudo bash -c 'cat > /etc/audit/rules.d/secrets.rules << EOF
# Мониторинг доступа к директории с секретами
-w /etc/secrets/ -p rwxa -k secrets_access
EOF'

# Перезапуск службы
sudo systemctl restart auditd
```

#### 3.3.2. Создание скрипта для проверки журналов доступа

Создаем файл `/opt/quercalc/scripts/check-secrets-access.sh`:

```bash
#!/bin/bash
# Скрипт для проверки доступа к секретам

echo "===== Проверка доступа к секретам ====="

# Проверка журналов auditd
echo "Последние записи доступа к секретам:"
sudo ausearch -k secrets_access -ts today

# Проверка неавторизованного доступа (пользователи не из группы secrets-access)
echo "Проверка на неавторизованный доступ:"
AUTHORIZED_USERS=$(grep secrets-access /etc/group | cut -d: -f4 | tr ',' ' ')
AUTHORIZED_USERS="$AUTHORIZED_USERS root"

sudo ausearch -k secrets_access -ts today | grep -i "auid" | while read line; do
    USER=$(echo $line | grep -o "auid=[0-9]*" | cut -d= -f2)
    USERNAME=$(getent passwd $USER | cut -d: -f1)
    
    AUTHORIZED=0
    for AUTH_USER in $AUTHORIZED_USERS; do
        if [ "$USERNAME" = "$AUTH_USER" ]; then
            AUTHORIZED=1
            break
        fi
    done
    
    if [ $AUTHORIZED -eq 0 ]; then
        echo "ПРЕДУПРЕЖДЕНИЕ: Обнаружен неавторизованный доступ от пользователя $USERNAME ($USER)"
    fi
done

echo "===== Завершение проверки ====="
```

```bash
# Устанавливаем правильные разрешения
sudo chmod 700 /opt/quercalc/scripts/check-secrets-access.sh
sudo chown root:root /opt/quercalc/scripts/check-secrets-access.sh
```

#### 3.3.3. Настройка ежедневной проверки с уведомлениями

Создаем cron-задачу для ежедневной проверки:

```bash
# Добавление cron-задачи для root
sudo bash -c 'cat > /etc/cron.d/secrets-monitor << EOF
# Ежедневная проверка доступа к секретам
0 2 * * * root /opt/quercalc/scripts/check-secrets-access.sh | mail -s "QuercCalc Secrets Access Report" <EMAIL>
EOF'
```

## 4. Документация и обучение команды

### 4.1. Создание руководства для разработчиков

Создаем файл `/opt/quercalc/docs/secrets-management.md`:

```markdown
# Руководство по управлению секретами QuercCalc

## Общие принципы

1. **Принцип наименьших привилегий:** Используйте только те секреты, которые необходимы для вашей задачи.
2. **Изоляция сред:** Никогда не смешивайте секреты из разных окружений (staging/production).
3. **No hardcoding:** Никогда не включайте секреты в код напрямую.
4. **Журналирование:** Не записывайте секреты в журналы.

## Доступ к секретам

### Локальная разработка

Для локальной разработки используйте файл `.env.local`:

```bash
# Копирование шаблона env-файла
cp .env.example .env.local

# Заполнение необходимыми значениями
# ВНИМАНИЕ: Используйте только тестовые API-ключи для локальной разработки!
```

### Staging и Production

Секреты для staging и production хранятся в `/etc/secrets/{staging,production}/app.env`.
Изменения в эти файлы должны вноситься только через процедуру ротации секретов.

## Процедуры

### Добавление нового секрета

1. Обратитесь к администратору с запросом на добавление секрета
2. Предоставьте:
   - Название переменной
   - Значение для каждого окружения
   - Обоснование необходимости

### Ротация секретов

Регулярная ротация секретов выполняется администратором системы.
Если вы заметили потенциальную компрометацию, немедленно сообщите об этом.

## Интеграция в приложение

### Next.js Frontend

Секреты доступны как переменные окружения:

```javascript
// Пример использования
const stripeKey = process.env.STRIPE_PUBLISHABLE_KEY;
```

### Rust Backend

Секреты загружаются через переменные окружения:

```rust
// Пример получения переменной окружения
let stripe_key = std::env::var("STRIPE_SECRET_KEY").expect("Missing STRIPE_SECRET_KEY");
```
```

### 4.2. Настройка автоматизированной проверки конфигурации

Создаем скрипт `/opt/quercalc/scripts/check-config.sh`:

```bash
#!/bin/bash
# Скрипт для проверки конфигурации приложения

ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment>"
    echo "Available environments: staging, production"
    exit 1
fi

echo "===== Проверка конфигурации для $ENVIRONMENT ====="

# Загрузка переменных окружения
source "/etc/secrets/common/app.env" 2>/dev/null
source "/etc/secrets/$ENVIRONMENT/app.env" 2>/dev/null

# Проверка обязательных переменных
REQUIRED_VARS=(
    "STRIPE_SECRET_KEY"
    "STRIPE_PUBLISHABLE_KEY"
    "STRIPE_WEBHOOK_SECRET"
    "CLERK_SECRET_KEY"
    "CLERK_PUBLISHABLE_KEY"
    "NEXT_PUBLIC_SITE_URL"
    "CORS_ORIGINS"
)

MISSING_VARS=0
for VAR in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!VAR}" ]; then
        echo "ОШИБКА: Отсутствует обязательная переменная: $VAR"
        MISSING_VARS=$((MISSING_VARS+1))
    fi
done

if [ $MISSING_VARS -gt 0 ]; then
    echo "Найдено $MISSING_VARS отсутствующих переменных!"
else
    echo "Все обязательные переменные присутствуют."
fi

# Проверка формата ключей
if [[ ! "$STRIPE_SECRET_KEY" =~ ^sk_(test|live)_ ]]; then
    echo "ПРЕДУПРЕЖДЕНИЕ: Формат STRIPE_SECRET_KEY не соответствует ожидаемому паттерну"
fi

if [[ ! "$STRIPE_PUBLISHABLE_KEY" =~ ^pk_(test|live)_ ]]; then
    echo "ПРЕДУПРЕЖДЕНИЕ: Формат STRIPE_PUBLISHABLE_KEY не соответствует ожидаемому паттерну"
fi

if [[ ! "$CLERK_SECRET_KEY" =~ ^sk_(test|live)_ ]]; then
    echo "ПРЕДУПРЕЖДЕНИЕ: Формат CLERK_SECRET_KEY не соответствует ожидаемому паттерну"
fi

# Проверка соответствия ключей окружению
if [ "$ENVIRONMENT" = "production" ]; then
    if [[ "$STRIPE_SECRET_KEY" =~ ^sk_test_ ]]; then
        echo "КРИТИЧЕСКАЯ ОШИБКА: В production используется тестовый ключ Stripe!"
    fi
    if [[ "$CLERK_SECRET_KEY" =~ ^sk_test_ ]]; then
        echo "КРИТИЧЕСКАЯ ОШИБКА: В production используется тестовый ключ Clerk!"
    fi
elif [ "$ENVIRONMENT" = "staging" ]; then
    if [[ "$STRIPE_SECRET_KEY" =~ ^sk_live_ ]]; then
        echo "ПРЕДУПРЕЖДЕНИЕ: В staging используется боевой ключ Stripe!"
    fi
    if [[ "$CLERK_SECRET_KEY" =~ ^sk_live_ ]]; then
        echo "ПРЕДУПРЕЖДЕНИЕ: В staging используется боевой ключ Clerk!"
    fi
fi

echo "===== Завершение проверки ====="
```

```bash
# Устанавливаем правильные разрешения
sudo chmod 700 /opt/quercalc/scripts/check-config.sh
sudo chown root:root /opt/quercalc/scripts/check-config.sh

# Добавляем в cron для регулярных проверок
sudo bash -c 'cat > /etc/cron.d/config-check << EOF
# Ежедневная проверка конфигурации
0 3 * * * root /opt/quercalc/scripts/check-config.sh staging | mail -s "QuercCalc Staging Config Check" <EMAIL>
30 3 * * * root /opt/quercalc/scripts/check-config.sh production | mail -s "QuercCalc Production Config Check" <EMAIL>
EOF'
```

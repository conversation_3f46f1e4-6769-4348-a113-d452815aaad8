# Подробный план настройки серверной инфраструктуры

В этом документе представлен детальный план настройки серверной инфраструктуры для проекта quer-calc, включая настройку отдельных окружений для staging (s.quer.us) и production (quer.us).

## 1. Подготовка базовой структуры сервера

### 1.1 Подключение к серверу
```bash
# Подключение по SSH (как x0 - пользователь с правами sudo)
ssh quer-x0
```

### 1.2 Обновление системы и установка необходимых пакетов
```bash
# Обновление списка пакетов и установка обновлений
sudo apt update && sudo apt upgrade -y

# Установка базовых зависимостей
sudo apt install -y build-essential pkg-config libssl-dev nodejs npm
```

### 1.3 Установка Node.js нужной версии
```bash
# Проверка текущей версии Node.js
node --version

# Если требуется более новая версия (например, для Next.js), устанавливаем её через nodesource
sudo apt remove -y nodejs npm  # удаляем старую версию, если есть
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Проверка установленной версии
node --version
npm --version

# Установка Yarn (опционально)
sudo npm install -g yarn
```

### 1.4 Создание структуры директорий для проекта
```bash
# Создание основных директорий для приложений
sudo mkdir -p /var/www/{quer.us,s.quer.us}/{current,releases,shared}

# Создание поддиректорий для хранения общих ресурсов
sudo mkdir -p /var/www/{quer.us,s.quer.us}/shared/{logs,public,uploads,.env}

# Назначение прав пользователю duploy
sudo chown -R duploy:duploy /var/www/quer.us /var/www/s.quer.us

# Установка правильных разрешений
sudo chmod -R 755 /var/www
```

## 2. Настройка Rust для бэкенда

### 2.1 Установка Rust
```bash
# Переключение на пользователя duploy для установки Rust
sudo su - duploy

# Установка Rust через rustup
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
# Выбираем опцию 1) Proceed with installation (default)

# Настройка переменных окружения в текущей сессии
source $HOME/.cargo/env

# Проверка установки
rustc --version
cargo --version

# Выход из сессии пользователя duploy
exit
```

### 2.2 Создание systemd-сервисов для бэкенда Rust

#### 2.2.1 Создание сервиса для staging

```bash
# Создание файла сервиса systemd для staging
sudo nano /etc/systemd/system/quer-backend-staging.service
```

Содержимое файла:
```ini
[Unit]
Description=Quer Calculator Staging Backend
After=network.target

[Service]
User=duploy
Group=duploy
WorkingDirectory=/var/www/s.quer.us/current/backend-rust
ExecStart=/var/www/s.quer.us/current/backend-rust/target/release/quer-calc
Restart=always
RestartSec=5
Environment="RUST_ENV=staging"
Environment="CORS_ORIGINS=https://s.quer.us"
Environment="RATE_LIMIT_BURST=100"
Environment="RATE_LIMIT_PER_SEC=50"

# Загрузка переменных окружения из файла
EnvironmentFile=-/var/www/s.quer.us/shared/.env/backend.env

# Ограничения безопасности
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
RestrictSUIDSGID=true

[Install]
WantedBy=multi-user.target
```

#### 2.2.2 Создание сервиса для production

```bash
# Создание файла сервиса systemd для production
sudo nano /etc/systemd/system/quer-backend-production.service
```

Содержимое файла:
```ini
[Unit]
Description=Quer Calculator Production Backend
After=network.target

[Service]
User=duploy
Group=duploy
WorkingDirectory=/var/www/quer.us/current/backend-rust
ExecStart=/var/www/quer.us/current/backend-rust/target/release/quer-calc
Restart=always
RestartSec=5
Environment="RUST_ENV=production"
Environment="CORS_ORIGINS=https://quer.us"
Environment="RATE_LIMIT_BURST=20"
Environment="RATE_LIMIT_PER_SEC=10"

# Загрузка переменных окружения из файла
EnvironmentFile=-/var/www/quer.us/shared/.env/backend.env

# Ограничения безопасности
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
RestrictSUIDSGID=true

[Install]
WantedBy=multi-user.target
```

#### 2.2.3 Создание директорий для переменных окружения

```bash
# Создание директорий для .env файлов
sudo mkdir -p /var/www/{quer.us,s.quer.us}/shared/.env
sudo chown duploy:duploy /var/www/{quer.us,s.quer.us}/shared/.env
sudo chmod 750 /var/www/{quer.us,s.quer.us}/shared/.env
```

## 3. Настройка Next.js для фронтенда

### 3.1 Настройка systemd-сервисов для Next.js

#### 3.1.1 Создание сервиса для staging

```bash
# Создание файла сервиса systemd для staging
sudo nano /etc/systemd/system/quer-frontend-staging.service
```

Содержимое файла:
```ini
[Unit]
Description=Quer Calculator Staging Frontend
After=network.target

[Service]
User=duploy
Group=duploy
WorkingDirectory=/var/www/s.quer.us/current
ExecStart=/usr/bin/node /var/www/s.quer.us/current/next.config.server.js
Restart=always
RestartSec=5
Environment="NODE_ENV=production"
Environment="PORT=3000"
Environment="NEXT_PUBLIC_API_URL=https://s.quer.us/api"
Environment="NEXT_PUBLIC_SITE_URL=https://s.quer.us"

# Загрузка переменных окружения из файла
EnvironmentFile=-/var/www/s.quer.us/shared/.env/frontend.env

# Ограничения безопасности
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
RestrictSUIDSGID=true

[Install]
WantedBy=multi-user.target
```

#### 3.1.2 Создание сервиса для production

```bash
# Создание файла сервиса systemd для production
sudo nano /etc/systemd/system/quer-frontend-production.service
```

Содержимое файла:
```ini
[Unit]
Description=Quer Calculator Production Frontend
After=network.target

[Service]
User=duploy
Group=duploy
WorkingDirectory=/var/www/quer.us/current
ExecStart=/usr/bin/node /var/www/quer.us/current/next.config.server.js
Restart=always
RestartSec=5
Environment="NODE_ENV=production"
Environment="PORT=3100"
Environment="NEXT_PUBLIC_API_URL=https://quer.us/api"
Environment="NEXT_PUBLIC_SITE_URL=https://quer.us"

# Загрузка переменных окружения из файла
EnvironmentFile=-/var/www/quer.us/shared/.env/frontend.env

# Ограничения безопасности
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=full
ProtectHome=true
RestrictSUIDSGID=true

[Install]
WantedBy=multi-user.target
```

### 3.2 Создание скрипта для запуска Next.js сервера

```bash
# Для staging
sudo su - duploy
mkdir -p /var/www/s.quer.us/current
nano /var/www/s.quer.us/current/next.config.server.js
```

Содержимое файла для staging:
```javascript
// next.config.server.js
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

const app = next({ dir: '.', dev: false })
const handle = app.getRequestHandler()

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true)
    handle(req, res, parsedUrl)
  }).listen(process.env.PORT || 3000, (err) => {
    if (err) throw err
    console.log(`> Ready on http://localhost:${process.env.PORT || 3000}`)
  })
})
```

```bash
# Повторяем для production
mkdir -p /var/www/quer.us/current
nano /var/www/quer.us/current/next.config.server.js
```

Содержимое файла для production (идентично staging, просто скопируйте):
```javascript
// next.config.server.js
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

const app = next({ dir: '.', dev: false })
const handle = app.getRequestHandler()

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true)
    handle(req, res, parsedUrl)
  }).listen(process.env.PORT || 3100, (err) => {
    if (err) throw err
    console.log(`> Ready on http://localhost:${process.env.PORT || 3100}`)
  })
})
```

```bash
# Выход из сессии пользователя duploy
exit
```

### 3.3 Создание файлов окружения для Next.js

```bash
# Переключаемся на пользователя duploy для создания .env файлов
sudo su - duploy

# Создание .env файла для staging
mkdir -p /var/www/s.quer.us/shared/.env
nano /var/www/s.quer.us/shared/.env/frontend.env
```

Содержимое файла frontend.env для staging:
```
# Настройки аутентификации
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
CLERK_SECRET_KEY=sk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
CLERK_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ

# Настройки оплаты
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
STRIPE_SECRET_KEY=sk_test_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
STRIPE_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
```

```bash
# Создание .env файла для production
mkdir -p /var/www/quer.us/shared/.env
nano /var/www/quer.us/shared/.env/frontend.env
```

Содержимое файла frontend.env для production:
```
# Настройки аутентификации
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
CLERK_SECRET_KEY=sk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
CLERK_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ

# Настройки оплаты
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
STRIPE_SECRET_KEY=sk_live_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
NEXT_PUBLIC_STRIPE_MONTHLY_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
NEXT_PUBLIC_STRIPE_ANNUAL_PRICE_ID=price_ЗАМЕНИТЕ_НА_СВОЙ_ID
STRIPE_WEBHOOK_SECRET=whsec_ЗАМЕНИТЕ_НА_СВОЙ_КЛЮЧ
```

```bash
# Создание .env файлов для бэкенда Rust
nano /var/www/s.quer.us/shared/.env/backend.env
```

Содержимое файла backend.env для staging:
```
# Настройки CORS и безопасности
CORS_ORIGINS=https://s.quer.us,http://localhost:3000,https://localhost:3001
RATE_LIMIT_BURST=100
RATE_LIMIT_PER_SEC=50
```

```bash
# Создание .env файла для production бэкенда
nano /var/www/quer.us/shared/.env/backend.env
```

Содержимое файла backend.env для production:
```
# Настройки CORS и безопасности
CORS_ORIGINS=https://quer.us
RATE_LIMIT_BURST=150
RATE_LIMIT_PER_SEC=75
```

```bash
# Выход из сессии пользователя duploy
exit
```

### 3.4 Создание директорий для логов

```bash
# Создание директорий для логов
sudo mkdir -p /var/www/{quer.us,s.quer.us}/shared/logs/{frontend,backend}
sudo chown -R duploy:duploy /var/www/{quer.us,s.quer.us}/shared/logs
```

## 4. Настройка Nginx для проксирования запросов

### 4.1 Установка и базовая настройка Nginx

```bash
# Установка Nginx
sudo apt install -y nginx

# Создание директории для кастомных конфигураций
sudo mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
```

### 4.2 Создание конфигурационных файлов для проектов

#### 4.2.1 Создание конфигурации для staging

```bash
# Создание конфигурации Nginx для s.quer.us
sudo nano /etc/nginx/sites-available/s.quer.us
```

Содержимое файла:
```nginx
# Базовая конфигурация для staging
server {
    listen 80;
    server_name s.quer.us;
    
    # Редирект на HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name s.quer.us;
    
    # SSL настройки (будут добавлены после настройки Cloudflare)
    ssl_certificate /etc/ssl/certs/cloudflare-s.quer.us.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-s.quer.us.key;
    
    # Логирование
    access_log /var/www/s.quer.us/shared/logs/nginx-access.log;
    error_log /var/www/s.quer.us/shared/logs/nginx-error.log;

    # Настройки безопасности
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';";
    
    # Проксирование запросов к API бэкенда
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Проксирование запросов к фронтенду Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Кэширование статических файлов
    location /_next/static/ {
        proxy_pass http://localhost:3000/_next/static/;
        proxy_cache_valid 60m;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
    
    location /static/ {
        proxy_pass http://localhost:3000/static/;
        proxy_cache_valid 60m;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}
```

#### 4.2.2 Создание конфигурации для production

```bash
# Создание конфигурации Nginx для quer.us
sudo nano /etc/nginx/sites-available/quer.us
```

Содержимое файла:
```nginx
# Базовая конфигурация для production
server {
    listen 80;
    server_name quer.us www.quer.us;
    
    # Редирект на HTTPS
    location / {
        return 301 https://quer.us$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name www.quer.us;
    
    # Редирект с www на без www
    return 301 https://quer.us$request_uri;
}

server {
    listen 443 ssl;
    server_name quer.us;
    
    # SSL настройки (будут добавлены после настройки Cloudflare)
    ssl_certificate /etc/ssl/certs/cloudflare-quer.us.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-quer.us.key;
    
    # Логирование
    access_log /var/www/quer.us/shared/logs/nginx-access.log;
    error_log /var/www/quer.us/shared/logs/nginx-error.log;

    # Настройки безопасности
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';";
    
    # Проксирование запросов к API бэкенда
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Проксирование запросов к фронтенду Next.js
    location / {
        proxy_pass http://localhost:3100;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Кэширование статических файлов
    location /_next/static/ {
        proxy_pass http://localhost:3100/_next/static/;
        proxy_cache_valid 60m;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
    
    location /static/ {
        proxy_pass http://localhost:3100/static/;
        proxy_cache_valid 60m;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}
```

### 4.3 Активация конфигураций и проверка синтаксиса

```bash
# Создание символических ссылок для активации конфигураций
sudo ln -s /etc/nginx/sites-available/s.quer.us /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/quer.us /etc/nginx/sites-enabled/

# Проверка синтаксиса конфигурационных файлов Nginx
sudo nginx -t

# Перезапуск Nginx для применения конфигурации
sudo systemctl restart nginx
```

## 5. Настройка разрешений и дополнительные файрвол-правила

### 5.1 Настройка разрешений для деплоя

```bash
# Обеспечение правильных прав для развертывания
sudo chown -R duploy:duploy /var/www/quer.us /var/www/s.quer.us

# Обеспечение необходимых разрешений для Nginx логов
sudo chmod -R 755 /var/www/quer.us/shared/logs /var/www/s.quer.us/shared/logs
```

### 5.2 Обновление правил файрвола

```bash
# Проверка текущих правил
sudo ufw status

# Обеспечение доступа к Nginx (если правила уже не активированы)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Применение изменений
sudo ufw reload
```

## 6. Подготовка временных директорий для деплоя

```bash
# Переход на пользователя duploy для создания структуры
sudo su - duploy

# Создание простых placeholder файлов для тестирования
mkdir -p /var/www/s.quer.us/current/public
echo "Staging environment" > /var/www/s.quer.us/current/public/index.html

mkdir -p /var/www/quer.us/current/public
echo "Production environment" > /var/www/quer.us/current/public/index.html

exit
```

## 7. Настройка базовых инструментов мониторинга

### 7.1 Установка инструментов мониторинга

```bash
# Установка базовых инструментов мониторинга
sudo apt install -y htop iotop iftop

# Установка netdata для базового мониторинга системы
sudo apt install -y netdata
```

### 7.2 Настройка netdata для локального доступа

```bash
# Редактирование конфигурации netdata
sudo nano /etc/netdata/netdata.conf
```

Настройте доступ только с localhost:
```
[web]
allow connections from = localhost 127.0.0.1 ::1
```

```bash
# Перезапуск netdata
sudo systemctl restart netdata
```

## 8. Первоначальная проверка установки

### 8.1 Активация сервисов

```bash
# Перезагрузка конфигурации systemd
sudo systemctl daemon-reload

# Проверка и запуск сервисов (если все файлы на месте)
sudo systemctl start quer-backend-staging.service
sudo systemctl start quer-frontend-staging.service

# Включение автозапуска сервисов при загрузке системы
sudo systemctl enable quer-backend-staging.service
sudo systemctl enable quer-frontend-staging.service
sudo systemctl enable quer-backend-production.service
sudo systemctl enable quer-frontend-production.service

# Проверка статуса сервисов
sudo systemctl status quer-backend-staging.service
sudo systemctl status quer-frontend-staging.service
```

### 8.2 Проверка конфигурации Nginx

```bash
# Проверка синтаксиса Nginx
sudo nginx -t

# Проверка статуса Nginx
sudo systemctl status nginx
```

## 9. Настройка логирования и ротации логов

### 9.1 Настройка логирования для приложений

```bash
# Создание конфигурации logrotate для логов приложения
sudo nano /etc/logrotate.d/quer-apps
```

Содержимое файла:
```
/var/www/*/shared/logs/*/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 duploy duploy
    sharedscripts
    postrotate
        [ -s /run/nginx.pid ] && kill -USR1 `cat /run/nginx.pid`
    endscript
}
```

### 9.2 Настройка logwatch для мониторинга логов

```bash
# Установка logwatch
sudo apt install -y logwatch

# Настройка ежедневных отчетов
sudo nano /etc/cron.daily/00logwatch
```

## 10. Заключительные шаги

### 10.1 Перезагрузка сервера для проверки автозапуска

```bash
# Перезагрузка сервера
sudo reboot
```

### 10.2 Проверка после перезагрузки

```bash
# Подключение по SSH после перезагрузки
ssh quer-x0

# Проверка статуса сервисов
sudo systemctl status nginx
sudo systemctl status quer-backend-staging.service
sudo systemctl status quer-frontend-staging.service
```

## Заключение

После выполнения всех шагов вы будете иметь полностью настроенную серверную инфраструктуру для проекта quer-calc с отдельными окружениями для staging и production. Nginx будет настроен для проксирования запросов к соответствующим приложениям, а systemd будет управлять запуском и перезапуском сервисов.

Дальнейшие шаги:
- Настройка SSL с использованием Cloudflare
- Настройка процесса деплоя
- Детальная настройка мониторинга и оповещений
- Создание резервного копирования

Эта конфигурация обеспечивает надежную основу для вашего приложения и подготавливает сервер к приему реальных запросов и деплою кода.

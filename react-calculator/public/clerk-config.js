// clerk-config.js - GENERATED FROM CONFIG
// Environment: production, Target: blue
// Generated on: Sun Jun 22 13:47:26 CDT 2025

window.CLERK_CONFIG = {
    publishableKey: "pk_live_Y2xlcmsucXVlci51cyQ",
    
    // Configuration metadata
    configVersion: "7.0-CONFIG-GENERATED",
    configTimestamp: new Date().toISOString(),
    architecture: "CONFIG GENERATED: приложение использует централизованную конфигурацию",
    environment: "production",
    target: "blue",
    
    // Base URLs for components
    signInUrl: "/sign-in",
    signUpUrl: "/sign-up",
    
    // Fallback redirects for Email/Password authentication
    signInFallbackRedirectUrl: "https://quer.us/terminal/",
    signUpFallbackRedirectUrl: "https://quer.us/terminal/",
    
    // Core configuration
    unstable_core: {
        continueSessionOnRedirect: true,
        globalRedirect: false,
        __internal_navigateWithError: false
    },
    
    // Dynamic OAuth redirect origins based on environment configuration
    allowedRedirectOrigins: (function() {
        // Environment-specific origins from config
        const configOrigins = [
            "https://quer.us",
            "https://quer.us/terminal",
            "https://quer.us/terminal/",
            "https://clerk.quer.us",
        ];
        
        // Add terminal-specific paths to each origin
        const expandedOrigins = [];
        configOrigins.forEach(origin => {
            const baseOrigin = origin.replace(/\/terminal.*$/, '');
            expandedOrigins.push(
                baseOrigin + '/terminal',
                baseOrigin + '/terminal/',
                baseOrigin + '/terminal/oauth-success.html',
                baseOrigin + '/terminal/oauth-callback.html'
            );
        });
        
        // Also include the original clerk domain
        expandedOrigins.push("clerk.quer.us");
        
        // Remove duplicates
        return [...new Set(expandedOrigins)];
    })()
};

console.log('🔒 CONFIG GENERATED v7.0: Environment: production, Target: blue', window.CLERK_CONFIG);

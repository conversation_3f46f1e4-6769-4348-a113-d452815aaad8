import React, { useState } from 'react';
import { ClerkProvider } from '@clerk/clerk-react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Calculator from './features/calculator/Calculator';
import SimpleSignInPage from './pages/SimpleSignInPage';

import NavigationBar from './components/base/NavigationBar';
import AuthContextProvider, { useAuth } from './contexts/AuthContext';
import { ConfigProvider } from './contexts/ConfigContext';
import { CopyProvider } from './contexts/CopyContext';
import { dark } from '@clerk/themes';
import './App.css';

// НОВЫЙ, ИСПРАВЛЕННЫЙ ProtectedRoute
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  console.log('[ProtectedRoute] Auth state:', { isAuthenticated, isLoading });

  if (isLoading) {
    console.log('[ProtectedRoute] Показываем загрузку - состояние загружается');
    return (
      <div className="flex items-center justify-center min-h-screen bg-slate-950">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  // Если пользователь не авторизован, показываем страницу входа ПРЯМО ЗДЕСЬ,
  // а не делаем редирект.
  if (!isAuthenticated) {
    console.log('[ProtectedRoute] Пользователь не аутентифицирован, показываем страницу входа');
    return <SimpleSignInPage />;
  }

  console.log('[ProtectedRoute] Пользователь аутентифицирован, показываем защищенный контент');
  return children;
};

// Функция для получения переменной окружения с fallback
const getEnvVariable = (key, fallback = null) => {
  const value = process.env[key];
  console.log(`[App.jsx] Environment variable ${key}:`, value || 'not set');
  return value || fallback;
};

function App() {
  const [calculatorType, setCalculatorType] = useState('Crypto');
  const isElectron = (typeof window !== 'undefined') && window.electronAPI && window.electronAPI.isElectron;
  
  console.log('[App] Application starting...');
  console.log('[App] Environment check - isElectron:', isElectron);
  console.log('[App] window.electronAPI available:', !!(typeof window !== 'undefined' && window.electronAPI));

  // Получаем конфигурацию Clerk
  const getClerkConfig = () => {
    // Проверяем наличие глобальной конфигурации
    if (typeof window !== 'undefined' && window.CLERK_CONFIG) {
      console.log('[App] Using global Clerk config from window.CLERK_CONFIG');
      return window.CLERK_CONFIG;
    }

    console.log('[App] Building Clerk config from environment variables');
    
    // ИСПРАВЛЕНИЕ: Force redirect URLs убраны полностью - используем только fallback для email/password
    const config = {
      publishableKey: getEnvVariable('REACT_APP_CLERK_PUBLISHABLE_KEY'),
      appearance: {
        baseTheme: dark,
        variables: {
          colorBackground: '#212126',
          colorNeutral: 'white',
          colorPrimary: '#ffffff',
          colorTextOnPrimaryBackground: 'black',
          colorText: 'white',
          colorInputText: 'white',
          colorInputBackground: '#26262B'
        },
        elements: {
          providerIcon__apple: {
            filter: 'invert(1)'
          },
          providerIcon__github: {
            filter: 'invert(1)'
          },
          providerIcon__okx_wallet: {
            filter: 'invert(1)'
          },
          activeDeviceIcon: {
            '--cl-chassis-bottom': '#d2d2d2',
            '--cl-chassis-back': '#e6e6e6',
            '--cl-chassis-screen': '#e6e6e6',
            '--cl-screen': '#111111'
          }
        }
      },
      routing: 'path',
      signInUrl: getEnvVariable('REACT_APP_CLERK_SIGN_IN_URL', '/sign-in'),
      signUpUrl: getEnvVariable('REACT_APP_CLERK_SIGN_UP_URL', '/sign-up'),

      // 🔥 СОВРЕМЕННЫЕ CLERK REDIRECTS (без принудительных Force URLs!)
      // FORCE URLs убраны - они применялись ко всем типам авторизации
      // OAuth redirects настраиваются в момент вызова authenticateWithRedirect

      // EMAIL/PASSWORD остается в SPA (использует fallback URLs)
      signInFallbackRedirectUrl: getEnvVariable('REACT_APP_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL', '/'),
      signUpFallbackRedirectUrl: getEnvVariable('REACT_APP_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL', '/'),
      
      afterSignOutUrl: getEnvVariable('REACT_APP_CLERK_AFTER_SIGN_OUT_URL', '/sign-in'),

      // КРИТИЧНО: Настройки для SPA режима
      unstable_core: {
        continueSessionOnRedirect: true, // Сохраняем сессию после redirect
        globalRedirect: false           // Не делаем автоматических redirect
      }
    };

    // Дополнительные настройки для Electron
    if (isElectron) {
      console.log("[App] Running in Electron environment");
      
      // ИСПРАВЛЕНО: Динамические домены для OAuth редиректов с /terminal/
      const baseOrigin = window.location.origin;
      config.allowedRedirectOrigins = [
        `${baseOrigin}/terminal`,
        `${baseOrigin}/terminal/`,
        "https://fit-lizard-24.clerk.accounts.dev",
        "https://accounts.google.com"
      ];
    } else {
      console.log("[App] Running in browser environment");
    }

    return config;
  };

  const clerkConfig = getClerkConfig();
  
  console.log('[App.jsx] 🔒 ВЕРСИЯ 6.1: ВЕРСИОНИРОВАННАЯ КОНФИГУРАЦИЯ - безопасно для существующих пользователей!', clerkConfig);
  console.log('[App.jsx] 🚀 OAuth → FULL URL signInForceRedirectUrl config JSON:', JSON.stringify(clerkConfig, null, 2));
  
  // 🔥 ПРОВЕРКА ВЕРСИИ КОНФИГУРАЦИИ
  console.log('[App.jsx] 🔥 CONFIG VERSION CHECK:', {
    version: clerkConfig.configVersion || 'UNKNOWN',
    timestamp: clerkConfig.configTimestamp || 'UNKNOWN',
    hasOAuthRedirects: !!(clerkConfig.afterSignInUrl || clerkConfig.signInForceRedirectUrl),
    redirectsRemoved: !clerkConfig.afterSignInUrl && !clerkConfig.signInForceRedirectUrl
  });

  console.log('[App.jsx] REDIRECT DEBUGGING:', {
    hasForceSignInRedirect: !!clerkConfig.signInForceRedirectUrl,
    hasForceSignUpRedirect: !!clerkConfig.signUpForceRedirectUrl,
    signInFallback: clerkConfig.signInFallbackRedirectUrl,
    signUpFallback: clerkConfig.signUpFallbackRedirectUrl,
    allowedOrigins: clerkConfig.allowedRedirectOrigins
  });

  // Простая проверка наличия ключа
  if (!clerkConfig.publishableKey) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-slate-950">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-500 mb-4">Configuration Error</h1>
          <p className="text-slate-300">
            Missing Clerk publishable key. Please check your environment variables.
          </p>
        </div>
      </div>
    );
  }

  return (
    <ClerkProvider {...clerkConfig}>
      <ConfigProvider>
        <AuthContextProvider>
          <CopyProvider>
          <Routes>
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <div className="min-h-screen bg-slate-950">
                    <NavigationBar
                      activeType={calculatorType}
                      onTypeChange={setCalculatorType}
                    />
                    {calculatorType === 'Crypto' ? (
                      <Calculator />
                    ) : (
                      <div className="flex items-center justify-center h-[calc(100vh-4rem)] text-slate-500">
                        {calculatorType} calculator coming soon...
                      </div>
                    )}
                  </div>
                </ProtectedRoute>
              } 
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </CopyProvider>
      </AuthContextProvider>
      </ConfigProvider>
    </ClerkProvider>
  );
}

export default App;
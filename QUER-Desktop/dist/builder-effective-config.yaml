directories:
  output: dist
  buildResources: build
appId: com.quer.desktop
productName: QUER
copyright: Copyright © 2025 Quer
protocols:
  name: QUER App
  schemes:
    - querapp
mac:
  category: public.app-category.productivity
  target:
    - dmg
    - zip
  icon: icon.icns
  darkModeSupport: true
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  identity: <PERSON> (S26L5SL85F)
win:
  target: appx
  icon: build/icon.ico
appx:
  identityName: Quer.Quer
  publisher: CN=EC8DCEF9-E5DC-4085-8669-2B04DB53C16A
  publisherDisplayName: Quer
  applicationId: Quer.Quer
  displayName: QUER
dmg:
  sign: false
  contents:
    - x: 130
      'y': 220
    - x: 410
      'y': 220
      type: link
      path: /Applications
publish:
  provider: generic
  url: https://quer.us/updates/
  channel: latest
afterSign: ./notarize.js
files: []
electronVersion: 36.2.1

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/app-update.yml</key>
		<data>
		b4x2lXABiu4/2SLIKT16gNFh30I=
		</data>
		<key>Resources/app.asar</key>
		<data>
		KQs0mDudV2TGLTZ2ljlkPZzKLo0=
		</data>
		<key>Resources/icon.icns</key>
		<data>
		IjPlDF6VW1CVqrKKpRm1ytlMKG8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Electron Framework.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			MNl6CpXUfEvoJzu+OqIcAc6G6eY=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Electron.framework" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/Mantle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			ZGvKTrxxvX1MW6EA+MAsaEnV+yY=
			</data>
			<key>requirement</key>
			<string>identifier "org.mantle.Mantle" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/QUER Helper (GPU).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			MlriN+xQz/C1NyL8Sc5pipVaj2c=
			</data>
			<key>requirement</key>
			<string>identifier "com.quer.desktop.helper.GPU" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/QUER Helper (Plugin).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			C5T/mmIIbj/UhqlU/1jEKNtK1oY=
			</data>
			<key>requirement</key>
			<string>identifier "com.quer.desktop.helper.Plugin" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/QUER Helper (Renderer).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			89NuubV8+hUJRU15TNiyAEvBxAk=
			</data>
			<key>requirement</key>
			<string>identifier "com.quer.desktop.helper.Renderer" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/QUER Helper.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			Yo/kCtpHj1w0z50VByh5LXXMTGU=
			</data>
			<key>requirement</key>
			<string>identifier "com.quer.desktop.helper" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/ReactiveObjC.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			wVNB6gQGHHWNY8sYLBft9E5khss=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.reactive" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Frameworks/Squirrel.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			h1VpjTuUj4XY5EaUCIoMivKaXmI=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Squirrel" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Resources/app-update.yml</key>
		<dict>
			<key>hash2</key>
			<data>
			+7qgIVbuv4X2w0Aj/i0ljz2YaBHoGJg1pE0tbpqIIN0=
			</data>
		</dict>
		<key>Resources/app.asar</key>
		<dict>
			<key>hash2</key>
			<data>
			raHWYYd+8VNA8Z1wuk5T38deTnfdRoo6P4OHC9Meq0M=
			</data>
		</dict>
		<key>Resources/icon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			kcfis8RN3OKUYcjTjxrGlsuICKca4+oP1r0Q7dN4b+Y=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		iWApHSq+vOgMvte4rQgN+3a+uys=
		</data>
		<key>Resources/MainMenu.nib</key>
		<data>
		8vZwWbxqzsKMQ1nd2hqwWHK4HT8=
		</data>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			cdpok9aKteMMFq2ixD7a/adMXb0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Je2WhgXbw7pyJ3ERX+TcQ1YzphM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			+4tIc1Mv3wZYbHBH7Y/i76zhoUI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			dtoiqY83b0gxQ0nyaU7LHcFccDA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			yhLH6ZQgvNgHMwxVrVGuIHy7t1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Sr0fsskjT4oAOcyftHbz9dH67GM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<data>
		VZdgtyVVJwoKWLVSwfd0LFkTfWE=
		</data>
		<key>Resources/chrome_200_percent.pak</key>
		<data>
		RO9Fgy80ulIqprRN8p3Lei5c43g=
		</data>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			dNckiuuRzYHnt4ItcPQ5NgPJswA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			LGe+1BZrS/C6jko4G1tBiG+Gea0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VAXfOrJxzQUxwqQYpharBbYOH3E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ExkyZ6UooSEyl41EgiFkwhUFcCE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			YreTxgCcD2H33lpRf+ialj/w2Sk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			OSkz0ij6ohpouOn25jaZq5qjQhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rmbS8Vmt2KX/uVEFTbnRR6yoTYk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			MPBWSLhKwNO0KVCt4CA9iVYUWAQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			RS3n0WoTsPSBX86BQLtZEuiGd4Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8H8WN35BneHrBo71SJF9PgtEhW4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Xrnt5enLn+tI+yp78zvb2nU/8UE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rWhJStBoat+6QFFUqwq81QO7ZRc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			HBX9k0DegNwFWR1kasSr8pXKHAc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			GhmzLFKguRfqEt5uFUU2ztxUpi8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			NNfG3vhgbsvYa9irS4Zavc/sF1k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Kh17cC2BxjQKPbmgXKyOwNLvsHc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			uENe0CTO+ROQf9dJI12ldVpitr8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5or4S0O/rpGV+fUN0jURQ4OrecE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<data>
		jRstQnB4EmCJB/gAAEHBbndVUyo=
		</data>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Yq6vq/6RXnG0nVgcuGHuRi5GzGo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			GAPU7qJweF9+MnGvkOOIW10Ova8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Urj4Oi49uzfOjyJ6OuYxfnheTo4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Q5/f4/wCOaHfTZOWwVxz72S3vKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			eDxvMWlxy2nditM1dZLFLAxeXko=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			APiKhyVaRKkcXPi/clY0vxI7yOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8+RN2UIN/y/u/NyDIYtgtLN40e0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			/rgKRoGLWj/P9SYPioTg1DcFtAY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ToLykFPy2UwvspNzmUv3v9qqbP8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			fwQ1lye9pDR0PDA+FXRg3M3by6w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			mPXXaJB9RTnT80U+YLGuU6KAYfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			NF8IjPWlksAvPmI+PHnfg3H5nps=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			V6d1LYXyi1VIuqbPpBqidP+ufmU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			2bb+BuZG7HIUub6/4anf07mTh+Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			M5AwapFTnow1sPJkHF5WNcKVMjw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<data>
		vqR5Bzb2EoMG2Fr1YIRWkziqecA=
		</data>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			YjhJQTAvyzei/+Z5ziXu3KT+5fw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			RjUV/TajI5qZ/iaxJm2B6I5dNNg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			oVoOBDEdK+0WKjWcLvcQs/cOI7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			UWaqN3vcbrxSJzlSCMQ+ePDKAx0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			LuQ5RRQwqx3HqThPaK+x0WEg2vU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Hj9rrvAd/Bmz983Ra4RTAtlAX44=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			JSqZblL3aK/ck0C+pAzXVssmyek=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Dw/YNIMC90hNodWd+inFOXrjeM8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			e9wFV2Ga81l/xGM8JtWEI4ZbdGU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			1N+bG5GlSzcL0oobj+erin8mFUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			eVQvoak0RH8uzriOgUi7mba5VpM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			1jjIU91GQC5E2Sf//BVCXkWAbHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			tAIB959IjpdFZRlLtUZ69R7YJ1A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<data>
		EENqvD2jiaFcgZu21eoFZo9qi+w=
		</data>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			D/uK2jdsJqqADgy2VPGuRm7BALo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			SUgX4GLTrSML2mzJS43wlVnJNFM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			fn7gDvsavQvjm5oFGYn/uMjjh5Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Helpers/chrome_crashpad_handler</key>
		<dict>
			<key>cdhash</key>
			<data>
			4B51J84LIePkg3C6q31Gj90ORAs=
			</data>
			<key>requirement</key>
			<string>identifier "chrome_crashpad_handler" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = S26L5SL85F</string>
		</dict>
		<key>Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			lqK6xxtlwCl3+eY6/7yKXnk26BRkrnRy3/BnIrHU+wE=
			</data>
		</dict>
		<key>Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			bBOmDlhZWhIbBHsbYTIsMGxaGTUAO0PPOk3924YCO+M=
			</data>
		</dict>
		<key>Libraries/libffmpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			lzYa6QthVUInw23ms8H9i9sp8qym27zQyP1su3+BiuM=
			</data>
		</dict>
		<key>Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Bup3bYjJnYoEf/BT7F7ONioKIdooCADW1tLQ1RMQ2yE=
			</data>
		</dict>
		<key>Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9lCF0qnZIe3+Xij5nvlwYSJffVe/bt2BQteA3/xEN6E=
			</data>
		</dict>
		<key>Resources/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFo3i1tmgmyD6qiMTnV8l9zsllWcd4lhy0ZG2k52Bk=
			</data>
		</dict>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1iOb0FBZ4z1xffv+lOGB1cJJD6N8/k0PkSm3WRCVLQA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WMWOVXa2FXV4xxeKJY6+DXh2YlFZ4iwUlZ78t2Rqkq8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			4IHnboOlcpGYtnIQJ+EMOgZdEYGl8gDza775J/+Qxj4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			TMfnlO3X+FexFhoiklfyctEGZCsPOQC3baYykGQ4PSc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			IA5Tc3dl5vwTYdqSgZaKCHa0ZTc2kqe4bfFoG5I6qyY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			teYEmzDseImsZDFxZaWmDSlLQpibZKEXr6vPkPDlrzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			f8BGRzk0TcBF4N5oOKLsE5GwRWxgl/jNmmxWJtm6PJs=
			</data>
		</dict>
		<key>Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			OTKuYxBbTGo8i12Uu94Hay+kvt1S1Tn5pnwtyaMJHE8=
			</data>
		</dict>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			E5aopPOVwqbJKnOYLhIMouozZXa+geWedDyY0IXk7UU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DBO8FEcq71rDJFkYHSFSP9mcYcGNc1SqL/1fBYwO0iI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			9otF3BDp7nM0VgLADzImkRMq5T/ElTQayqph/UXluws=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xV8drLwOo7BmPXKwmn3VUkM8ZZw/CFtlEmLLY0duBoU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			E8U8plznBXZ0En+AMpHhHzHbwHyQC3veGlFgnI5ynn4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			nXyWy0KGJziq7wL67bvuUgyE+rkMgJ7j/XoIaPF2+RM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DaL1Ovvl1RBoi858aAD2Nq2bfGKi7poo+f0nIBP3zt8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0+aOvIKUTyy64FWOgb26UrO8TOjiXdNFvnyNyOy08T8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			JeNgTXKnGJ9A4oefcZV0mOcmW6eyMzXfruULPw4YzFY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DBMji15dgJE4fxNtcKLPd6cud6P4ZT7DC+dTGGeuiIE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			zhOK3195mRzryKEADQELyJ0Y8p8dEJlZRzGFOgaTr9E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xK+MvV8wkOfGnXu6U23WaYO61vHSVvHFKXcMVy6aG2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VW9eUAwCegp/xpgGM5Tfn5uovGWvuo0Qqow4+m2YfHo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DJyRssqbXKtreUZkAIjQB9ZIVXijeYTgZVOvBHAJgKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AdimRZK+xIDH/HwJ8GKa6n84LusTnJZDxvqowkYCgEE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			dZN0NIvb4B0Y4je2eRjXz8A58P6WYkcaDgWu/8y/Xyg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vFK4NUT3mymMItQaLuAPV9vRNwxtPiY4+u2WquqDh3U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mzOjwShF11pbiZ/UKNefyW6RGVOTndJEfRHchXc8gyI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			qfYHLoWOopy2ZQGEeFbUA8hi8j0DysJlbR4PooZf3Hs=
			</data>
		</dict>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			voJr1d26ymfuR/s5HZdjB8lmO8NKY684818cSOTL/Ow=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			QVIvHrVF208AsWQ6IdvLrJR3Fy7ObQ/mBA6B8GAUAkg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			CucAfbR7ubXd0smG/lkFWcZK5QRWoEn2GqhZ1D/E0hc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			5xdyYMITrp7KGxTgLxQte43rV3tVbP0E5V7WPVMflaE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			yjMdRMiSHhUMDM/OEs129QlUavZ8DeWwZ96NqDpTh34=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			PFeXOyLQSdW+5zoqYruowZdRTjZB8iSjqlCbK15dgHc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Ey6pH/OXlqg7iYx/AJr/k+krYaWStFrvOkFtYIPojfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			n7dskPnpZcUy3ij8trL2+wanCNzkeflv36kZte6DyII=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			hZrsbBTpuexcxvgZzt3eh6rtj5Wc/qDatM44dT3PFc8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mxc3xFSZ9OOvtK7HFHXHwcl72GLWxdnBhlWwlYHLOD8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			BxfDaF7o7muj2AOH6srBWtprlo9mbvY4e0853PbUQ9k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RfDj8aNepNO2acxo55XcxEdzi6u8BRNaLlmkS2NfRy4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			kDXHq9rgNwAVs65lRJi81x7S7IPbOyMhcB1/AMbkspk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			JvnPgtwBnsIXSsCBQTKDtKROl6eIMyVAc6REM3Y/KxM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			g5pMHwL2za4GszWiDfC4kXJtpOV1FR+MPB6Yad0q54w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			3aLfwLn0Ma77bMbU3JPp07+PgbLGHkjFdNA0BBsb8BQ=
			</data>
		</dict>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vKWuoO9Rw1ROD1/69AVQsNz1zdf1Ih2wcVBmp8hhSkc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			jwirxzAHPM8rutQ00Mq0j/rUlADgJeYX8YUhxITAgjQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			yxg/CB/OrOSH8wrIuuzg0JYi+QgV+hM12CEXkIP8te0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Kr0HiFiIpGPgO/gKiqJupbdf917iZ+UNo7skk9DANgk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			FtSpDT6Mot0e45tIF/sBDt4KlQ5is9dNfdMjz2N5iK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			gsWMQUU4jPcIaTSQYSRaVJQZCCDGyiQt1KOBb8BTzE4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			akHCIyWZsxNLjRtX/mSm+rFd7bReRdC11wuhLxJRyfU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			R6E2AJKhCaqIOmNJtult5rPumcZjnteqe6GcyflkhvY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			fYJOb6+HfhQLVlH5Ds4dqjb9rJaBltc5ihf66u9mPuo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VXobpj4Ty8AUb5zxcoxuZD74WXwM0nugYQsurLukYSk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DkmX9m3+lIvarJaso+CZSWCFSbyYXSquq+267f0GebA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1ypMW5fH8+TPc+pC5vhWj25DcHe4Iqz6m5YkLB78MIg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			kWD7dMC3ykAyE0z/ZGXGd70JeqTb3b1YcModhHQivZ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			s/Iqvs8M9O/DsdraT/HEm1RCwJA7/TJd+mRxnFkE0Rw=
			</data>
		</dict>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			3QO5xKI3Ir9icp2f5mI2K0flKM4WaAcMn+9101xy0xg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			EngWfDdtqrvMyRhFr79kT1IIMHmgERHqM//M26yCFi0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xB2opuwKSHRPjhWHmv3zUhJYs9sTqc7GCXb1BSoobCc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>

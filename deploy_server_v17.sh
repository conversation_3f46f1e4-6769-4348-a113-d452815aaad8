#!/bin/bash
# ==============================================================================
# ФИНАЛЬНЫЙ СКРИПТ v17.0 - ТОЛЬКО СЕРВЕРНАЯ ЧАСТЬ
# ==============================================================================
set -e

# --- Константы ---
PROJECT_ROOT="/Users/<USER>/quer-calc-ui"
ASTRO_DIR="${PROJECT_ROOT}/astro-landing"
SSH_HOST="quer-x0"

# --- Фаза 1: Загрузка артефакта на сервер ---
echo "🚀 Загрузка артефакта на сервер..."
scp "${ASTRO_DIR}/deployment_artifact.tar.gz" "${SSH_HOST}:/tmp/"

# --- Фаза 2: Атомарный Деплой на Сервере ---
echo -e "\n✅ Выполнение атомарного деплоя и настройки на сервере..."
ssh "$SSH_HOST" "
    set -e
    echo '⚙️  Остановка и очистка...'
    sudo systemctl stop astro-blue.service
    sudo rm -rf /var/www/quer.us/blue/app
    sudo mkdir -p /var/www/quer.us/blue/app
    
    echo '📦 Распаковка нового артефакта...'
    sudo tar -xzf /tmp/deployment_artifact.tar.gz -C /var/www/quer.us/blue/app

    echo '⏳ Установка нативных зависимостей на сервере...'
    cd /var/www/quer.us/blue/app && sudo npm install --omit=dev
    
    echo '⚙️  Восстановление .env и настройка прав...'
    BACKUP_ENV=\$(sudo find /var/www/quer.us/blue/ -maxdepth 2 -type f -name '.env' -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [ -n \"\$BACKUP_ENV\" ]; then
        sudo cp \"\$BACKUP_ENV\" /var/www/quer.us/blue/app/.env
    fi
    sudo chown -R www-data:www-data /var/www/quer.us/blue/app
    
    echo '🚀 Настройка и запуск сервиса...'
    sudo sed -i 's|WorkingDirectory=.*|WorkingDirectory=/var/www/quer.us/blue/app|' /etc/systemd/system/astro-blue.service
    sudo sed -i 's|ExecStart=.*|ExecStart=/usr/bin/node server/entry.mjs|' /etc/systemd/system/astro-blue.service
    
    sudo systemctl daemon-reload
    sudo systemctl start astro-blue.service
"

# --- Фаза 3: Финальная Верификация ---
echo -e "\n✅ Финальная верификация..."
ssh "$SSH_HOST" "
    sleep 5
    echo '📊 Финальный статус сервиса Astro:'
    sudo systemctl status astro-blue.service --no-pager
    echo '---'
    echo '📝 Проверка последних 15 строк лога:'
    sudo journalctl -u astro-blue.service -n 15 --no-pager
"

echo "🔬 Проверка локального ответа от Astro..."
LOCAL_STATUS=\$(ssh "$SSH_HOST" "curl -o /dev/null -s -w '%{http_code}' http://localhost:3001/")

if [ "\$LOCAL_STATUS" -eq 200 ]; then
    echo "🎉🎉🎉 ПОЛНЫЙ УСПЕХ! Сайт https://quer.us должен быть доступен."
else
    echo "❌ ОШИБКА: Astro сервис на сервере ответил кодом \$LOCAL_STATUS. Проблема сохраняется."
fi
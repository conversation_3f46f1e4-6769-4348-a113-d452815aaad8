{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(repomix:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./repomix-with-timestamp.sh:*)", "Bash(rm:*)", "Bash(ssh:*)", "Bash(./promote-to-production.sh:*)", "Bash(./deploy-rust-with-crosscompiler.sh:*)", "Bash(./health-check.sh:*)", "Bash(./deploy-stag-to-prod-blue-green-no-nginx-traffic-switch.sh:*)", "Bash(./blue-green-switch.sh:*)", "Bash(./blue-green-rollback.sh:*)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "<PERSON><PERSON>(timeout 10s npm start)", "Bash(npm run pack:*)", "Bash(sudo chown:*)", "Bash(/Users/<USER>/quer-calc-ui/create-app.sh:*)", "Bash(swift build)", "Bash(./create-app.sh:*)", "Bash(grep:*)", "Bash(cp:*)", "Bash(./deploy-landing-script.sh:*)", "Bash(rg:*)", "Bash(./deploy-terminal-script.sh:*)", "Bash(npm run build:*)", "Bash(swift build:*)", "<PERSON><PERSON>(curl:*)", "Bash(./deploy-local-to-staging.sh)", "Bash(./deploy-stag-to-prod-blue-green-with-nginx-traffic-switch.sh:*)", "Bash(rsync:*)", "<PERSON><PERSON>(open:*)", "Bash(npm run preview:*)", "Bash(go build:*)", "Bash(./config/config-loader.sh validate:*)", "Bash(./config/config-loader.sh show:*)", "Bash(./config/generate-env.sh:*)", "Bash(./config/generate-nginx.sh:*)", "Bash(./deploy-with-config.sh:*)", "Bash(./config/generate-clerk-config.sh generate:*)", "Bash(./config/generate-all.sh generate:*)", "Bash(./config/generate-all.sh:*)", "Bash(./deploy.sh:*)", "Bash(sudo systemctl status:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(jq:*)", "Bash(npm run dist:prod:mac:*)", "Bash(npm run dist:staging:mac:*)", "<PERSON><PERSON>(./config-manager.sh:*)", "Bash(npm start)", "<PERSON><PERSON>(mv:*)", "Bash(/Users/<USER>/quer-calc-ui/deploy-stag-to-prod-blue-green-no-nginx-traffic-switch.sh:*)", "Bash(/Users/<USER>/quer-calc-ui/deploy-stag-to-prod-blue-green-with-nginx-traffic-switch.sh:*)", "Bash(./astro-landing/deploy-landing-script.sh:*)", "Bash(./deploy-go-backend.sh:*)", "Bash(./deploy-local-to-staging.sh:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(./scripts/deployment-orchestrator.sh:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:clerk.com)", "Bash(tar:*)", "Bash(sudo rm:*)", "Bash(npm install:*)", "<PERSON><PERSON>(sed:*)"], "deny": []}}
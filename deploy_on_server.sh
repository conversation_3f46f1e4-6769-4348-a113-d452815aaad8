#!/bin/bash
# ==============================================================================
# ФИНАЛЬНЫЙ СКРИПТ v15.0 - СЕРВЕРНАЯ ЧАСТЬ
# ==============================================================================
set -e

echo "--- ДЕПЛОЙ И ЗАПУСК НА СЕРВЕРЕ ---"

# Останавливаем сервис
echo "⚙️  Остановка сервиса Astro..."
sudo systemctl stop astro-blue.service

# Создаем чистую директорию и распаковываем артефакт
echo "📦 Развертывание нового артефакта..."
sudo rm -rf /var/www/quer.us/blue/app
sudo mkdir -p /var/www/quer.us/blue/app
sudo tar -xzf /tmp/deployment_artifact.tar.gz -C /var/www/quer.us/blue/app

# Устанавливаем нативные зависимости на сервере
echo "⏳ Установка зависимостей, совместимых с Ubuntu/AMD64..."
cd /var/www/quer.us/blue/app && sudo npm install --omit=dev

# Восстанавливаем .env из самого свежего бэкапа
BACKUP_ENV=$(sudo find /var/www/quer.us/blue/ -maxdepth 2 -type f -name ".env" -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
if [ -n "$BACKUP_ENV" ]; then
    echo "   -> Найден .env бэкап: $BACKUP_ENV. Копируем..."
    sudo cp "$BACKUP_ENV" /var/www/quer.us/blue/app/.env
else
    echo "ВНИМАНИЕ: .env бэкап не найден. Создается пустой файл. Его нужно будет заполнить."
    sudo touch /var/www/quer.us/blue/app/.env
fi

# Устанавливаем права
echo "⚙️  Настройка прав..."
sudo chown -R www-data:www-data /var/www/quer.us/blue/app

# Исправляем systemd и запускаем сервис
echo "🚀 Настройка и запуск сервиса..."
sudo sed -i 's|WorkingDirectory=.*|WorkingDirectory=/var/www/quer.us/blue/app|' /etc/systemd/system/astro-blue.service
sudo sed -i 's|ExecStart=.*|ExecStart=/usr/bin/node dist/server/entry.mjs|' /etc/systemd/system/astro-blue.service
sudo systemctl daemon-reload
sudo systemctl start astro-blue.service

sleep 5
echo "📊 Финальный статус сервиса Astro:"
sudo systemctl status astro-blue.service --no-pager
#!/bin/bash
# ==============================================================================
# ФИНАЛЬНЫЙ СКРИПТ v15.0 - ЛОКАЛЬНАЯ ЧАСТЬ
# Создает ПРАВИЛЬНЫЙ артефакт и загружает его
# ==============================================================================
set -e

# --- Фаза 1: Локальная Подготовка ---
echo "✅ Фаза 1: Подготовка локальных файлов..."
cd /Users/<USER>/quer-calc-ui/astro-landing

echo "📦 Создание КОРРЕКТНОГО артефакта для деплоя..."
# Сначала чистая установка и сборка
rm -rf dist node_modules deployment_artifact.tar.gz deployment_artifact
npm install && npm run build

# Создаем директорию для артефакта
mkdir -p deployment_artifact

# Копируем всё необходимое: результат сборки И package.json
cp -R dist/ deployment_artifact/
cp package.json package-lock.json deployment_artifact/

# Упаковываем всё содержимое артефакта в архив
tar -czf deployment_artifact.tar.gz -C deployment_artifact .

echo "   -> Артефакт deployment_artifact.tar.gz успешно создан."

# --- Фаза 2: Загрузка на сервер ---
echo -e "\n✅ Фаза 2: Загрузка на сервер..."
scp deployment_artifact.tar.gz quer-x0:/tmp/

echo "✅ Локальная часть завершена. Теперь запустите скрипт на сервере."
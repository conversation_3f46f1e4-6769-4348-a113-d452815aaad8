Account ID: b662931e0db1b0ef84bc872efb57c578
R2 Account To<PERSON> was successfully created
Permissions: Allows the ability to read, write, and list objects in specific buckets.
Buckets: quer
Name: quer
Public Access: Enabled
Default Storage Class: Standard
Custom Domains domain: downloads.quer.us (Minimum TLS: 1.3 | Status: Active | Access: Enabled)
CORS Policy:
[
  {
    "AllowedOrigins": [
      "http://localhost:3000",
      "https://downloads.quer.us"
    ],
    "AllowedMethods": [
      "GET",
      "HEAD"
    ],
    "AllowedHeaders": [
      "*"
    ],
    "MaxAgeSeconds": 3600
  }
]
Use this token for authenticating against the Cloudflare API:
Token value: ****************************************
Use the following credentials for S3 clients:
Access Key ID: d72362bd9863bd371996a5cb6683b9b4
Secret Access Key: b85a3d9be908742a05ca207a3d81ff3d49df5d24c198a57c72f2a9004b1e9571
Use jurisdiction-specific endpoints for S3 clients:
Default: https://b662931e0db1b0ef84bc872efb57c578.r2.cloudflarestorage.com
S3 API: https://b662931e0db1b0ef84bc872efb57c578.r2.cloudflarestorage.com/quer
For security reasons, these token values will not be shown again.
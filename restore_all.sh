#!/bin/bash
# ==============================================================================
# ФИНАЛЬНЫЙ СКРИПТ v16.0 (Исправленная логика сборки артефакта)
# ==============================================================================
set -e

# --- Фаза 1: Локальная Подготовка ---
echo "✅ Фаза 1: Подготовка локальных файлов..."
cd /Users/<USER>/quer-calc-ui/astro-landing

echo "📦 Создание КОРРЕКТНОГО артефакта..."
# Полная очистка и сборка
rm -rf dist node_modules deployment_artifact.tar.gz
npm install && npm run build

# КЛЮЧЕВОЕ ИСПРАВЛЕНИЕ: Создаем артефакт из правильных папок
# Копируем ./dist/client и ./dist/server в корень артефакта
tar -czf deployment_artifact.tar.gz -C dist .
# Добавляем package.json файлы в корень архива
cp package.json package-lock.json .
(cd deployment_artifact && tar -rzf ../deployment_artifact.tar.gz ../package.json ../package-lock.json)
rm package.json package-lock.json

echo "   -> Артефакт deployment_artifact.tar.gz успешно создан."

# --- Фаза 2: Загрузка на сервер ---
echo "🚀 Загрузка артефакта на сервер..."
scp deployment_artifact.tar.gz quer-x0:/tmp/

# --- Фаза 3: Атомарный Деплой на Сервере ---
echo -e "\n✅ Фаза 3: Деплой и запуск на сервере..."
ssh quer-x0 "
    set -e
    echo '⚙️  Остановка и очистка...'
    sudo systemctl stop astro-blue.service
    sudo rm -rf /var/www/quer.us/blue/app
    sudo mkdir -p /var/www/quer.us/blue/app
    
    echo '📦 Распаковка нового артефакта...'
    sudo tar -xzf /tmp/deployment_artifact.tar.gz -C /var/www/quer.us/blue/app

    echo '⏳ Установка нативных зависимостей на сервере...'
    cd /var/www/quer.us/blue/app && sudo npm install --omit=dev
    
    echo '⚙️  Настройка прав и запуск сервиса...'
    sudo chown -R www-data:www-data /var/www/quer.us/blue/app
    
    # Исправляем systemd, чтобы он указывал на ПРАВИЛЬНЫЙ путь (теперь без dist/)
    sudo sed -i 's|ExecStart=.*|ExecStart=/usr/bin/node server/entry.mjs|' /etc/systemd/system/astro-blue.service
    sudo sed -i 's|WorkingDirectory=.*|WorkingDirectory=/var/www/quer.us/blue/app|' /etc/systemd/system/astro-blue.service
    
    sudo systemctl daemon-reload
    sudo systemctl start astro-blue.service
"

# --- Фаза 4: Финальная Верификация ---
echo -e "\n✅ Фаза 4: Финальная верификация..."
ssh quer-x0 "
    sleep 5
    echo '📊 Финальный статус сервиса Astro:'
    sudo systemctl status astro-blue.service --no-pager
    echo '---'
    echo '📝 Проверка последних 15 строк лога:'
    sudo journalctl -u astro-blue.service -n 15 --no-pager
"
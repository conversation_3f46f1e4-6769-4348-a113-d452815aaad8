<!DOCTYPE html>
<html>
<head>
  <title>Redirecting back to QUER app...</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #0f172a;
      color: #e2e8f0;
      text-align: center;
      padding-top: 20vh;
      height: 100vh;
      margin: 0;
      overflow: hidden;
    }
    .container {
      max-width: 400px;
      margin: 0 auto;
      padding: 20px;
    }
    .loader {
      width: 48px;
      height: 48px;
      border: 5px solid #1e293b;
      border-bottom-color: #D97706;
      border-radius: 50%;
      display: inline-block;
      box-sizing: border-box;
      animation: rotation 1s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes rotation {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    p {
      color: #94a3b8;
      font-size: 0.875rem;
      line-height: 1.4;
      margin: 1rem 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="loader"></div>
    <h2>Redirecting back to QUER app...</h2>
    <p>Please wait while we're completing the authentication process.</p>
    <p id="status-message">Checking environment...</p>
  </div>
  
  <script>
    (function() {
      const statusMessage = document.getElementById('status-message');
      
      // Обновление статусного сообщения
      function updateStatus(message) {
        if (statusMessage) {
          statusMessage.textContent = message;
        }
      }
      
      // Функция проверки, запущены ли мы в Electron
      function isRunningInElectron() {
        return window.electronAPI && window.electronAPI.isElectron === true;
      }
      
      // Функция для логирования в консоль с отметкой времени
      function logWithTimestamp(message) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${message}`);
      }
      
      // Основная логика
      function init() {
        try {
          logWithTimestamp('OAuth redirect page loaded, initializing...');
          
          // Проверяем параметры URL
          const urlParams = new URLSearchParams(window.location.search);
          const clerkHandshake = urlParams.get('__clerk_handshake');
          
          // Если мы НЕ в Electron (т.е. в системном браузере)
          if (!isRunningInElectron()) {
            logWithTimestamp('Running in system browser, redirecting to desktop app...');
            updateStatus('Detected system browser, redirecting to QUER app...');
            
            // Определяем тип OAuth callback
            let redirectUrl;
            
            if (clerkHandshake) {
              // 🎯 НОВОЕ: Обработка Clerk handshake параметров
              logWithTimestamp('Detected Clerk OAuth handshake, processing...');
              updateStatus('Processing Clerk authentication...');
              
              const customProtocolPrefix = 'querapp://auth-callback';
              redirectUrl = `${customProtocolPrefix}?__clerk_handshake=${encodeURIComponent(clerkHandshake)}`;
              
              logWithTimestamp(`Redirecting to Clerk protocol: ${customProtocolPrefix}[handshake hidden]`);
            } else {
              // Старая логика для других OAuth провайдеров
              const customProtocolPrefix = 'querapp://oauth-complete';
              const searchParams = window.location.search || '';
              const hashParams = window.location.hash || '';
              redirectUrl = `${customProtocolPrefix}${searchParams}${hashParams}`;
              
              logWithTimestamp(`Redirecting to legacy protocol: ${customProtocolPrefix}[params hidden]`);
            }
            
            // Выполняем редирект с небольшой задержкой для отображения информации пользователю
            setTimeout(() => {
              updateStatus('Opening QUER app...');
              window.location.href = redirectUrl;
              
              // Сообщение на случай, если редирект не сработает автоматически
              setTimeout(() => {
                updateStatus('If the app doesn\'t open automatically, click here');
                document.body.innerHTML += `
                  <div style="margin-top: 20px;">
                    <a href="${redirectUrl}" style="display: inline-block; padding: 10px 20px; background-color: #D97706; color: white; text-decoration: none; border-radius: 6px; font-weight: 500;">
                      Open QUER App
                    </a>
                  </div>
                `;
              }, 3000);
            }, 1500);
          } else {
            // Если мы уже в Electron, показываем сообщение
            logWithTimestamp('Already in Electron, no redirect needed');
            updateStatus('Authentication complete! You can close this window.');
          }
        } catch (error) {
          console.error('Error in OAuth redirect:', error);
          updateStatus('Error processing authentication. Please try again.');
        }
      }
      
      // Запускаем инициализацию после загрузки страницы
      window.addEventListener('DOMContentLoaded', init);
    })();
  </script>
</body>
</html>

const c=document.getElementById("token-input"),i=document.getElementById("protocol-url"),u=document.getElementById("generate-url"),k=document.getElementById("open-url"),m=document.getElementById("log"),b=document.getElementById("load-token"),p=document.getElementById("save-to-storage"),a=document.getElementById("check-token-input"),h=document.getElementById("check-token"),s=document.getElementById("token-check-result"),g=document.getElementById("localstorage-tokens"),v=document.getElementById("refresh-localstorage");function r(e,t="info"){console.log(`[${t.toUpperCase()}] ${e}`);const o=document.createElement("div");o.className=`mb-1 pb-1 border-b border-slate-700 ${t==="error"?"text-red-500":t==="success"?"text-green-500":"text-blue-400"}`;const n=new Date().toLocaleTimeString();o.textContent=`[${n}] ${e}`,m.prepend(o)}u.addEventListener("click",()=>{const e=c.value.trim();if(!e){r("Введите токен","error");return}const t=`querapp://oauth-complete?clerk_session_token=${encodeURIComponent(e)}`;i.value=t,r(`URL сгенерирован: ${t}`)});k.addEventListener("click",()=>{const e=i.value;if(!e.startsWith("querapp://")){r("Некорректный протокольный URL","error");return}r(`Пробуем открыть: ${e}`);try{window.location.href=e,r("Команда открытия URL выполнена","success")}catch(t){r(`Ошибка при открытии URL: ${t.message}`,"error")}});b.addEventListener("click",()=>{try{const e=localStorage.getItem("clerk_oauth_token")||localStorage.getItem("clerk_debug_token");e?(c.value=e,r("Токен загружен из localStorage","success"),a.value=e):r("Токен не найден в localStorage","error")}catch(e){r(`Ошибка при загрузке из localStorage: ${e.message}`,"error")}});p.addEventListener("click",()=>{const e=c.value.trim();if(!e){r("Введите токен для сохранения","error");return}try{localStorage.setItem("clerk_debug_token",e),localStorage.setItem("clerk_debug_timestamp",Date.now().toString()),r("Токен сохранен в localStorage","success"),d()}catch(t){r(`Ошибка при сохранении в localStorage: ${t.message}`,"error")}});h.addEventListener("click",async()=>{const e=a.value.trim();if(!e){s.innerHTML='<div class="text-red-500">Введите токен для проверки</div>';return}s.innerHTML='<div class="text-blue-400">Проверка токена...</div>';try{const t=await fetch("/api/landing/auth/check-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e}),credentials:"include"}),o=await t.json();t.ok?s.innerHTML=`
          <div class="text-green-500 mb-2">Токен валиден!</div>
          <pre>${JSON.stringify(o,null,2)}</pre>
        `:s.innerHTML=`
          <div class="text-red-500 mb-2">Токен недействителен</div>
          <pre>${JSON.stringify(o,null,2)}</pre>
        `}catch(t){s.innerHTML=`
        <div class="text-red-500">Ошибка при проверке токена: ${t.message}</div>
      `}});function d(){try{const e=localStorage.getItem("clerk_oauth_token"),t=localStorage.getItem("clerk_debug_token"),o=localStorage.getItem("clerk_debug_timestamp");let n='<table class="w-full text-left border-collapse">';if(n+='<tr class="bg-slate-700"><th class="p-2 border border-slate-600">Ключ</th><th class="p-2 border border-slate-600">Значение</th></tr>',e){const l=e.length>20?e.substring(0,10)+"..."+e.substring(e.length-10):e;n+=`
          <tr>
            <td class="p-2 border border-slate-600">clerk_oauth_token</td>
            <td class="p-2 border border-slate-600">${l} (длина: ${e.length})</td>
          </tr>
        `}else n+='<tr><td class="p-2 border border-slate-600">clerk_oauth_token</td><td class="p-2 border border-slate-600">Не найден</td></tr>';if(t){const l=t.length>20?t.substring(0,10)+"..."+t.substring(t.length-10):t;n+=`
          <tr>
            <td class="p-2 border border-slate-600">clerk_debug_token</td>
            <td class="p-2 border border-slate-600">${l} (длина: ${t.length})</td>
          </tr>
        `}else n+='<tr><td class="p-2 border border-slate-600">clerk_debug_token</td><td class="p-2 border border-slate-600">Не найден</td></tr>';if(o){const l=new Date(parseInt(o));n+=`
          <tr>
            <td class="p-2 border border-slate-600">clerk_debug_timestamp</td>
            <td class="p-2 border border-slate-600">${l.toLocaleString()} (${o})</td>
          </tr>
        `}n+="</table>",g.innerHTML=n}catch(e){g.innerHTML=`<div class="text-red-500">Ошибка при чтении localStorage: ${e.message}</div>`}}v.addEventListener("click",d);document.addEventListener("DOMContentLoaded",()=>{r("Страница загружена"),d();const e=localStorage.getItem("clerk_oauth_token")||localStorage.getItem("clerk_debug_token");e&&(c.value=e,a.value=e,r("Токен автоматически загружен из localStorage","info"))});

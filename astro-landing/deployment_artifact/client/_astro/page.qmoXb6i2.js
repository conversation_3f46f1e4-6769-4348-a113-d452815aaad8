import{a as h,s as w,r as p}from"./chunk-6EJX3HJ2.COMaeMaZ.js";const U="modulepreload",y=function(c){return"/"+c},f={},E=function(u,e,o){let l=Promise.resolve();if(e&&e.length>0){let s=function(t){return Promise.all(t.map(a=>Promise.resolve(a).then(i=>({status:"fulfilled",value:i}),i=>({status:"rejected",reason:i}))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),m=n?.nonce||n?.getAttribute("nonce");l=s(e.map(t=>{if(t=y(t),t in f)return;f[t]=!0;const a=t.endsWith(".css"),i=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${i}`))return;const r=document.createElement("link");if(r.rel=a?"stylesheet":U,a||(r.as="script"),r.crossOrigin="",r.href=t,m&&r.setAttribute("nonce",m),document.head.appendChild(r),a)return new Promise((g,v)=>{r.addEventListener("load",g),r.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${t}`)))})}))}function d(s){const n=new Event("vite:preloadError",{cancelable:!0});if(n.payload=s,window.dispatchEvent(n),!n.defaultPrevented)throw s}return l.then(s=>{for(const n of s||[])n.status==="rejected"&&d(n.reason);return u().catch(d)})};h();const S=()=>!!document.querySelector('[name="astro-view-transitions-enabled"]');if(S()){const{navigate:c,swapFunctions:u}=await E(async()=>{const{navigate:e,swapFunctions:o}=await import("./client.BYwDIDqY.js");return{navigate:e,swapFunctions:o}},[]);document.addEventListener("astro:before-swap",e=>{const o=document.querySelector("#clerk-components");if(o){const l=o.cloneNode(!0);e.newDocument.body.appendChild(l)}e.swap=()=>w(u,e.newDocument)}),document.addEventListener("astro:page-load",async e=>{await p({proxyUrl:"/__clerk",afterSignInUrl:"/account",afterSignUpUrl:"/account",signInUrl:"/sign-in",signUpUrl:"/sign-up",sdkMetadata:{version:"2.4.5",name:"@clerk/astro",environment:"production"},routerPush:c,routerReplace:o=>c(o,{history:"replace"})})})}else await p({proxyUrl:"/__clerk",afterSignInUrl:"/account",afterSignUpUrl:"/account",signInUrl:"/sign-in",signUpUrl:"/sign-up",sdkMetadata:{version:"2.4.5",name:"@clerk/astro",environment:"production"}});

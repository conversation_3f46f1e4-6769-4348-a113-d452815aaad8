import{g as He}from"./chunk-6EJX3HJ2.COMaeMaZ.js";var q={exports:{}},R={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var re;function Pe(){if(re)return R;re=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(n,o,s){var a=null;if(s!==void 0&&(a=""+s),o.key!==void 0&&(a=""+o.key),"key"in o){s={};for(var i in o)i!=="key"&&(s[i]=o[i])}else s=o;return o=s.ref,{$$typeof:e,type:n,key:a,ref:o!==void 0?o:null,props:s}}return R.Fragment=t,R.jsx=r,R.jsxs=r,R}var ne;function De(){return ne||(ne=1,q.exports=Pe()),q.exports}var Er=De();const Ve={name:"MissingMediaQueryDirective",title:"Missing value for `client:media` directive.",message:'Media query not provided for `client:media` directive. A media query similar to `client:media="(max-width: 600px)"` must be provided'},T={name:"NoMatchingRenderer",title:"No matching renderer found.",message:(e,t,r,n)=>`Unable to render \`${e}\`.

${n>0?`There ${r?"are":"is"} ${n} renderer${r?"s":""} configured in your \`astro.config.mjs\` file,
but ${r?"none were":"it was not"} able to server-side render \`${e}\`.`:`No valid renderer was found ${t?`for the \`.${t}\` file extension.`:"for this file extension."}`}`,hint:e=>`Did you mean to enable the ${e} integration?

See https://docs.astro.build/en/guides/framework-components/ for more information on how to install and configure integrations.`},F={name:"NoClientOnlyHint",title:"Missing hint on client:only directive.",message:e=>`Unable to render \`${e}\`. When using the \`client:only\` hydration strategy, Astro needs a hint to use the correct renderer.`,hint:e=>`Did you mean to pass \`client:only="${e}"\`? See https://docs.astro.build/en/reference/directives-reference/#clientonly for more information on client:only`},oe={name:"NoMatchingImport",title:"No import found for component.",message:e=>`Could not render \`${e}\`. No matching import has been found for \`${e}\`.`,hint:"Please make sure the component is properly imported."},se={name:"InvalidComponentArgs",title:"Invalid component arguments.",message:e=>`Invalid arguments passed to${e?` <${e}>`:""} component.`,hint:"Astro components cannot be rendered directly via function call, such as `Component()` or `{items.map(Component)}`."},ie={name:"AstroGlobUsedOutside",title:"Astro.glob() used outside of an Astro file.",message:e=>`\`Astro.glob(${e})\` can only be used in \`.astro\` files. \`import.meta.glob(${e})\` can be used instead to achieve a similar result.`,hint:"See Vite's documentation on `import.meta.glob` for more information: https://vite.dev/guide/features.html#glob-import"},ae={name:"AstroGlobNoMatch",title:"Astro.glob() did not match any files.",message:e=>`\`Astro.glob(${e})\` did not return any matching files.`,hint:"Check the pattern for typos."};function qe(e){return e.replace(/\r\n|\r(?!\n)|\n/g,`
`)}function Fe(e,t){if(!t||t.line===void 0||t.column===void 0)return"";const r=qe(e).split(`
`).map(a=>a.replace(/\t/g,"  ")),n=[];for(let a=-2;a<=2;a++)r[t.line+a]&&n.push(t.line+a);let o=0;for(const a of n){let i=`> ${a}`;i.length>o&&(o=i.length)}let s="";for(const a of n){const i=a===t.line-1;s+=i?"> ":"  ",s+=`${a+1} | ${r[a]}
`,i&&(s+=`${Array.from({length:o}).join(" ")}  | ${Array.from({length:t.column}).join(" ")}^
`)}return s}class j extends Error{loc;title;hint;frame;type="AstroError";constructor(t,r){const{name:n,title:o,message:s,stack:a,location:i,hint:l,frame:m}=t;super(s,r),this.title=o,this.name=n,s&&(this.message=s),this.stack=a||this.stack,this.loc=i,this.hint=l,this.frame=m}setLocation(t){this.loc=t}setName(t){this.name=t}setMessage(t){this.message=t}setHint(t){this.hint=t}setFrame(t,r){this.frame=Fe(t,r)}static is(t){return t.type==="AstroError"}}function Je(e){return!(e.length!==3||!e[0]||typeof e[0]!="object")}function ve(e,t,r){const n=t?.split("/").pop()?.replace(".astro","")??"",o=(...s)=>{if(!Je(s))throw new j({...se,message:se.message(n)});return e(...s)};return Object.defineProperty(o,"name",{value:n,writable:!1}),o.isAstroComponentFactory=!0,o.moduleId=t,o.propagation=r,o}function ze(e){return ve(e.factory,e.moduleId,e.propagation)}function Be(e,t,r){return typeof e=="function"?ve(e,t,r):ze(e)}const We="5.5.6";function Ge(){return t=>{if(console.warn(`Astro.glob is deprecated and will be removed in a future major version of Astro.
Use import.meta.glob instead: https://vitejs.dev/guide/features.html#glob-import`),typeof t=="string")throw new j({...ie,message:ie.message(JSON.stringify(t))});let r=[...Object.values(t)];if(r.length===0)throw new j({...ae,message:ae.message(JSON.stringify(t))});return Promise.all(r.map(n=>n()))}}function Ye(e){return{site:e?new URL(e):void 0,generator:`Astro v${We}`,glob:Ge()}}typeof process<"u"&&process.stdout&&process.stdout.isTTY;const{replace:Xe}="",Qe=/[&<>'"]/g,Ze={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},Ke=e=>Ze[e],et=e=>Xe.call(e,Qe,Ke);function _(e){return!!e&&typeof e=="object"&&"then"in e&&typeof e.then=="function"}const k=et;class H extends String{get[Symbol.toStringTag](){return"HTMLString"}}const g=e=>e instanceof H?e:typeof e=="string"?new H(e):e;function tt(e){return Object.prototype.toString.call(e)==="[object HTMLString]"}const Ae=Symbol.for("astro:render");function Y(e){return Object.defineProperty(e,Ae,{value:!0})}function rt(e){return e&&typeof e=="object"&&e[Ae]}function Se(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=Se(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function $e(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=Se(e))&&(n&&(n+=" "),n+=t);return n}const A={Value:0,JSON:1,RegExp:2,Date:3,Map:4,Set:5,BigInt:6,URL:7,Uint8Array:8,Uint16Array:9,Uint32Array:10,Infinity:11};function J(e,t={},r=new WeakSet){if(r.has(e))throw new Error(`Cyclic reference detected while serializing props for <${t.displayName} client:${t.hydrate}>!

Cyclic references cannot be safely serialized for client-side usage. Please remove the cyclic reference.`);r.add(e);const n=e.map(o=>Ee(o,t,r));return r.delete(e),n}function _e(e,t={},r=new WeakSet){if(r.has(e))throw new Error(`Cyclic reference detected while serializing props for <${t.displayName} client:${t.hydrate}>!

Cyclic references cannot be safely serialized for client-side usage. Please remove the cyclic reference.`);r.add(e);const n=Object.fromEntries(Object.entries(e).map(([o,s])=>[o,Ee(s,t,r)]));return r.delete(e),n}function Ee(e,t={},r=new WeakSet){switch(Object.prototype.toString.call(e)){case"[object Date]":return[A.Date,e.toISOString()];case"[object RegExp]":return[A.RegExp,e.source];case"[object Map]":return[A.Map,J(Array.from(e),t,r)];case"[object Set]":return[A.Set,J(Array.from(e),t,r)];case"[object BigInt]":return[A.BigInt,e.toString()];case"[object URL]":return[A.URL,e.toString()];case"[object Array]":return[A.JSON,J(e,t,r)];case"[object Uint8Array]":return[A.Uint8Array,Array.from(e)];case"[object Uint16Array]":return[A.Uint16Array,Array.from(e)];case"[object Uint32Array]":return[A.Uint32Array,Array.from(e)];default:return e!==null&&typeof e=="object"?[A.Value,_e(e,t,r)]:e===1/0?[A.Infinity,1]:e===-1/0?[A.Infinity,-1]:e===void 0?[A.Value]:[A.Value,e]}}function Ie(e,t){return JSON.stringify(_e(e,t))}const je=Object.freeze(["data-astro-transition-scope","data-astro-transition-persist","data-astro-transition-persist-props"]);function nt(e,t){let r={isPage:!1,hydration:null,props:{},propsWithoutTransitionAttributes:{}};for(const[n,o]of Object.entries(e))if(n.startsWith("server:")&&n==="server:root"&&(r.isPage=!0),n.startsWith("client:"))switch(r.hydration||(r.hydration={directive:"",value:"",componentUrl:"",componentExport:{value:""}}),n){case"client:component-path":{r.hydration.componentUrl=o;break}case"client:component-export":{r.hydration.componentExport.value=o;break}case"client:component-hydration":break;case"client:display-name":break;default:{if(r.hydration.directive=n.split(":")[1],r.hydration.value=o,!t.has(r.hydration.directive)){const s=Array.from(t.keys()).map(a=>`client:${a}`).join(", ");throw new Error(`Error: invalid hydration directive "${n}". Supported hydration methods: ${s}`)}if(r.hydration.directive==="media"&&typeof r.hydration.value!="string")throw new j(Ve);break}}else r.props[n]=o,je.includes(n)||(r.propsWithoutTransitionAttributes[n]=o);for(const n of Object.getOwnPropertySymbols(e))r.props[n]=e[n],r.propsWithoutTransitionAttributes[n]=e[n];return r}async function ot(e,t){const{renderer:r,result:n,astroId:o,props:s,attrs:a}=e,{hydrate:i,componentUrl:l,componentExport:m}=t;if(!m.value)throw new j({...oe,message:oe.message(t.displayName)});const p={children:"",props:{uid:o}};if(a)for(const[h,y]of Object.entries(a))p.props[h]=k(y);p.props["component-url"]=await n.resolve(decodeURI(l)),r.clientEntrypoint&&(p.props["component-export"]=m.value,p.props["renderer-url"]=await n.resolve(decodeURI(r.clientEntrypoint.toString())),p.props.props=k(Ie(s,t))),p.props.ssr="",p.props.client=i;let b=await n.resolve("astro:scripts/before-hydration.js");return b.length&&(p.props["before-hydration-url"]=b),p.props.opts=k(JSON.stringify({name:t.displayName,value:t.hydrateArgs||""})),je.forEach(h=>{typeof s[h]<"u"&&(p.props[h]=s[h])}),p}/**
 * shortdash - https://github.com/bibig/node-shorthash
 *
 * @license
 *
 * (The MIT License)
 *
 * Copyright (c) 2013 Bibig <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 */const X="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXY",z=X.length;function st(e){let t=0;if(e.length===0)return t;for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);t=(t<<5)-t+n,t=t&t}return t}function it(e){let t,r="",n=st(e);const o=n<0?"Z":"";for(n=Math.abs(n);n>=z;)t=n%z,n=Math.floor(n/z),r=X[t]+r;return n>0&&(r=X[n]+r),o+r}function at(e){return e==null?!1:e.isAstroComponentFactory===!0}function ct(e,t){let r=t.propagation||"none";return t.moduleId&&e.componentMetadata.has(t.moduleId)&&r==="none"&&(r=e.componentMetadata.get(t.moduleId).propagation),r==="in-tree"||r==="self"}const lt=Symbol.for("astro.headAndContent");function ft(e){return typeof e=="object"&&e!==null&&!!e[lt]}var ut='(()=>{var A=Object.defineProperty;var g=(i,o,a)=>o in i?A(i,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[o]=a;var d=(i,o,a)=>g(i,typeof o!="symbol"?o+"":o,a);{let i={0:t=>m(t),1:t=>a(t),2:t=>new RegExp(t),3:t=>new Date(t),4:t=>new Map(a(t)),5:t=>new Set(a(t)),6:t=>BigInt(t),7:t=>new URL(t),8:t=>new Uint8Array(t),9:t=>new Uint16Array(t),10:t=>new Uint32Array(t),11:t=>1/0*t},o=t=>{let[l,e]=t;return l in i?i[l](e):void 0},a=t=>t.map(o),m=t=>typeof t!="object"||t===null?t:Object.fromEntries(Object.entries(t).map(([l,e])=>[l,o(e)]));class y extends HTMLElement{constructor(){super(...arguments);d(this,"Component");d(this,"hydrator");d(this,"hydrate",async()=>{var b;if(!this.hydrator||!this.isConnected)return;let e=(b=this.parentElement)==null?void 0:b.closest("astro-island[ssr]");if(e){e.addEventListener("astro:hydrate",this.hydrate,{once:!0});return}let c=this.querySelectorAll("astro-slot"),n={},h=this.querySelectorAll("template[data-astro-template]");for(let r of h){let s=r.closest(this.tagName);s!=null&&s.isSameNode(this)&&(n[r.getAttribute("data-astro-template")||"default"]=r.innerHTML,r.remove())}for(let r of c){let s=r.closest(this.tagName);s!=null&&s.isSameNode(this)&&(n[r.getAttribute("name")||"default"]=r.innerHTML)}let p;try{p=this.hasAttribute("props")?m(JSON.parse(this.getAttribute("props"))):{}}catch(r){let s=this.getAttribute("component-url")||"<unknown>",v=this.getAttribute("component-export");throw v&&(s+=` (export ${v})`),console.error(`[hydrate] Error parsing props for component ${s}`,this.getAttribute("props"),r),r}let u;await this.hydrator(this)(this.Component,p,n,{client:this.getAttribute("client")}),this.removeAttribute("ssr"),this.dispatchEvent(new CustomEvent("astro:hydrate"))});d(this,"unmount",()=>{this.isConnected||this.dispatchEvent(new CustomEvent("astro:unmount"))})}disconnectedCallback(){document.removeEventListener("astro:after-swap",this.unmount),document.addEventListener("astro:after-swap",this.unmount,{once:!0})}connectedCallback(){if(!this.hasAttribute("await-children")||document.readyState==="interactive"||document.readyState==="complete")this.childrenConnectedCallback();else{let e=()=>{document.removeEventListener("DOMContentLoaded",e),c.disconnect(),this.childrenConnectedCallback()},c=new MutationObserver(()=>{var n;((n=this.lastChild)==null?void 0:n.nodeType)===Node.COMMENT_NODE&&this.lastChild.nodeValue==="astro:end"&&(this.lastChild.remove(),e())});c.observe(this,{childList:!0}),document.addEventListener("DOMContentLoaded",e)}}async childrenConnectedCallback(){let e=this.getAttribute("before-hydration-url");e&&await import(e),this.start()}async start(){let e=JSON.parse(this.getAttribute("opts")),c=this.getAttribute("client");if(Astro[c]===void 0){window.addEventListener(`astro:${c}`,()=>this.start(),{once:!0});return}try{await Astro[c](async()=>{let n=this.getAttribute("renderer-url"),[h,{default:p}]=await Promise.all([import(this.getAttribute("component-url")),n?import(n):()=>()=>{}]),u=this.getAttribute("component-export")||"default";if(!u.includes("."))this.Component=h[u];else{this.Component=h;for(let f of u.split("."))this.Component=this.Component[f]}return this.hydrator=p,this.hydrate},e,this)}catch(n){console.error(`[astro-island] Error hydrating ${this.getAttribute("component-url")}`,n)}}attributeChangedCallback(){this.hydrate()}}d(y,"observedAttributes",["props"]),customElements.get("astro-island")||customElements.define("astro-island",y)}})();';const dt="<style>astro-island,astro-slot,astro-static-slot{display:contents}</style>";function pt(e){return e._metadata.hasHydrationScript?!1:e._metadata.hasHydrationScript=!0}function ht(e,t){return e._metadata.hasDirectives.has(t)?!1:(e._metadata.hasDirectives.add(t),!0)}function ce(e,t){const n=e.clientDirectives.get(t);if(!n)throw new Error(`Unknown directive: ${t}`);return n}function mt(e,t,r){switch(t){case"both":return`${dt}<script>${ce(e,r)};${ut}<\/script>`;case"directive":return`<script>${ce(e,r)}<\/script>`}return""}const xe=/^(area|base|br|col|command|embed|hr|img|input|keygen|link|meta|param|source|track|wbr)$/i,yt=/^(?:allowfullscreen|async|autofocus|autoplay|checked|controls|default|defer|disabled|disablepictureinpicture|disableremoteplayback|formnovalidate|hidden|inert|loop|nomodule|novalidate|open|playsinline|readonly|required|reversed|scoped|seamless|selected|itemscope)$/i,gt=/&/g,bt=/"/g,wt=new Set(["set:html","set:text"]),vt=e=>e.trim().replace(/(?!^)\b\w|\s+|\W+/g,(t,r)=>/\W/.test(t)?"":r===0?t:t.toUpperCase()),C=(e,t=!0)=>t?String(e).replace(gt,"&#38;").replace(bt,"&#34;"):e,At=e=>e.toLowerCase()===e?e:e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`),le=e=>Object.entries(e).filter(([t,r])=>typeof r=="string"&&r.trim()||typeof r=="number").map(([t,r])=>t[0]!=="-"&&t[1]!=="-"?`${At(t)}:${r}`:`${t}:${r}`).join(";");function Ce(e){let t="";for(const[r,n]of Object.entries(e))t+=`const ${vt(r)} = ${JSON.stringify(n)?.replace(/<\/script>/g,"\\x3C/script>")};
`;return g(t)}function B(e){return e.length===1?e[0]:`${e.slice(0,-1).join(", ")} or ${e[e.length-1]}`}function Te(e,t,r=!0){if(e==null)return"";if(wt.has(t))return console.warn(`[astro] The "${t}" directive cannot be applied dynamically at runtime. It will not be rendered as an attribute.

Make sure to use the static attribute syntax (\`${t}={value}\`) instead of the dynamic spread syntax (\`{...{ "${t}": value }}\`).`),"";if(t==="class:list"){const n=C($e(e),r);return n===""?"":g(` ${t.slice(0,-5)}="${n}"`)}if(t==="style"&&!(e instanceof H)){if(Array.isArray(e)&&e.length===2)return g(` ${t}="${C(`${le(e[0])};${e[1]}`,r)}"`);if(typeof e=="object")return g(` ${t}="${C(le(e),r)}"`)}return t==="className"?g(` class="${C(e,r)}"`):typeof e=="string"&&e.includes("&")&&Et(e)?g(` ${t}="${C(e,!1)}"`):yt.test(t)?g(e?` ${t}`:""):g(e===""?` ${t}`:` ${t}="${C(e,r)}"`)}function Q(e,t=!0){let r="";for(const[n,o]of Object.entries(e))r+=Te(o,n,t);return g(r)}function O(e,{props:t,children:r=""},n=!0){const{lang:o,"data-astro-id":s,"define:vars":a,...i}=t;return a&&(e==="style"&&(delete i["is:global"],delete i["is:scoped"]),e==="script"&&(delete i.hoist,r=Ce(a)+`
`+r)),(r==null||r=="")&&xe.test(e)?`<${e}${Q(i,n)}>`:`<${e}${Q(i,n)}>${r}</${e}>`}const St=()=>{};class $t{chunks=[];renderPromise;destination;flushed=!1;constructor(t,r){this.destination=t,this.renderPromise=r(this),_(this.renderPromise)&&Promise.resolve(this.renderPromise).catch(St)}write(t){this.flushed?this.destination.write(t):this.chunks.push(t)}flush(){if(this.flushed)throw new Error("The render buffer has already been flushed.");this.flushed=!0;for(const t of this.chunks)this.destination.write(t);return this.renderPromise}}function Re(e,t){return new $t(e,t)}typeof process<"u"&&Object.prototype.toString.call(process);const _t=["http:","https:"];function Et(e){try{const t=new URL(e);return _t.includes(t.protocol)}catch{return!1}}const W=(e,t,r)=>{const n=JSON.stringify(e.props),o=e.children;return t===r.findIndex(s=>JSON.stringify(s.props)===n&&s.children==o)};function fe(e){e._metadata.hasRenderedHead=!0;const t=Array.from(e.styles).filter(W).map(s=>s.props.rel==="stylesheet"?O("link",s):O("style",s));e.styles.clear();const r=Array.from(e.scripts).filter(W).map(s=>(e.userAssetsBase&&(s.props.src=(e.base==="/"?"":e.base)+e.userAssetsBase+s.props.src),O("script",s,!1))),n=Array.from(e.links).filter(W).map(s=>O("link",s,!1));let o=t.join(`
`)+n.join(`
`)+r.join(`
`);if(e._metadata.extraHead.length>0)for(const s of e._metadata.extraHead)o+=s;return g(o)}function It(){return Y({type:"maybe-head"})}const Oe=Symbol.for("astro.renderTemplateResult");class jt{[Oe]=!0;htmlParts;expressions;error;constructor(t,r){this.htmlParts=t,this.error=void 0,this.expressions=r.map(n=>_(n)?Promise.resolve(n).catch(o=>{if(!this.error)throw this.error=o,o}):n)}render(t){const r=this.expressions.map(s=>Re(t,a=>{if(s||s===0)return E(a,s)}));let n=0;const o=()=>{for(;n<this.htmlParts.length;){const s=this.htmlParts[n],a=r[n];if(n++,s&&t.write(g(s)),a){const i=a.flush();if(_(i))return i.then(o)}}};return o()}}function xt(e){return typeof e=="object"&&e!==null&&!!e[Oe]}function Me(e,...t){return new jt(e,t)}const Z=Symbol.for("astro:slot-string");class K extends H{instructions;[Z];constructor(t,r){super(t),this.instructions=r,this[Z]=!0}}function Ct(e){return!!e[Z]}function Ne(e,t,r){return!t&&r?Ne(e,r):{async render(n){await E(n,typeof t=="function"?t(e):t)}}}async function M(e,t,r){let n="",o=null;const s={write(i){if(i instanceof K)n+=i,i.instructions&&(o??=[],o.push(...i.instructions));else{if(i instanceof Response)return;typeof i=="object"&&"type"in i&&typeof i.type=="string"?(o===null&&(o=[]),o.push(i)):n+=ee(e,i)}}};return await Ne(e,t,r).render(s),g(new K(n,o))}async function Ue(e,t={}){let r=null,n={};return t&&await Promise.all(Object.entries(t).map(([o,s])=>M(e,s).then(a=>{a.instructions&&(r===null&&(r=[]),r.push(...a.instructions)),n[o]=a}))),{slotInstructions:r,children:n}}const Tt=Symbol.for("astro:fragment"),ue=Symbol.for("astro:renderer");new TextEncoder;const Rt=new TextDecoder;function Le(e,t){if(rt(t)){const r=t;switch(r.type){case"directive":{const{hydration:n}=r;let o=n&&pt(e),s=n&&ht(e,n.directive),a=o?"both":s?"directive":null;if(a){let i=mt(e,a,n.directive);return g(i)}else return""}case"head":return e._metadata.hasRenderedHead||e.partial?"":fe(e);case"maybe-head":return e._metadata.hasRenderedHead||e._metadata.headInTree||e.partial?"":fe(e);case"renderer-hydration-script":{const{rendererSpecificHydrationScripts:n}=e._metadata,{rendererName:o}=r;return n.has(o)?"":(n.add(o),r.render())}default:throw new Error(`Unknown chunk type: ${t.type}`)}}else{if(t instanceof Response)return"";if(Ct(t)){let r="";const n=t;if(n.instructions)for(const o of n.instructions)r+=Le(e,o);return r+=t.toString(),r}}return t.toString()}function ee(e,t){return ArrayBuffer.isView(t)?Rt.decode(t):Le(e,t)}function Ot(e){return!!e&&typeof e=="object"&&"render"in e&&typeof e.render=="function"}function E(e,t){if(_(t))return t.then(r=>E(e,r));if(t instanceof K){e.write(t);return}if(tt(t)){e.write(t);return}if(Array.isArray(t))return Mt(e,t);if(typeof t=="function")return E(e,t());if(!(!t&&t!==0)){if(typeof t=="string"){e.write(g(k(t)));return}if(Ot(t)||xt(t)||Pt(t))return t.render(e);if(ArrayBuffer.isView(t)){e.write(t);return}if(typeof t=="object"&&(Symbol.asyncIterator in t||Symbol.iterator in t))return Symbol.asyncIterator in t?Ut(e,t):Nt(e,t);e.write(t)}}function Mt(e,t){const n=t.map(s=>Re(e,a=>E(a,s)))[Symbol.iterator](),o=()=>{for(;;){const{value:s,done:a}=n.next();if(a)break;const i=s.flush();if(_(i))return i.then(o)}};return o()}function Nt(e,t){const r=t[Symbol.iterator](),n=()=>{for(;;){const{value:o,done:s}=r.next();if(s)break;const a=E(e,o);if(_(a))return a.then(n)}};return n()}async function Ut(e,t){for await(const r of t)await E(e,r)}const ke=Symbol.for("astro.componentInstance");class Lt{[ke]=!0;result;props;slotValues;factory;returnValue;constructor(t,r,n,o){this.result=t,this.props=r,this.factory=o,this.slotValues={};for(const s in n){let a=!1,i=n[s](t);this.slotValues[s]=()=>a?n[s](t):(a=!0,i)}}init(t){return this.returnValue!==void 0?this.returnValue:(this.returnValue=this.factory(t,this.props,this.slotValues),_(this.returnValue)&&this.returnValue.then(r=>{this.returnValue=r}).catch(()=>{}),this.returnValue)}render(t){const r=this.init(this.result);return _(r)?r.then(n=>this.renderImpl(t,n)):this.renderImpl(t,r)}renderImpl(t,r){return ft(r)?r.content.render(t):E(t,r)}}function kt(e,t){if(e!=null)for(const r of Object.keys(e))r.startsWith("client:")&&console.warn(`You are attempting to render <${t} ${r} />, but ${t} is an Astro component. Astro components do not render in the client and should not have a hydration directive. Please use a framework component for client rendering.`)}function Ht(e,t,r,n,o={}){kt(n,t);const s=new Lt(e,n,o,r);return ct(e,r)&&e._metadata.propagators.add(s),s}function Pt(e){return typeof e=="object"&&e!==null&&!!e[ke]}function Dt(e){return typeof HTMLElement<"u"&&HTMLElement.isPrototypeOf(e)}async function Vt(e,t,r,n){const o=qt(t);let s="";for(const a in r)s+=` ${a}="${C(await r[a])}"`;return g(`<${o}${s}>${await M(e,n?.default)}</${o}>`)}function qt(e){const t=customElements.getName(e);return t||e.name.replace(/^HTML|Element$/g,"").replace(/[A-Z]/g,"-$&").toLowerCase().replace(/^-/,"html-")}function Ft(e){let t="";for(let r=0;r<e.length;r++)t+=de[e[r]>>4],t+=de[e[r]&15];return t}const de="0123456789ABCDEF";var pe;(function(e){e[e.Include=0]="Include",e[e.None=1]="None"})(pe||(pe={}));var he;(function(e){e[e.Required=0]="Required",e[e.Ignore=1]="Ignore"})(he||(he={}));function Jt(e){return zt(e,Bt,P.Include)}function zt(e,t,r){let n="";for(let o=0;o<e.byteLength;o+=3){let s=0,a=0;for(let i=0;i<3&&o+i<e.byteLength;i++)s=s<<8|e[o+i],a+=8;for(let i=0;i<4;i++)a>=6?(n+=t[s>>a-6&63],a-=6):a>0?(n+=t[s<<6-a&63],a=0):r===P.Include&&(n+="=")}return n}const Bt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var P;(function(e){e[e.Include=0]="Include",e[e.None=1]="None"})(P||(P={}));var me;(function(e){e[e.Required=0]="Required",e[e.Ignore=1]="Ignore"})(me||(me={}));const Wt="AES-GCM",Gt=new TextEncoder;new TextDecoder;const Yt=24;async function Xt(e,t){const r=crypto.getRandomValues(new Uint8Array(Yt/2)),n=Gt.encode(t),o=await crypto.subtle.encrypt({name:Wt,iv:r},e,n);return Ft(r)+Jt(new Uint8Array(o))}const Qt=new Set(["server:component-path","server:component-export","server:component-directive","server:defer"]);function Zt(e){return"server:component-directive"in e}const Kt=/<\/script/giu,er=/<!--/gu,tr="<\\/script",rr="\\u003C!--";function U(e){return JSON.stringify(e).replace(Kt,tr).replace(er,rr)}function nr(e,t,r){const n=new URLSearchParams;return n.set("e",e),n.set("p",t),n.set("s",r),n}function or(e,t){return(e+"?"+t.toString()).length<2048}function sr(e,t,r,n){return{async render(o){const s=r["server:component-path"],a=r["server:component-export"],i=e.serverIslandNameMap.get(s);if(!i)throw new Error("Could not find server component name");for(const d of Object.keys(r))Qt.has(d)&&delete r[d];o.write("<!--[if astro]>server-island-start<![endif]-->");const l={};for(const d in n)if(d!=="fallback"){const v=await M(e,n[d]);l[d]=v.toString()}else await E(o,n.fallback(e));const m=await e.key,p=Object.keys(r).length===0?"":await Xt(m,JSON.stringify(r)),b=crypto.randomUUID(),h=e.base.endsWith("/")?"":"/";let y=`${e.base}${h}_server-islands/${i}${e.trailingSlash==="always"?"/":""}`;const S=nr(a,p,U(l)),$=or(y,S);$&&(y+="?"+S.toString(),o.write(`<link rel="preload" as="fetch" href="${y}" crossorigin="anonymous">`)),o.write(`<script async type="module" data-astro-rerun data-island-id="${b}">
let script = document.querySelector('script[data-island-id="${b}"]');

${$?`let response = await fetch('${y}');
`:`let data = {
	componentExport: ${U(a)},
	encryptedProps: ${U(p)},
	slots: ${U(l)},
};

let response = await fetch('${y}', {
	method: 'POST',
	body: JSON.stringify(data),
});
`}
if (script) {
	if(
		response.status === 200
		&& response.headers.has('content-type')
		&& response.headers.get('content-type').split(";")[0].trim() === 'text/html') {
		let html = await response.text();

		// Swap!
		while(script.previousSibling &&
			script.previousSibling.nodeType !== 8 &&
			script.previousSibling.data !== '[if astro]>server-island-start<![endif]') {
			script.previousSibling.remove();
		}
		script.previousSibling?.remove();

		let frag = document.createRange().createContextualFragment(html);
		script.before(frag);
	}
	script.remove(); // Prior to v5.4.2, this was the trick to force rerun of scripts.  Keeping it to minimize change to the existing behavior.
}
<\/script>`)}}}const L=new Map([["solid","solid-js"]]),ye=new Set(["solid-js","react","preact","vue","svelte"]);function ir(e){switch(e?.split(".").pop()){case"svelte":return["@astrojs/svelte"];case"vue":return["@astrojs/vue"];case"jsx":case"tsx":return["@astrojs/react","@astrojs/preact","@astrojs/solid-js","@astrojs/vue (jsx)"];case void 0:default:return["@astrojs/react","@astrojs/preact","@astrojs/solid-js","@astrojs/vue","@astrojs/svelte"]}}function ar(e){return e===Tt}function cr(e){return e&&e["astro:html"]===!0}const lr=/<\/?astro-slot\b[^>]*>/g,fr=/<\/?astro-static-slot\b[^>]*>/g;function ur(e,t=!0){const r=t?fr:lr;return e.replace(r,"")}async function dr(e,t,r,n,o={}){if(!r&&!("client:only"in n))throw new Error(`Unable to render ${t} because it is ${r}!
Did you forget to import the component or is it possible there is a typo?`);const{renderers:s,clientDirectives:a}=e,i={astroStaticSlot:!0,displayName:t},{hydration:l,isPage:m,props:p,propsWithoutTransitionAttributes:b}=nt(n,a);let h="",y;l&&(i.hydrate=l.directive,i.hydrateArgs=l.value,i.componentExport=l.componentExport,i.componentUrl=l.componentUrl);const S=ir(i.componentUrl),$=s.filter(c=>c.name!=="astro:jsx"),{children:d,slotInstructions:v}=await Ue(e,o);let f;if(i.hydrate!=="only"){let c=!1;try{c=r&&r[ue]}catch{}if(c){const u=r[ue];f=s.find(({name:w})=>w===u)}if(!f){let u;for(const w of s)try{if(await w.ssr.check.call({result:e},r,p,d)){f=w;break}}catch(V){u??=V}if(!f&&u)throw u}if(!f&&typeof HTMLElement=="function"&&Dt(r)){const u=await Vt(e,r,n,o);return{render(w){w.write(u)}}}}else{if(i.hydrateArgs){const c=L.has(i.hydrateArgs)?L.get(i.hydrateArgs):i.hydrateArgs;ye.has(c)&&(f=s.find(({name:u})=>u===`@astrojs/${c}`||u===c))}if(!f&&$.length===1&&(f=$[0]),!f){const c=i.componentUrl?.split(".").pop();f=s.find(({name:u})=>u===`@astrojs/${c}`||u===c)}}if(f)i.hydrate==="only"?h=await M(e,o?.fallback):(performance.now(),{html:h,attrs:y}=await f.ssr.renderToStaticMarkup.call({result:e},r,b,d,i));else if(i.hydrate==="only"){const c=L.has(i.hydrateArgs)?L.get(i.hydrateArgs):i.hydrateArgs;if(ye.has(c)){const u=$.length>1;throw new j({...T,message:T.message(i.displayName,i?.componentUrl?.split(".").pop(),u,$.length),hint:T.hint(B(S.map(w=>"`"+w+"`")))})}else throw new j({...F,message:F.message(i.displayName),hint:F.hint(S.map(u=>u.replace("@astrojs/","")).join("|"))})}else if(typeof r!="string"){const c=$.filter(w=>S.includes(w.name)),u=$.length>1;if(c.length===0)throw new j({...T,message:T.message(i.displayName,i?.componentUrl?.split(".").pop(),u,$.length),hint:T.hint(B(S.map(w=>"`"+w+"`")))});if(c.length===1)f=c[0],{html:h,attrs:y}=await f.ssr.renderToStaticMarkup.call({result:e},r,b,d,i);else throw new Error(`Unable to render ${i.displayName}!

This component likely uses ${B(S)},
but Astro encountered an error during server-side rendering.

Please ensure that ${i.displayName}:
1. Does not unconditionally access browser-specific globals like \`window\` or \`document\`.
   If this is unavoidable, use the \`client:only\` hydration directive.
2. Does not conditionally return \`null\` or \`undefined\` when rendered on the server.

If you're still stuck, please open an issue on GitHub or join us at https://astro.build/chat.`)}if(!h&&typeof r=="string"){const c=pr(r),u=Object.values(d).join(""),w=Me`<${c}${Q(p)}${g(u===""&&xe.test(c)?"/>":`>${u}</${c}>`)}`;h="";const V={write(te){te instanceof Response||(h+=ee(e,te))}};await w.render(V)}if(!l)return{render(c){if(v)for(const u of v)c.write(u);m||f?.name==="astro:jsx"?c.write(h):h&&h.length>0&&c.write(g(ur(h,f?.ssr?.supportsAstroStaticSlot)))}};const N=it(`<!--${i.componentExport.value}:${i.componentUrl}-->
${h}
${Ie(p,i)}`),x=await ot({renderer:f,result:e,astroId:N,props:p,attrs:y},i);let I=[];if(h){if(Object.keys(d).length>0)for(const c of Object.keys(d)){let u=f?.ssr?.supportsAstroStaticSlot?i.hydrate?"astro-slot":"astro-static-slot":"astro-slot",w=c==="default"?`<${u}>`:`<${u} name="${c}">`;h.includes(w)||I.push(c)}}else I=Object.keys(d);const D=I.length>0?I.map(c=>`<template data-astro-template${c!=="default"?`="${c}"`:""}>${d[c]}</template>`).join(""):"";return x.children=`${h??""}${D}`,x.children&&(x.props["await-children"]="",x.children+="<!--astro:end-->"),{render(c){if(v)for(const w of v)c.write(w);c.write(Y({type:"directive",hydration:l})),l.directive!=="only"&&f?.ssr.renderHydrationScript&&c.write(Y({type:"renderer-hydration-script",rendererName:f.name,render:f.ssr.renderHydrationScript}));const u=O("astro-island",x,!1);c.write(g(u))}}}function pr(e){const t=/[&<>'"\s]+/;return t.test(e)?e.trim().split(t)[0].trim():e}async function hr(e,t={}){const r=await M(e,t?.default);return{render(n){r!=null&&n.write(r)}}}async function mr(e,t,r,n={}){const{slotInstructions:o,children:s}=await Ue(e,n),a=t({slots:s}),i=o?o.map(l=>ee(e,l)).join(""):"";return{render(l){l.write(g(i+a))}}}function yr(e,t,r,n,o={}){if(Zt(n))return sr(e,t,n,o);const s=Ht(e,t,r,n,o);return{render(a){return s.render(a)}}}function gr(e,t,r,n,o={}){if(_(r))return r.catch(s).then(a=>gr(e,t,a,n,o));if(ar(r))return hr(e,o).catch(s);if(n=br(n),cr(r))return mr(e,r,n,o).catch(s);if(at(r))return yr(e,t,r,n,o);return dr(e,t,r,n,o).catch(s);function s(a){if(e.cancelled)return{render(){}};throw a}}function br(e){if(e["class:list"]!==void 0){const t=e["class:list"];delete e["class:list"],e.class=$e(e.class,t),e.class===""&&delete e.class}return e}/*! https://mths.be/cssesc v3.0.0 by @mathias */var G,ge;function wr(){if(ge)return G;ge=1;var e={},t=e.hasOwnProperty,r=function(l,m){if(!l)return m;var p={};for(var b in m)p[b]=t.call(l,b)?l[b]:m[b];return p},n=/[ -,\.\/:-@\[-\^`\{-~]/,o=/[ -,\.\/:-@\[\]\^`\{-~]/,s=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,a=function i(l,m){m=r(m,i.options),m.quotes!="single"&&m.quotes!="double"&&(m.quotes="single");for(var p=m.quotes=="double"?'"':"'",b=m.isIdentifier,h=l.charAt(0),y="",S=0,$=l.length;S<$;){var d=l.charAt(S++),v=d.charCodeAt(),f=void 0;if(v<32||v>126){if(v>=55296&&v<=56319&&S<$){var N=l.charCodeAt(S++);(N&64512)==56320?v=((v&1023)<<10)+(N&1023)+65536:S--}f="\\"+v.toString(16).toUpperCase()+" "}else m.escapeEverything?n.test(d)?f="\\"+d:f="\\"+v.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(d)?f="\\"+v.toString(16).toUpperCase()+" ":d=="\\"||!b&&(d=='"'&&p==d||d=="'"&&p==d)||b&&o.test(d)?f="\\"+d:f=d;y+=f}return b&&(/^-[-\d]/.test(y)?y="\\-"+y.slice(1):/\d/.test(h)&&(y="\\3"+h+" "+y.slice(1))),y=y.replace(s,function(x,I,D){return I&&I.length%2?x:(I||"")+D}),!b&&m.wrap?p+y+p:y};return a.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},a.version="3.0.0",G=a,G}wr();"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_".split("").reduce((e,t)=>(e[t.charCodeAt(0)]=t,e),[]);"-0123456789_".split("").reduce((e,t)=>(e[t.charCodeAt(0)]=t,e),[]);var be=Object.freeze,vr=Object.defineProperty,Ar=(e,t)=>be(vr(e,"raw",{value:be(t||e.slice())})),we;const Sr=Ye("https://quer.us"),$r=Be((e,t,r)=>{const n=e.createAstro(Sr,t,r);n.self=$r;const{component:o,id:s,...a}=n.props,i=s||He();return Me(we||(we=Ar(["","<div","></div> <script>(function(){",`
  /**
   * Store the id and the props for the Astro component in order to mount the correct UI component once clerk is loaded.
   * The above is handled by \`mountAllClerkAstroJSComponents\`.
   */
  const setOrCreatePropMap = ({ category, id, props }) => {
    if (!window.__astro_clerk_component_props) {
      window.__astro_clerk_component_props = new Map();
    }

    if (!window.__astro_clerk_component_props.has(category)) {
      const _ = new Map();
      _.set(id, props);
      window.__astro_clerk_component_props.set(category, _);
    }

    window.__astro_clerk_component_props.get(category)?.set(id, props);
  };

  setOrCreatePropMap({
    category: component,
    id: \`clerk-\${component}-\${safeId}\`,
    props,
  });
})();<\/script>`],["","<div","></div> <script>(function(){",`
  /**
   * Store the id and the props for the Astro component in order to mount the correct UI component once clerk is loaded.
   * The above is handled by \\\`mountAllClerkAstroJSComponents\\\`.
   */
  const setOrCreatePropMap = ({ category, id, props }) => {
    if (!window.__astro_clerk_component_props) {
      window.__astro_clerk_component_props = new Map();
    }

    if (!window.__astro_clerk_component_props.has(category)) {
      const _ = new Map();
      _.set(id, props);
      window.__astro_clerk_component_props.set(category, _);
    }

    window.__astro_clerk_component_props.get(category)?.set(id, props);
  };

  setOrCreatePropMap({
    category: component,
    id: \\\`clerk-\\\${component}-\\\${safeId}\\\`,
    props,
  });
})();<\/script>`])),It(),Te(`clerk-${o}-${i}`,"data-clerk-id"),Ce({props:a,component:o,safeId:i}))},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/InternalUIComponentRenderer.astro",void 0);export{$r as $,Ye as a,Me as b,Be as c,Ne as d,Ce as e,Er as j,g as m,gr as r};

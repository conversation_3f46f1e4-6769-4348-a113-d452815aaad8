const b="data-astro-transition-persist",P=new Set;function F(t){const e=t.src?new URL(t.src,location.href).href:t.textContent;return P.has(e)?!0:(P.add(e),!1)}function O(t){for(const e of t.scripts)!e.hasAttribute("data-astro-rerun")&&F(e)&&(e.dataset.astroExec="")}function M(t){const e=document.documentElement,n=[...e.attributes].filter(({name:o})=>(e.removeAttribute(o),o.startsWith("data-astro-")));[...t.documentElement.attributes,...n].forEach(({name:o,value:r})=>e.setAttribute(o,r))}function H(t){for(const e of Array.from(document.head.children)){const n=V(e,t);n?n.remove():e.remove()}document.head.append(...t.head.children)}function X(t,e){e.replaceWith(t);for(const n of e.querySelectorAll(`[${b}]`)){const o=n.getAttribute(b),r=t.querySelector(`[${b}="${o}"]`);r&&(r.replaceWith(n),r.localName==="astro-island"&&K(n)&&!z(n,r)&&(n.setAttribute("ssr",""),n.setAttribute("props",r.getAttribute("props"))))}}const Y=()=>{const t=document.activeElement;if(t?.closest(`[${b}]`)){if(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement){const e=t.selectionStart,n=t.selectionEnd;return()=>E({activeElement:t,start:e,end:n})}return()=>E({activeElement:t})}else return()=>E({activeElement:null})},E=({activeElement:t,start:e,end:n})=>{t&&(t.focus(),(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement)&&(typeof e=="number"&&(t.selectionStart=e),typeof n=="number"&&(t.selectionEnd=n)))},V=(t,e)=>{const n=t.getAttribute(b),o=n&&e.head.querySelector(`[${b}="${n}"]`);if(o)return o;if(t.matches("link[rel=stylesheet]")){const r=t.getAttribute("href");return e.head.querySelector(`link[rel=stylesheet][href="${r}"]`)}return null},K=t=>{const e=t.dataset.astroTransitionPersistProps;return e==null||e==="false"},z=(t,e)=>t.getAttribute("props")===e.getAttribute("props"),mt={deselectScripts:O,swapRootAttributes:M,swapHeadElements:H,swapBodyElement:X,saveFocus:Y},G=t=>{O(t),M(t),H(t);const e=Y();X(t.body,document.body),e()},J="astro:before-preparation",Q="astro:after-preparation",Z="astro:before-swap",tt="astro:after-swap",et=t=>document.dispatchEvent(new Event(t));class _ extends Event{from;to;direction;navigationType;sourceElement;info;newDocument;signal;constructor(e,n,o,r,i,u,a,l,d,c){super(e,n),this.from=o,this.to=r,this.direction=i,this.navigationType=u,this.sourceElement=a,this.info=l,this.newDocument=d,this.signal=c,Object.defineProperties(this,{from:{enumerable:!0},to:{enumerable:!0,writable:!0},direction:{enumerable:!0,writable:!0},navigationType:{enumerable:!0},sourceElement:{enumerable:!0},info:{enumerable:!0},newDocument:{enumerable:!0,writable:!0},signal:{enumerable:!0}})}}class nt extends _{formData;loader;constructor(e,n,o,r,i,u,a,l,d,c){super(J,{cancelable:!0},e,n,o,r,i,u,a,l),this.formData=d,this.loader=c.bind(this,this),Object.defineProperties(this,{formData:{enumerable:!0},loader:{enumerable:!0,writable:!0}})}}class ot extends _{direction;viewTransition;swap;constructor(e,n){super(Z,void 0,e.from,e.to,e.direction,e.navigationType,e.sourceElement,e.info,e.newDocument,e.signal),this.direction=e.direction,this.viewTransition=n,this.swap=()=>G(this.newDocument),Object.defineProperties(this,{direction:{enumerable:!0},viewTransition:{enumerable:!0},swap:{enumerable:!0,writable:!0}})}}async function rt(t,e,n,o,r,i,u,a,l){const d=new nt(t,e,n,o,r,i,window.document,u,a,l);return document.dispatchEvent(d)&&(await d.loader(),d.defaultPrevented||(et(Q),d.navigationType!=="traverse"&&R({scrollX,scrollY}))),d}function st(t,e){const n=new ot(t,e);return document.dispatchEvent(n),n.swap(),n}const it=history.pushState.bind(history),v=history.replaceState.bind(history),R=t=>{history.state&&(history.scrollRestoration="manual",v({...history.state,...t},""))},C=!!document.startViewTransition,x=()=>!!document.querySelector('[name="astro-view-transitions-enabled"]'),$=(t,e)=>t.pathname===e.pathname&&t.search===e.search;let f,p,g;const q=t=>document.dispatchEvent(new Event(t)),B=()=>q("astro:page-load"),at=()=>{let t=document.createElement("div");t.setAttribute("aria-live","assertive"),t.setAttribute("aria-atomic","true"),t.className="astro-route-announcer",document.body.append(t),setTimeout(()=>{let e=document.title||document.querySelector("h1")?.textContent||location.pathname;t.textContent=e},60)},D="data-astro-transition-persist",I="data-astro-transition",S="data-astro-transition-fallback";let L,T=0;history.state?(T=history.state.index,scrollTo({left:history.state.scrollX,top:history.state.scrollY})):x()&&(v({index:T,scrollX,scrollY},""),history.scrollRestoration="manual");async function ct(t,e){try{const n=await fetch(t,e),r=(n.headers.get("content-type")??"").split(";",1)[0].trim();return r!=="text/html"&&r!=="application/xhtml+xml"?null:{html:await n.text(),redirected:n.redirected?n.url:void 0,mediaType:r}}catch{return null}}function W(){const t=document.querySelector('[name="astro-view-transitions-fallback"]');return t?t.getAttribute("content"):"animate"}function lt(){let t=Promise.resolve(),e=!1;for(const n of document.getElementsByTagName("script"))n.dataset.astroExec===void 0&&n.getAttribute("type")==="module"&&(e=n.getAttribute("src")===null);e&&document.body.insertAdjacentHTML("beforeend",'<script type="module" src="data:application/javascript,"/>');for(const n of document.getElementsByTagName("script")){if(n.dataset.astroExec==="")continue;const o=n.getAttribute("type");if(o&&o!=="module"&&o!=="text/javascript")continue;const r=document.createElement("script");r.innerHTML=n.innerHTML;for(const i of n.attributes){if(i.name==="src"){const u=new Promise(a=>{r.onload=r.onerror=a});t=t.then(()=>u)}r.setAttribute(i.name,i.value)}r.dataset.astroExec="",n.replaceWith(r)}return t}const j=(t,e,n,o,r)=>{const i=$(e,t),u=document.title;document.title=o;let a=!1;if(t.href!==location.href&&!r)if(n.history==="replace"){const l=history.state;v({...n.state,index:l.index,scrollX:l.scrollX,scrollY:l.scrollY},"",t.href)}else it({...n.state,index:++T,scrollX:0,scrollY:0},"",t.href);if(document.title=u,g=t,i||(scrollTo({left:0,top:0,behavior:"instant"}),a=!0),r)scrollTo(r.scrollX,r.scrollY);else{if(t.hash){history.scrollRestoration="auto";const l=history.state;location.href=t.href,history.state||(v(l,""),i&&window.dispatchEvent(new PopStateEvent("popstate")))}else a||scrollTo({left:0,top:0,behavior:"instant"});history.scrollRestoration="manual"}};function ut(t){const e=[];for(const n of t.querySelectorAll("head link[rel=stylesheet]"))if(!document.querySelector(`[${D}="${n.getAttribute(D)}"], link[rel=stylesheet][href="${n.getAttribute("href")}"]`)){const o=document.createElement("link");o.setAttribute("rel","preload"),o.setAttribute("as","style"),o.setAttribute("href",n.getAttribute("href")),e.push(new Promise(r=>{["load","error"].forEach(i=>o.addEventListener(i,r)),document.head.append(o)}))}return e}async function k(t,e,n,o,r){async function i(l){function d(h){const m=h.effect;return!m||!(m instanceof KeyframeEffect)||!m.target?!1:window.getComputedStyle(m.target,m.pseudoElement).animationIterationCount==="infinite"}const c=document.getAnimations();document.documentElement.setAttribute(S,l);const w=document.getAnimations().filter(h=>!c.includes(h)&&!d(h));return Promise.allSettled(w.map(h=>h.finished))}if(r==="animate"&&!n.transitionSkipped&&!t.signal.aborted)try{await i("old")}catch{}const u=document.title,a=st(t,n.viewTransition);j(a.to,a.from,e,u,o),q(tt),r==="animate"&&(!n.transitionSkipped&&!a.signal.aborted?i("new").finally(()=>n.viewTransitionFinished()):n.viewTransitionFinished())}function dt(){return f?.controller.abort(),f={controller:new AbortController}}async function U(t,e,n,o,r){const i=dt();if(!x()||location.origin!==n.origin){i===f&&(f=void 0),location.href=n.href;return}const u=r?"traverse":o.history==="replace"?"replace":"push";if(u!=="traverse"&&R({scrollX,scrollY}),$(e,n)&&(t!=="back"&&n.hash||t==="back"&&e.hash)){j(n,e,o,document.title,r),i===f&&(f=void 0);return}const a=await rt(e,n,t,u,o.sourceElement,o.info,i.controller.signal,o.formData,l);if(a.defaultPrevented||a.signal.aborted){i===f&&(f=void 0),a.signal.aborted||(location.href=n.href);return}async function l(s){const w=s.to.href,h={signal:s.signal};if(s.formData){h.method="POST";const y=s.sourceElement instanceof HTMLFormElement?s.sourceElement:s.sourceElement instanceof HTMLElement&&"form"in s.sourceElement?s.sourceElement.form:s.sourceElement?.closest("form");h.body=e!==void 0&&Reflect.get(HTMLFormElement.prototype,"attributes",y).getNamedItem("enctype")?.value==="application/x-www-form-urlencoded"?new URLSearchParams(s.formData):s.formData}const m=await ct(w,h);if(m===null){s.preventDefault();return}if(m.redirected){const y=new URL(m.redirected);if(y.origin!==s.to.origin){s.preventDefault();return}s.to=y}if(L??=new DOMParser,s.newDocument=L.parseFromString(m.html,m.mediaType),s.newDocument.querySelectorAll("noscript").forEach(y=>y.remove()),!s.newDocument.querySelector('[name="astro-view-transitions-enabled"]')&&!s.formData){s.preventDefault();return}const A=ut(s.newDocument);A.length&&!s.signal.aborted&&await Promise.all(A)}async function d(){if(p&&p.viewTransition){try{p.viewTransition.skipTransition()}catch{}try{await p.viewTransition.updateCallbackDone}catch{}}return p={transitionSkipped:!1}}const c=await d();if(a.signal.aborted){i===f&&(f=void 0);return}if(document.documentElement.setAttribute(I,a.direction),C)c.viewTransition=document.startViewTransition(async()=>await k(a,o,c,r));else{const s=(async()=>{await Promise.resolve(),await k(a,o,c,r,W())})();c.viewTransition={updateCallbackDone:s,ready:s,finished:new Promise(w=>c.viewTransitionFinished=w),skipTransition:()=>{c.transitionSkipped=!0,document.documentElement.removeAttribute(S)},types:new Set}}c.viewTransition?.updateCallbackDone.finally(async()=>{await lt(),B(),at()}),c.viewTransition?.finished.finally(()=>{c.viewTransition=void 0,c===p&&(p=void 0),i===f&&(f=void 0),document.documentElement.removeAttribute(I),document.documentElement.removeAttribute(S)});try{await c.viewTransition?.updateCallbackDone}catch(s){const w=s;console.log("[astro]",w.name,w.message,w.stack)}}async function ht(t,e){await U("forward",g,new URL(t,location.href),e??{})}function ft(t){if(!x()&&t.state){location.reload();return}if(t.state===null)return;const e=history.state,n=e.index,o=n>T?"forward":"back";T=n,U(o,g,new URL(location.href),{},e)}const N=()=>{history.state&&(scrollX!==history.state.scrollX||scrollY!==history.state.scrollY)&&R({scrollX,scrollY})};{if(C||W()!=="none")if(g=new URL(location.href),addEventListener("popstate",ft),addEventListener("load",B),"onscrollend"in window)addEventListener("scrollend",N);else{let t,e,n,o;const r=()=>{if(o!==history.state?.index){clearInterval(t),t=void 0;return}if(e===scrollY&&n===scrollX){clearInterval(t),t=void 0,N();return}else e=scrollY,n=scrollX};addEventListener("scroll",()=>{t===void 0&&(o=history.state?.index,e=scrollY,n=scrollX,t=window.setInterval(r,50))},{passive:!0})}for(const t of document.getElementsByTagName("script"))F(t),t.dataset.astroExec=""}export{Q as TRANSITION_AFTER_PREPARATION,tt as TRANSITION_AFTER_SWAP,J as TRANSITION_BEFORE_PREPARATION,Z as TRANSITION_BEFORE_SWAP,nt as TransitionBeforePreparationEvent,ot as TransitionBeforeSwapEvent,ht as navigate,C as supportsViewTransitions,mt as swapFunctions,x as transitionEnabledOnThisPage};

var q=(e,r,t)=>!e&&t?Q(t):X(r),Q=e=>{const r=e.userId,t=e.user,n=e.sessionId,i=e.sessionStatus,s=e.session,o=e.organization,u=e.orgId,l=e.orgRole,d=e.orgPermissions,a=e.orgSlug,c=e.actor,p=e.factorVerificationAge;return{userId:r,user:t,sessionId:n,session:s,sessionStatus:i,organization:o,orgId:u,orgRole:l,orgPermissions:d,orgSlug:a,actor:c,factorVerificationAge:p}},X=e=>{const r=e.user?e.user.id:e.user,t=e.user,n=e.session?e.session.id:e.session,i=e.session,s=e.session?.status,o=e.session?e.session.factorVerificationAge:null,u=i?.actor,l=e.organization,d=e.organization?e.organization.id:e.organization,a=l?.slug,c=l&&t?.organizationMemberships?.find(W=>W.organization.id===d),p=c&&c.permissions,Y=c&&c.role;return{userId:r,user:t,sessionId:n,session:i,sessionStatus:s,organization:l,orgId:d,orgRole:Y,orgSlug:a,orgPermissions:p,actor:u,factorVerificationAge:o}};function I(e){return!1}var L=e=>typeof atob<"u"&&typeof atob=="function"?atob(e):typeof global<"u"&&global.Buffer?new global.Buffer(e,"base64").toString():e,G=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],x="pk_live_",j="pk_test_";function U(e,r={}){if(e=e||"",!e||!P(e)){if(r.fatal&&!e)throw new Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(r.fatal&&!P(e))throw new Error("Publishable key not valid.");return null}const t=e.startsWith(x)?"production":"development";let n=L(e.split("_")[2]);return n=n.slice(0,-1),r.proxyUrl?n=r.proxyUrl:t!=="development"&&r.domain&&(n=`clerk.${r.domain}`),{instanceType:t,frontendApi:n}}function P(e=""){try{const r=e.startsWith(x)||e.startsWith(j),t=L(e.split("_")[2]||"").endsWith("$");return r&&t}catch{return!1}}function Z(){const e=new Map;return{isDevOrStagingUrl:r=>{if(!r)return!1;const t=typeof r=="string"?r:r.hostname;let n=e.get(t);return n===void 0&&(n=G.some(i=>t.endsWith(i)),e.set(t,n)),n}}}var ee="METHOD_CALLED";function re(e,r){return{event:ee,payload:{method:e,...r}}}let f=[],h=0;const y=4;let E=0,k=e=>{let r=[],t={get(){return t.lc||t.listen(()=>{})(),t.value},lc:0,listen(n){return t.lc=r.push(n),()=>{for(let s=h+y;s<f.length;)f[s]===n?f.splice(s,y):s+=y;let i=r.indexOf(n);~i&&(r.splice(i,1),--t.lc||t.off())}},notify(n,i){E++;let s=!f.length;for(let o of r)f.push(o,t.value,n,i);if(s){for(h=0;h<f.length;h+=y)f[h](f[h+1],f[h+2],f[h+3]);f.length=0}},off(){},set(n){let i=t.value;i!==n&&(t.value=n,t.notify(i))},subscribe(n){let i=t.listen(n);return n(t.value),i},value:e};return t};const te=5,b=6,w=10;let ne=(e,r,t,n)=>(e.events=e.events||{},e.events[t+w]||(e.events[t+w]=n(i=>{e.events[t].reduceRight((s,o)=>(o(s),s),{shared:{},...i})})),e.events[t]=e.events[t]||[],e.events[t].push(r),()=>{let i=e.events[t],s=i.indexOf(r);i.splice(s,1),i.length||(delete e.events[t],e.events[t+w](),delete e.events[t+w])}),ie=1e3,D=(e,r)=>ne(e,n=>{let i=r(n);i&&e.events[b].push(i)},te,n=>{let i=e.listen;e.listen=(...o)=>(!e.lc&&!e.active&&(e.active=!0,n()),i(...o));let s=e.off;return e.events[b]=[],e.off=()=>{s(),setTimeout(()=>{if(e.active&&!e.lc){e.active=!1;for(let o of e.events[b])o();e.events[b]=[]}},ie)},()=>{e.listen=i,e.off=s}}),z=(e,r,t)=>{Array.isArray(e)||(e=[e]);let n,i,s=()=>{if(i===E)return;i=E;let a=e.map(c=>c.get());if(!n||a.some((c,p)=>c!==n[p])){n=a;let c=r(...a);c&&c.then&&c.t?c.then(p=>{n===a&&o.set(p)}):(o.set(c),i=E)}},o=k(void 0),u=o.get;o.get=()=>(s(),u());let l,d=t?()=>{clearTimeout(l),l=setTimeout(s)}:s;return D(o,()=>{let a=e.map(c=>c.listen(d));return s(),()=>{for(let c of a)c()}}),o},g=(e,r)=>z(e,r),se=(e,r)=>z(e,r,!0),K=(e={})=>{let r=k(e);return r.setKey=function(t,n){let i=r.value;typeof n>"u"&&t in r.value?(r.value={...r.value},delete r.value[t],r.notify(i,t)):r.value[t]!==n&&(r.value={...r.value,[t]:n},r.notify(i,t))},r};var m=K({isLoaded:!1,client:void 0,user:void 0,session:void 0,organization:void 0}),N=K(),v=k(null);g([m],e=>e.isLoaded);var S=se([m,N],(e,r)=>q(e.isLoaded,{session:e.session,user:e.user,organization:e.organization,client:e.client},r));g([S],e=>e.user);g([S],e=>e.session);var oe=g([S],e=>e.organization),_=g([m],e=>e.client),ae=g([v],e=>e);g([_],e=>e?.sessions);var le=g([_],e=>e?.signIn),ce=g([_],e=>e?.signUp),A=(e,r)=>{D(e,()=>{v.get()?.telemetry?.record(re(r))})};A(le,"$signInStore");A(ce,"$signUpStore");A(oe,"$organizationStore");function ue(e){if(!e)return"";let r;if(e.match(/^(clerk\.)+\w*$/))r=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;r=/^(clerk\.)*/gi}return`clerk.${e.replace(r,"")}`}var de=(e,r="5.59.0")=>{if(e)return e;const t=fe(r);return t?t==="snapshot"?"5.59.0":t:ge(r)},fe=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],ge=e=>e.trim().replace(/^v/,"").split(".")[0];function he(e){return e?me(e)||$(e):!0}function me(e){return/^http(s)?:\/\//.test(e||"")}function $(e){return e.startsWith("/")}function pe(e){return e?$(e)?new URL(e,window.location.origin).toString():e:""}var ve={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,r)=>r<5,retryImmediately:!1,jitter:!0},ye=100,V=async e=>new Promise(r=>setTimeout(r,e)),F=(e,r)=>r?e*(1+Math.random()):e,be=e=>{let r=0;const t=()=>{const n=e.initialDelay,i=e.factor;let s=n*Math.pow(i,r);return s=F(s,e.jitter),Math.min(e.maxDelayBetweenRetries||s,s)};return async()=>{await V(t()),r++}},we=async(e,r={})=>{let t=0;const{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:s,factor:o,retryImmediately:u,jitter:l}={...ve,...r},d=be({initialDelay:i,maxDelayBetweenRetries:s,factor:o,jitter:l});for(;;)try{return await e()}catch(a){if(t++,!n(a,t))throw a;u&&t===1?await V(F(ye,l)):await d()}},Ee="loadScript cannot be called when document does not exist",ke="loadScript cannot be called without a src";async function Se(e="",r){const{async:t,defer:n,beforeLoad:i,crossOrigin:s,nonce:o}=r||{};return we(()=>new Promise((l,d)=>{e||d(new Error(ke)),(!document||!document.body)&&d(Ee);const a=document.createElement("script");s&&a.setAttribute("crossorigin",s),a.async=t||!1,a.defer=n||!1,a.addEventListener("load",()=>{a.remove(),l(a)}),a.addEventListener("error",()=>{a.remove(),d()}),a.src=e,a.nonce=o,i?.(a),document.body.appendChild(a)}),{shouldRetry:(l,d)=>d<=5})}var _e=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function Ae({packageName:e,customMessages:r}){let t=e;const n={..._e,...r};function i(s,o){if(!o)return`${t}: ${s}`;let u=s;const l=s.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);for(const d of l){const a=(o[d[1]]||"").toString();u=u.replace(`{{${d[1]}}}`,a)}return`${t}: ${u}`}return{setPackageName({packageName:s}){return typeof s=="string"&&(t=s),this},setMessages({customMessages:s}){return Object.assign(n,s||{}),this},throwInvalidPublishableKeyError(s){throw new Error(i(n.InvalidPublishableKeyErrorMessage,s))},throwInvalidProxyUrl(s){throw new Error(i(n.InvalidProxyUrlErrorMessage,s))},throwMissingPublishableKeyError(){throw new Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw new Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(s){throw new Error(i(n.MissingClerkProvider,s))},throw(s){throw new Error(i(s))}}}var R="Clerk: Failed to load Clerk",{isDevOrStagingUrl:Ie}=Z(),B=Ae({packageName:"@clerk/shared"});function Ue(e){B.setPackageName({packageName:e})}var Pe=async e=>{const r=document.querySelector("script[data-clerk-js-script]");if(r)return new Promise((t,n)=>{r.addEventListener("load",()=>{t(r)}),r.addEventListener("error",()=>{n(R)})});if(!e?.publishableKey){B.throwMissingPublishableKeyError();return}return Se(Re(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:Te(e)}).catch(()=>{throw new Error(R)})},Re=e=>{const{clerkJSUrl:r,clerkJSVariant:t,clerkJSVersion:n,proxyUrl:i,domain:s,publishableKey:o}=e;if(r)return r;let u="";i&&he(i)?u=pe(i).replace(/http(s)?:\/\//,""):s&&!Ie(U(o)?.frontendApi||"")?u=ue(s):u=U(o)?.frontendApi||"";const l=t?`${t.replace(/\.+$/,"")}.`:"",d=de(n);return`https://${u}/npm/@clerk/clerk-js@${d}/dist/clerk.${l}browser.js`},Ce=e=>{const r={};return e.publishableKey&&(r["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(r["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(r["data-clerk-domain"]=e.domain),e.nonce&&(r.nonce=e.nonce),r},Te=e=>r=>{const t=Ce(e);for(const n in t)r.setAttribute(n,t[n])},J=()=>{["handleRedirectCallback"].forEach(r=>{document.querySelectorAll(`[data-clerk-function-id^="clerk-${r}"]`).forEach(n=>{const i=n.getAttribute("data-clerk-function-id"),s=window.__astro_clerk_function_props?.get(r)?.get(i)??{};v.get()?.[r]?.(s)})})},H=()=>{Object.entries({"organization-list":"mountOrganizationList","organization-profile":"mountOrganizationProfile","organization-switcher":"mountOrganizationSwitcher","user-button":"mountUserButton","user-profile":"mountUserProfile","sign-in":"mountSignIn","sign-up":"mountSignUp","google-one-tap":"openGoogleOneTap",waitlist:"mountWaitlist"}).forEach(([r,t])=>{document.querySelectorAll(`[data-clerk-id^="clerk-${r}"]`).forEach(i=>{const s=i.getAttribute("data-clerk-id"),o=window.__astro_clerk_component_props?.get(r)?.get(s);i&&v.get()?.[t](i,o)})})},Oe=e=>{let r=!1;return t=>{if(r){const n=window.Clerk;return new Promise(i=>n?(n.loaded&&(H(),J()),i(n.loaded)):i(!1))}return r=!0,e(t)}},C;Ue("@clerk/astro");function T(e){return(r,t)=>{t?.__internal_metadata?.navigationType==="internal"?e(history.state,"",r):t?.windowNavigate(r)}}var Me=Oe(Le);async function Le(e){let r=window.Clerk;if(!r){if(await Pe(e),!window.Clerk)throw new Error("Failed to download latest ClerkJS. Contact <EMAIL>.");r=window.Clerk}return v.get()||v.set(r),C={routerPush:T(window.history.pushState.bind(window.history)),routerReplace:T(window.history.replaceState.bind(window.history)),...e},r.load(C).then(()=>{m.setKey("isLoaded",!0),ae.notify(),H(),J(),r.addListener(t=>{m.setKey("client",t.client),m.setKey("user",t.user),m.setKey("session",t.session),m.setKey("organization",t.organization)})}).catch(()=>{})}const xe="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let De=e=>crypto.getRandomValues(new Uint8Array(e)),ze=(e,r,t)=>{let n=(2<<Math.log2(e.length-1))-1,i=-~(1.6*n*r/e.length);return(s=r)=>{let o="";for(;;){let u=t(i),l=i|0;for(;l--;)if(o+=e[u[l]&n]||"",o.length>=s)return o}}},Ke=(e,r=21)=>ze(e,r|0,De);var Ne=e=>{const{signInUrl:r,signUpUrl:t,isSatellite:n,proxyUrl:i,domain:s,publishableKey:o,telemetry:u,...l}=e||{};return{signInUrl:r||"/sign-in",signUpUrl:t||"/sign-up",isSatellite:n||void 0,proxyUrl:i||"/__clerk",domain:s||void 0,publishableKey:o||"pk_live_Y2xlcmsucXVlci51cyQ",telemetry:u||{disabled:I(),debug:I()},...l}};function $e(e){async function r(t){const n=document.getElementById("__CLERK_ASTRO_DATA__");n&&N.set(JSON.parse(n.textContent||"{}"));const i=document.getElementById("__CLERK_ASTRO_SAFE_VARS__");let s={};i&&(s=JSON.parse(i.textContent||"{}")),await e(Ne({...t,...s}))}return r}var Je=(e=10)=>Ke(xe,e)(),O="data-astro-transition-persist",Ve="data-emotion";function He(e,r){e.deselectScripts(r),e.swapRootAttributes(r);const t=document.querySelectorAll(`style[${Ve}]`);Fe(r,Array.from(t));const n=e.saveFocus();e.swapBodyElement(r.body,document.body),n()}function Fe(e,r){for(const t of Array.from(document.head.children)){const n=Be(t,e);n?n.remove():r.includes(t)||t.remove()}document.head.append(...e.head.children)}function Be(e,r){const t=e.getAttribute(O),n=t&&r.head.querySelector(`[${O}="${t}"]`);if(n)return n;if(e.matches("link[rel=stylesheet]")){const i=e.getAttribute("href");return r.head.querySelector(`link[rel=stylesheet][href="${i}"]`)}return null}var M="__netlify_clerk_cache_bust";function Ye(){const e=new URL(window.location.href);e.searchParams.has(M)&&(e.searchParams.delete(M),window.history.replaceState(window.history.state,"",e))}var We=$e(Me);export{Ye as a,Je as g,We as r,He as s};

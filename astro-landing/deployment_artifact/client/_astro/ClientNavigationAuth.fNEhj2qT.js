import{m as v,a as d,c as l,r as f,d as p,b as s,$ as W,e as C,j as a}from"./InternalUIComponentRenderer.Djr_-AHC.js";import{r as g}from"./index.BVOCwoKb.js";import"./chunk-6EJX3HJ2.COMaeMaZ.js";async function b(e,n){if(e._metadata.renderedScripts.has(n))return;e._metadata.renderedScripts.add(n);const o=e.inlinedScripts.get(n);if(o!=null)return o?v(`<script type="module">${o}<\/script>`):"";const t=await e.resolve(n);return v(`<script type="module" src="${e.userAssetsBase?(e.base==="/"?"":e.base)+e.userAssetsBase:""}${t}"><\/script>`)}const H=d("https://quer.us"),U=l((e,n,o)=>{const t=e.createAstro(H,n,o);t.self=U;const{class:r}=t.props;return s`${f(e,"clerk-signed-in","clerk-signed-in",{class:r,hidden:!0},{default:()=>s` ${p(e,o.default)} `})} ${b(e,"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedInCSR.astro?astro&type=script&index=0&lang.ts")}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedInCSR.astro",void 0),T=d("https://quer.us"),S=l((e,n,o)=>{const t=e.createAstro(T,n,o);t.self=S;const{userId:r}=t.locals.auth();return s`${r?s`${p(e,o.default)}`:null}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedInSSR.astro",void 0),D="server";function $(e){return e!==void 0?e:D==="static"}const z=d("https://quer.us"),y=l((e,n,o)=>{const t=e.createAstro(z,n,o);t.self=y;const{isStatic:r,class:c}=t.props,i=$(r)?U:S;return s`${f(e,"SignedInComponent",i,{class:c},{default:u=>s` ${p(u,o.default)} `})}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedIn.astro",void 0),G=d("https://quer.us"),q=l((e,n,o)=>{const t=e.createAstro(G,n,o);t.self=q;const{class:r}=t.props;return s`${f(e,"clerk-signed-out","clerk-signed-out",{class:r,hidden:!0},{default:()=>s` ${p(e,o.default)} `})} ${b(e,"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedOutCSR.astro?astro&type=script&index=0&lang.ts")}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedOutCSR.astro",void 0),Z=d("https://quer.us"),O=l((e,n,o)=>{const t=e.createAstro(Z,n,o);t.self=O;const{userId:r}=t.locals.auth();return s`${r?null:s`${p(e,o.default)}`}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedOutSSR.astro",void 0),K=d("https://quer.us"),P=l((e,n,o)=>{const t=e.createAstro(K,n,o);t.self=P;const{isStatic:r,class:c}=t.props,i=$(r)?q:O;return s`${f(e,"SignedOutComponent",i,{class:c},{default:u=>s` ${p(u,o.default)} `})}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/control/SignedOut.astro",void 0),V=d("https://quer.us"),j=l((e,n,o)=>{const t=e.createAstro(V,n,o);return t.self=j,s`${f(e,"InternalUIComponentRenderer",W,{...t.props,component:"user-button"})} ${p(e,o.default)}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/UserButton.astro",void 0);var _=Object.freeze,F=Object.defineProperty,J=(e,n)=>_(F(e,"raw",{value:_(n||e.slice())})),B;const Q=d("https://quer.us"),k=l(async(e,n,o)=>{const t=e.createAstro(Q,n,o);t.self=k;const{label:r,href:c,open:i,clickIdentifier:u,parent:m}=t.props;let I="";return t.slots.has("label-icon")&&(I=await t.slots.render("label-icon")),s(B||(B=J(["<script>(function(){",`
const parentElement = document.currentScript.parentElement;

// We used a web component in the \`<UserButton.MenuItems>\` component.
const hasParentMenuItem = parentElement.tagName.toLowerCase() === 'clerk-user-button-menu-items';
if (!hasParentMenuItem) {
  if (isDevMode) {
    throw new Error(
      \`Clerk: <UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored.\`
    );
  }
  return
}

// Get the user button map from window that we set in the \`<InternalUIComponentRenderer />\`.
const userButtonComponentMap = window.__astro_clerk_component_props.get('user-button');

let userButton
if (parent) {
  userButton = document.querySelector(\`[data-clerk-id="clerk-user-button-\${parent}"]\`);
} else {
  userButton = document.querySelector('[data-clerk-id^="clerk-user-button"]');
}

const safeId = userButton.getAttribute('data-clerk-id');
const currentOptions = userButtonComponentMap.get(safeId);

const reorderItemsLabels = ['manageAccount', 'signOut'];
const isReorderItem = reorderItemsLabels.includes(label);

let newMenuItem = {
  label,
}

if (!isReorderItem) {
  newMenuItem = {
    ...newMenuItem,
    mountIcon: (el) => {
      el.innerHTML = labelIcon
    },
    unmountIcon: () => { /* What to clean up? */}
  }

  if (href) {
    newMenuItem.href = href;
  } else if (open) {
    newMenuItem.open = open.startsWith('/') ? open : \`/\${open}\`;
  } else if (clickIdentifier) {
    const clickEvent = new CustomEvent('clerk:menu-item-click', { detail: clickIdentifier });
    newMenuItem.onClick = () => {
      document.dispatchEvent(clickEvent);
    }
  }
}

userButtonComponentMap.set(safeId, {
  ...currentOptions,
  customMenuItems: [
    ...(currentOptions?.customMenuItems ?? []),
    newMenuItem,
  ]
})
})();<\/script>`],["<script>(function(){",`
const parentElement = document.currentScript.parentElement;

// We used a web component in the \\\`<UserButton.MenuItems>\\\` component.
const hasParentMenuItem = parentElement.tagName.toLowerCase() === 'clerk-user-button-menu-items';
if (!hasParentMenuItem) {
  if (isDevMode) {
    throw new Error(
      \\\`Clerk: <UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored.\\\`
    );
  }
  return
}

// Get the user button map from window that we set in the \\\`<InternalUIComponentRenderer />\\\`.
const userButtonComponentMap = window.__astro_clerk_component_props.get('user-button');

let userButton
if (parent) {
  userButton = document.querySelector(\\\`[data-clerk-id="clerk-user-button-\\\${parent}"]\\\`);
} else {
  userButton = document.querySelector('[data-clerk-id^="clerk-user-button"]');
}

const safeId = userButton.getAttribute('data-clerk-id');
const currentOptions = userButtonComponentMap.get(safeId);

const reorderItemsLabels = ['manageAccount', 'signOut'];
const isReorderItem = reorderItemsLabels.includes(label);

let newMenuItem = {
  label,
}

if (!isReorderItem) {
  newMenuItem = {
    ...newMenuItem,
    mountIcon: (el) => {
      el.innerHTML = labelIcon
    },
    unmountIcon: () => { /* What to clean up? */}
  }

  if (href) {
    newMenuItem.href = href;
  } else if (open) {
    newMenuItem.open = open.startsWith('/') ? open : \\\`/\\\${open}\\\`;
  } else if (clickIdentifier) {
    const clickEvent = new CustomEvent('clerk:menu-item-click', { detail: clickIdentifier });
    newMenuItem.onClick = () => {
      document.dispatchEvent(clickEvent);
    }
  }
}

userButtonComponentMap.set(safeId, {
  ...currentOptions,
  customMenuItems: [
    ...(currentOptions?.customMenuItems ?? []),
    newMenuItem,
  ]
})
})();<\/script>`])),C({label:r,href:c,open:i,clickIdentifier:u,labelIcon:I,isDevMode:!1,parent:m}))},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/MenuItemRenderer.astro",void 0),X=d("https://quer.us"),L=l((e,n,o)=>{const t=e.createAstro(X,n,o);t.self=L;const{label:r,href:c,parent:i}=t.props;return s`${f(e,"MenuItemRenderer",k,{label:r,href:c,parent:i},{"label-icon":u=>s`${p(u,o["label-icon"])}`})}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/UserButtonLink.astro",void 0),Y=d("https://quer.us"),R=l((e,n,o)=>{const t=e.createAstro(Y,n,o);t.self=R;const{label:r,open:c,clickIdentifier:i,parent:u}=t.props;return s`${f(e,"MenuItemRenderer",k,{label:r,open:c,clickIdentifier:i,parent:u},{"label-icon":m=>s`${p(m,o["label-icon"])}`})}`},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/UserButtonAction.astro",void 0),ee=l((e,n,o)=>s`${f(e,"clerk-user-button-menu-items","clerk-user-button-menu-items",{},{default:()=>s` ${p(e,o.default)} `})} ${b(e,"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/UserButtonMenuItems.astro?astro&type=script&index=0&lang.ts")}`,"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/UserButtonMenuItems.astro",void 0);var x=Object.freeze,ne=Object.defineProperty,te=(e,n)=>x(ne(e,"raw",{value:x(n||e.slice())})),M;const oe=d("https://quer.us"),E=l(async(e,n,o)=>{const t=e.createAstro(oe,n,o);t.self=E;const{url:r,label:c,parent:i}=t.props;let u="",m="";return t.slots.has("label-icon")&&(u=await t.slots.render("label-icon")),t.slots.has("default")&&(m=await t.slots.render("default")),s(M||(M=te(["<script>(function(){",`
// Get the user button map from window that we set in the \`<InternalUIComponentRenderer />\`.
const userButtonComponentMap = window.__astro_clerk_component_props.get('user-button');

let userButton
if (parent) {
  userButton = document.querySelector(\`[data-clerk-id="clerk-user-button-\${parent}"]\`);
} else {
  userButton = document.querySelector('[data-clerk-id^="clerk-user-button"]');
}

const safeId = userButton.getAttribute('data-clerk-id');
const currentOptions = userButtonComponentMap.get(safeId);

const newCustomPage = {
  label,
  url,
  mountIcon: (el) => {
    el.innerHTML = labelIcon
  },
  unmountIcon: () => { /* What to clean up? */},
  mount: (el) => {
    el.innerHTML = content
  },
  unmount: () => { /* What to clean up? */},
}

userButtonComponentMap.set(safeId, {
  ...currentOptions,
  userProfileProps: {
    customPages: [
      ...(currentOptions?.userProfileProps?.customPages ?? []),
      newCustomPage,
    ]
  }
})
})();<\/script>`],["<script>(function(){",`
// Get the user button map from window that we set in the \\\`<InternalUIComponentRenderer />\\\`.
const userButtonComponentMap = window.__astro_clerk_component_props.get('user-button');

let userButton
if (parent) {
  userButton = document.querySelector(\\\`[data-clerk-id="clerk-user-button-\\\${parent}"]\\\`);
} else {
  userButton = document.querySelector('[data-clerk-id^="clerk-user-button"]');
}

const safeId = userButton.getAttribute('data-clerk-id');
const currentOptions = userButtonComponentMap.get(safeId);

const newCustomPage = {
  label,
  url,
  mountIcon: (el) => {
    el.innerHTML = labelIcon
  },
  unmountIcon: () => { /* What to clean up? */},
  mount: (el) => {
    el.innerHTML = content
  },
  unmount: () => { /* What to clean up? */},
}

userButtonComponentMap.set(safeId, {
  ...currentOptions,
  userProfileProps: {
    customPages: [
      ...(currentOptions?.userProfileProps?.customPages ?? []),
      newCustomPage,
    ]
  }
})
})();<\/script>`])),C({url:r,label:c,content:m,labelIcon:u,parent:i}))},"/Users/<USER>/quer-calc-ui/astro-landing/node_modules/@clerk/astro/components/interactive/UserButton/UserButtonUserProfilePage.astro",void 0),h=Object.assign(j,{MenuItems:ee,Link:L,Action:R,UserProfilePage:E});/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),se=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(n,o,t)=>t?t.toUpperCase():o.toLowerCase()),A=e=>{const n=se(e);return n.charAt(0).toUpperCase()+n.slice(1)},N=(...e)=>e.filter((n,o,t)=>!!n&&n.trim()!==""&&t.indexOf(n)===o).join(" ").trim(),ce=e=>{for(const n in e)if(n.startsWith("aria-")||n==="role"||n==="title")return!0};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var ae={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=g.forwardRef(({color:e="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:t,className:r="",children:c,iconNode:i,...u},m)=>g.createElement("svg",{ref:m,...ae,width:n,height:n,stroke:e,strokeWidth:t?Number(o)*24/Number(n):o,className:N("lucide",r),...!c&&!ce(u)&&{"aria-hidden":"true"},...u},[...i.map(([I,w])=>g.createElement(I,w)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=(e,n)=>{const o=g.forwardRef(({className:t,...r},c)=>g.createElement(ue,{ref:c,iconNode:n,className:N(`lucide-${re(A(e))}`,`lucide-${e}`,t),...r}));return o.displayName=A(e),o};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],de=ie("user",le);function ge(){return g.useEffect(()=>{const e=document.getElementById("custom-user-button");e&&e.addEventListener("click",()=>{const n=document.querySelector(".cl-userButtonTrigger");n&&n.click()})},[]),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(P,{children:a.jsx("a",{href:"/sign-in",className:"inline-flex items-center justify-center px-3 py-1 bg-gradient-to-b from-slate-700 to-slate-800 text-white text-sm font-medium rounded-md transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.07)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-slate-600 hover:to-slate-700 hover:shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_4px_rgba(0,0,0,0.3)] focus:outline-none focus:ring-2 focus:ring-primary-orange/70",children:a.jsx("span",{children:"Sign In"})})}),a.jsx(y,{children:a.jsxs("div",{id:"user-button-container",className:"relative",children:[a.jsx("div",{className:"absolute opacity-0 pointer-events-none",children:a.jsx(h,{children:a.jsxs(h.MenuItems,{children:[a.jsx(h.Link,{label:"Billing & Downloads",href:"/account",children:a.jsxs("svg",{slot:"label-icon",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[a.jsx("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),a.jsx("polyline",{points:"7 10 12 15 17 10"}),a.jsx("line",{x1:"12",y1:"15",x2:"12",y2:"3"})]})}),a.jsx(h.Action,{label:"signOut"})]})})}),a.jsx("button",{id:"custom-user-button",className:"w-9 h-9 flex items-center justify-center rounded-full hover:bg-slate-800 text-slate-400 hover:text-slate-300 transition-colors",children:a.jsx(de,{size:20})})]})})]})}export{ge as default};

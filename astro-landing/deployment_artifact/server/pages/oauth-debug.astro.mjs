/* empty css                                   */
import { f as createComponent, n as renderHead, j as renderScript, r as renderTemplate } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import "clsx";
/* empty css                                       */
import { renderers } from "../renderers.mjs";
const $$OauthDebug = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`<html data-astro-cid-bswva3tm> <head><title>OAuth Debug</title><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">${renderHead()}</head> <body data-astro-cid-bswva3tm> <h1 data-astro-cid-bswva3tm>OAuth Debug Page</h1> <div class="section" data-astro-cid-bswva3tm> <div class="label" data-astro-cid-bswva3tm>Current URL:</div> <div id="currentUrl" class="debug-info value" data-astro-cid-bswva3tm></div> <div class="label" data-astro-cid-bswva3tm>URL Parameters:</div> <div id="urlParams" class="debug-info value" data-astro-cid-bswva3tm></div> <div class="label" data-astro-cid-bswva3tm>Headers:</div> <div id="headers" class="debug-info value" data-astro-cid-bswva3tm></div> <div class="label" data-astro-cid-bswva3tm>Cookies:</div> <div id="cookies" class="debug-info value" data-astro-cid-bswva3tm></div> <div class="label" data-astro-cid-bswva3tm>Clerk State:</div> <div id="clerkState" class="debug-info value" data-astro-cid-bswva3tm>Checking...</div> </div> <button id="redirectBtn" class="btn" data-astro-cid-bswva3tm>Redirect to App</button> <button id="clearLogsBtn" class="btn" data-astro-cid-bswva3tm>Clear Debug Info</button> ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-debug.astro?astro&type=script&index=0&lang.ts")} </body> </html>`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-debug.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-debug.astro";
const $$url = "/oauth-debug";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$OauthDebug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

/* empty css                                   */
import { e as createAstro, f as createComponent, i as renderComponent, r as renderTemplate, m as maybeRenderHead } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "../chunks/Layout_GAVVpe6b.mjs";
import { $ as $$RedirectProps } from "../chunks/RedirectProps_C3lwCBc1.mjs";
import { renderers } from "../renderers.mjs";
const $$Astro = createAstro("https://quer.us");
const $$SignIn = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$SignIn;
  const { userId } = Astro2.locals.auth();
  if (userId) {
    return Astro2.redirect("/account");
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Sign In - Quer Terminal" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "RedirectProps", $$RedirectProps, {})} ${maybeRenderHead()}<div class="flex justify-center items-center min-h-screen bg-slate-950 p-4"> ${renderComponent($$result2, "ClientSignIn", null, { "client:only": "react", "client:component-hydration": "only", "client:component-path": "/Users/<USER>/quer-calc-ui/astro-landing/src/components/ClientSignIn.tsx", "client:component-export": "default" })} </div> ` })}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/sign-in.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/sign-in.astro";
const $$url = "/sign-in";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$SignIn,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

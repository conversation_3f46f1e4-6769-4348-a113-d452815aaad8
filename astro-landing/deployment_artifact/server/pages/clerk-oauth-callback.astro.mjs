/* empty css                                   */
import { f as createComponent, i as renderComponent, j as renderScript, r as renderTemplate, m as maybeRenderHead } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "../chunks/Layout_GAVVpe6b.mjs";
import { renderers } from "../renderers.mjs";
const $$ClerkOauthCallback = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "OAuth Callback - Quer" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="flex flex-col items-center justify-center min-h-screen bg-slate-950 p-4"> <div class="w-20 h-20 mb-6"> <div class="animate-spin rounded-full h-20 w-20 border-t-4 border-primary-orange"></div> </div> <h1 class="text-2xl font-bold text-white mb-4">OAuth Authentication Complete</h1> <p class="text-slate-400 mb-8 text-center max-w-md">
Redirecting to the QUER app...
</p> <div id="debugInfo" class="hidden w-full max-w-md text-xs text-slate-500 overflow-auto max-h-48 p-4 bg-slate-900 rounded-md mb-6"></div> </div> ` })} ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/clerk-oauth-callback.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/clerk-oauth-callback.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/clerk-oauth-callback.astro";
const $$url = "/clerk-oauth-callback";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$ClerkOauthCallback,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

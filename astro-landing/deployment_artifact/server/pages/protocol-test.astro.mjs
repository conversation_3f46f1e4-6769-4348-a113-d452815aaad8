/* empty css                                   */
import { f as createComponent, i as renderComponent, j as renderScript, r as renderTemplate, m as maybeRenderHead } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "../chunks/Layout_GAVVpe6b.mjs";
import { renderers } from "../renderers.mjs";
const $$ProtocolTest = createComponent(async ($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Protocol Link Test" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8 max-w-4xl"> <h1 class="text-2xl font-bold text-center text-primary-500 mb-6">Protocol Link Test</h1> <p class="text-center text-slate-400 mb-8">
Эта страница позволяет проверить регистрацию и работу протокольных ссылок для Electron приложения.
</p> <div class="flex justify-center gap-4 mb-6"> <a href="/protocol-test" class="text-slate-300 hover:text-primary-500">Protocol Test</a> <a href="/direct-oauth" class="text-slate-300 hover:text-primary-500">Direct OAuth</a> <a href="/auth-debug" class="text-slate-300 hover:text-primary-500">Auth Debug</a> </div> <div class="bg-slate-800 rounded-lg p-6 mb-6"> <h2 class="text-xl font-semibold text-primary-500 mb-4">Генератор протокольных ссылок</h2> <p class="text-slate-400 mb-4">
Создайте и протестируйте протокольную ссылку querapp:// с разными параметрами.
</p> <div class="mb-4"> <label for="token-input" class="block mb-2">Clerk Session Token (или любой другой текст):</label> <input type="text" id="token-input" placeholder="eyJhbGciOiJSUzI..." class="w-full p-2 bg-slate-700 border border-slate-600 rounded-md"> </div> <div class="flex gap-2 mb-4"> <button id="load-token" class="bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-md">
Загрузить из localStorage
</button> <button id="save-to-storage" class="bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-md">
Сохранить в localStorage
</button> </div> <div class="mb-4"> <label for="protocol-url" class="block mb-2">Протокольная ссылка:</label> <input type="text" id="protocol-url" value="querapp://oauth-complete?clerk_session_token=" readonly class="w-full p-2 bg-slate-700 border border-slate-600 rounded-md"> </div> <div class="flex gap-2 mb-4"> <button id="generate-url" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md">
Сгенерировать URL
</button> <button id="open-url" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md">
Открыть протокольную ссылку
</button> </div> <div> <h3 class="font-semibold mb-2">Результат:</h3> <div id="log" class="bg-slate-900 p-4 rounded-md font-mono text-sm h-48 overflow-auto"></div> </div> </div> <div class="bg-slate-800 rounded-lg p-6 mb-6"> <h2 class="text-xl font-semibold text-primary-500 mb-4">Проверка токена</h2> <p class="text-slate-400 mb-4">
Проверьте валидность токена через Go бэкенд.
</p> <div class="mb-4"> <label for="check-token-input" class="block mb-2">Токен для проверки:</label> <textarea id="check-token-input" class="w-full p-2 bg-slate-700 border border-slate-600 rounded-md h-32 font-mono"></textarea> </div> <button id="check-token" class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md mb-4">
Проверить токен
</button> <div> <h3 class="font-semibold mb-2">Результат проверки:</h3> <div id="token-check-result" class="bg-slate-900 p-4 rounded-md font-mono text-sm h-48 overflow-auto"></div> </div> </div> <div class="bg-slate-800 rounded-lg p-6"> <h2 class="text-xl font-semibold text-primary-500 mb-4">Токены в localStorage</h2> <div id="localstorage-tokens" class="bg-slate-900 p-4 rounded-md font-mono text-sm mb-4"></div> <button id="refresh-localstorage" class="bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-md">
Обновить
</button> </div> </div> ` })} ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/protocol-test.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/protocol-test.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/protocol-test.astro";
const $$url = "/protocol-test";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$ProtocolTest,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

/* empty css                                   */
import { f as createComponent, i as renderComponent, j as renderScript, r as renderTemplate, m as maybeRenderHead } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "../chunks/Layout_GAVVpe6b.mjs";
import { renderers } from "../renderers.mjs";
const $$AuthDebug = createComponent(async ($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Auth Debug Tools" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto p-4"> <h1 class="text-2xl text-center mb-4">Auth Debug Tools</h1> <div class="flex justify-center gap-4 mb-6"> <a href="/protocol-test">Protocol Test</a> <a href="/direct-oauth">Direct OAuth</a> <a href="/auth-debug">Auth Debug</a> </div> <div class="bg-slate-800 p-4 rounded mb-4"> <h2 class="text-xl mb-2">Текущие cookies</h2> <button id="check-cookies" class="bg-blue-500 text-white px-4 py-2 rounded mb-2">
Проверить cookies
</button> <div id="cookies-result" class="bg-slate-900 p-2 rounded h-40 overflow-auto"></div> </div> <div class="bg-slate-800 p-4 rounded mb-4"> <h2 class="text-xl mb-2">Проверка API</h2> <button id="check-api" class="bg-blue-500 text-white px-4 py-2 rounded mb-2">
Проверить API
</button> <div id="api-result" class="bg-slate-900 p-2 rounded h-40 overflow-auto"></div> </div> </div> ` })} ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/auth-debug.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/auth-debug.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/auth-debug.astro";
const $$url = "/auth-debug";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$AuthDebug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

/* empty css                                   */
import { f as createComponent, i as renderComponent, j as renderScript, r as renderTemplate, m as maybeRenderHead } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "../chunks/Layout_GAVVpe6b.mjs";
/* empty css                                        */
import { renderers } from "../renderers.mjs";
const $$DirectOauth = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "OAuth Redirecting", "description": "Redirecting to new OAuth handler", "data-astro-cid-njv33vyp": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="flex flex-col items-center justify-center min-h-screen bg-slate-950 text-slate-200 p-4" data-astro-cid-njv33vyp> <div class="w-20 h-20 mb-8" data-astro-cid-njv33vyp> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" data-astro-cid-njv33vyp> <circle cx="50" cy="50" r="45" fill="#0f172a" stroke="#f97316" stroke-width="2" data-astro-cid-njv33vyp></circle> <text x="50" y="65" fill="#f97316" font-size="40" text-anchor="middle" font-family="sans-serif" font-weight="bold" data-astro-cid-njv33vyp>Q</text> </svg> </div> <h1 class="text-xl font-medium text-center mb-4 text-primary-orange" data-astro-cid-njv33vyp>Authentication Redirecting</h1> <p class="text-slate-400 text-center mb-8 max-w-md" data-astro-cid-njv33vyp>We are redirecting you to the new authentication handler.</p> <div class="mt-8 p-4 bg-slate-900 rounded-lg border border-slate-800 max-w-md w-full" data-astro-cid-njv33vyp> <p class="text-slate-300 mb-2" data-astro-cid-njv33vyp>Status: <span id="status" data-astro-cid-njv33vyp>Checking parameters...</span><span id="loading-indicator" class="inline-block w-4 h-4 border-2 border-primary-orange border-t-transparent rounded-full animate-spin ml-2" data-astro-cid-njv33vyp></span></p> <p id="status-message" class="text-sm text-slate-400" data-astro-cid-njv33vyp>Please wait while we redirect you...</p> </div> </div> ` })} ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/direct-oauth.astro?astro&type=script&index=0&lang.ts")} `;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/direct-oauth.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/direct-oauth.astro";
const $$url = "/direct-oauth";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$DirectOauth,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

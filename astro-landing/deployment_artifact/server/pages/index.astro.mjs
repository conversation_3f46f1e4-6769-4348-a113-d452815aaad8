/* empty css                                   */
import { f as createComponent, m as maybeRenderHead, h as addAttribute, r as renderTemplate, e as createAstro, j as renderScript, i as renderComponent } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$SiteLayout } from "../chunks/SiteLayout_DiyTDXZM.mjs";
import { PiAppleLogo, PiWindowsLogo } from "react-icons/pi";
import "clsx";
/* empty css                                 */
import { renderers } from "../renderers.mjs";
const $$Features = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<section id="features" class="py-20 bg-slate-900/50"> <div class="max-w-6xl mx-auto px-4"> <!-- Abstract background gradient --> <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-orange opacity-5 rounded-full blur-[100px]"></div> <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-sky-500 opacity-5 rounded-full blur-[100px]"></div> <div class="text-center mb-16 max-w-3xl mx-auto relative"> <h2 class="text-4xl font-bold mb-4 text-slate-200">
Precision Trading Tools
</h2> <p class="text-slate-400 font-light leading-relaxed">
Advanced tools for maximizing results and confidence in every trade
</p> </div> <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 relative"> <!-- Feature Card 1 --> <div class="rounded-2xl overflow-hidden border border-white/10"> <div class="bg-gradient-to-br from-slate-800 to-slate-900 p-8 h-full"> <div class="text-primary-orange mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round"${addAttribute(1.5, "stroke-width")} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg> </div> <h3 class="text-xl font-medium mb-3 text-slate-200">Intelligent Position Sizing</h3> <p class="text-slate-400 leading-relaxed text-sm font-light">Automatic detection of position type (long/short) with optimal size calculation based on your risk management strategy. Supports percentage and fixed risk.</p> </div> </div> <!-- Feature Card 2 --> <div class="rounded-2xl overflow-hidden border border-white/10"> <div class="bg-gradient-to-br from-slate-800 to-slate-900 p-8 h-full"> <div class="text-primary-orange mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round"${addAttribute(1.5, "stroke-width")} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> </div> <h3 class="text-xl font-medium mb-3 text-slate-200">Trading Fee Analysis</h3> <p class="text-slate-400 leading-relaxed text-sm font-light">Complete analysis of fee impact on your trade profitability. Shows the difference between nominal and effective risk/reward ratio.</p> </div> </div> <!-- Feature Card 3 --> <div class="rounded-2xl overflow-hidden border border-white/10"> <div class="bg-gradient-to-br from-slate-800 to-slate-900 p-8 h-full"> <div class="text-primary-orange mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round"${addAttribute(1.5, "stroke-width")} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg> </div> <h3 class="text-xl font-medium mb-3 text-slate-200">Safe Leverage</h3> <p class="text-slate-400 leading-relaxed text-sm font-light">Smart calculation of optimal leverage levels with protection against premature liquidation. Ensures your stop-loss triggers before reaching liquidation price.</p> </div> </div> <!-- Feature Card 4 --> <div class="rounded-2xl overflow-hidden border border-white/10"> <div class="bg-gradient-to-br from-slate-800 to-slate-900 p-8 h-full"> <div class="text-primary-orange mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round"${addAttribute(1.5, "stroke-width")} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg> </div> <h3 class="text-xl font-medium mb-3 text-slate-200">Price Level Calculation</h3> <p class="text-slate-400 leading-relaxed text-sm font-light">Precise calculation of all critical price levels: liquidation, break-even, and optimal take-profit with consideration for fees and other exchange parameters.</p> </div> </div> <!-- Feature Card 5 --> <div class="rounded-2xl overflow-hidden border border-white/10"> <div class="bg-gradient-to-br from-slate-800 to-slate-900 p-8 h-full"> <div class="text-primary-orange mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round"${addAttribute(1.5, "stroke-width")} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg> </div> <h3 class="text-xl font-medium mb-3 text-slate-200">Statistical Projections</h3> <p class="text-slate-400 leading-relaxed text-sm font-light">Forecast trading results based on your win rate. Calculate the necessary win rate for break-even, maximum drawdown, and losing streaks.</p> </div> </div> <!-- Feature Card 6 --> <div class="rounded-2xl overflow-hidden border border-white/10"> <div class="bg-gradient-to-br from-slate-800 to-slate-900 p-8 h-full"> <div class="text-primary-orange mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round"${addAttribute(1.5, "stroke-width")} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path></svg> </div> <h3 class="text-xl font-medium mb-3 text-slate-200">Position Optimization</h3> <p class="text-slate-400 leading-relaxed text-sm font-light">Smart take-profit optimization to achieve desired risk/reward ratios accounting for all factors. Maximize the efficiency of every trade.</p> </div> </div> </div> <div class="mt-16 text-center relative"> <a href="/sign-up" class="inline-block px-8 py-4 bg-gradient-to-r from-amber-500 to-primary-orange text-slate-200 font-medium rounded-lg transition-all duration-300 hover:shadow-[0_0_15px_rgba(217,119,6,0.5)]">
Start Free Trial
</a> <p class="mt-4 text-sm font-light text-slate-500">7 days free • Cancel anytime</p> </div> </div> </section>`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/features.astro", void 0);
const $$Astro = createAstro("https://quer.us");
const $$WalletStylePricing = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$WalletStylePricing;
  return renderTemplate`${maybeRenderHead()}<section id="pricing" class="py-20 bg-gradient-to-b from-slate-900/50 to-slate-950 overflow-hidden" data-astro-cid-qi2ey7q5> <div class="max-w-6xl mx-auto px-4" data-astro-cid-qi2ey7q5> <div class="text-center mb-10" data-astro-cid-qi2ey7q5> <span class="inline-block bg-slate-800/50 text-xs text-primary-orange px-3 py-1 rounded-full uppercase tracking-wider font-medium mb-3" data-astro-cid-qi2ey7q5>Phase 1 Pricing</span> <h2 class="text-4xl font-bold mb-3 text-white" data-astro-cid-qi2ey7q5>Choose your plan</h2> <p class="text-slate-400 font-light max-w-xl mx-auto" data-astro-cid-qi2ey7q5>
Lock in special early adopter pricing before we reach Phase 6.
</p> </div> <!-- Pricing Toggle --> <div class="flex justify-center mb-10" data-astro-cid-qi2ey7q5> <div class="p-1 bg-slate-800/50 rounded-full border border-white/5 inline-flex" data-astro-cid-qi2ey7q5> <button id="yearly-toggle" class="w-28 px-6 py-2 text-sm rounded-full transition-all bg-slate-700/60 text-white" data-astro-cid-qi2ey7q5>
Yearly
</button> <button id="monthly-toggle" class="w-28 px-6 py-2 text-sm rounded-full transition-all text-slate-400 hover:text-slate-300" data-astro-cid-qi2ey7q5>
Monthly
</button> </div> </div> <!-- Card Stack (visible on desktop) / Single Card (visible on mobile) --> <div class="relative" data-astro-cid-qi2ey7q5> <!-- Desktop Card Stack --> <div class="hidden md:block max-w-2xl mx-auto relative h-[520px]" data-astro-cid-qi2ey7q5> <!-- Monthly Card - Positioned behind when not selected --> <div id="monthly-card" class="absolute top-0 left-1/2 w-full max-w-md rounded-2xl shadow-2xl transition-all duration-75 ease-in-out cursor-pointer z-10 translate-x-[-100px] translate-y-4 opacity-90 scale-95 hover:translate-y-3 inactive-card" data-astro-cid-qi2ey7q5> <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl overflow-hidden border border-white/10" data-astro-cid-qi2ey7q5> <!-- Card Header --> <div class="px-8 pt-8 pb-6" data-astro-cid-qi2ey7q5> <div class="flex justify-between items-start mb-4" data-astro-cid-qi2ey7q5> <div data-astro-cid-qi2ey7q5> <h3 class="text-xl font-bold text-white mb-1" data-astro-cid-qi2ey7q5>Monthly</h3> <p class="text-slate-400 text-sm" data-astro-cid-qi2ey7q5>Flexible month-to-month</p> </div> <div id="monthly-check" class="w-6 h-6 rounded-full bg-primary-orange hidden" data-astro-cid-qi2ey7q5> <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> </div> </div> <div class="relative mb-1" data-astro-cid-qi2ey7q5> <div class="flex items-baseline gap-1" data-astro-cid-qi2ey7q5> <span class="text-white text-4xl font-bold" data-astro-cid-qi2ey7q5>$39</span> <span class="text-slate-400 text-lg" data-astro-cid-qi2ey7q5>/month</span> </div> <div class="absolute right-0 top-0 bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full font-medium" data-astro-cid-qi2ey7q5>
Save 60%
</div> </div> <div class="flex items-center" data-astro-cid-qi2ey7q5> <span class="text-slate-500 line-through text-sm" data-astro-cid-qi2ey7q5>$99/month</span> <span class="text-slate-400 text-xs ml-2" data-astro-cid-qi2ey7q5>Future Phase 6 price</span> </div> </div> <!-- Divider with Badge --> <div class="relative h-px bg-white/10" data-astro-cid-qi2ey7q5> <div class="absolute -top-4 right-8 bg-slate-900 border border-white/10 px-3 py-1 rounded-full" data-astro-cid-qi2ey7q5> <span class="text-slate-300 text-xs" data-astro-cid-qi2ey7q5>Phase 1 pricing</span> </div> </div> <!-- Card Details --> <div class="px-8 py-8" data-astro-cid-qi2ey7q5> <ul class="space-y-4 mb-8" data-astro-cid-qi2ey7q5> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Professional trading calculator</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Advanced risk management</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Save unlimited configurations</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Desktop app for Mac & Windows</span> </li> </ul> <div id="monthly-cta" class="hidden" data-astro-cid-qi2ey7q5> <a href="/sign-up" class="block w-full py-3 bg-white text-slate-900 text-center text-sm font-medium rounded-xl transition-colors hover:bg-slate-100" data-astro-cid-qi2ey7q5>
Start 7-Day Free Trial
</a> <p class="text-center text-slate-500 text-xs mt-4 max-w-[80%] mx-auto" data-astro-cid-qi2ey7q5>
Cancel before trial ends and you won't be charged.
</p> </div> <div id="monthly-select" class="block" data-astro-cid-qi2ey7q5> <button class="select-plan-btn block w-full py-3 border border-white/20 text-white text-center text-sm font-medium rounded-xl transition-colors hover:bg-white/5" data-plan="monthly" data-astro-cid-qi2ey7q5>
Select Plan
</button> </div> </div> </div> </div> <!-- Yearly Card - Positioned in front when selected --> <div id="yearly-card" class="absolute top-0 left-1/2 w-full max-w-md rounded-2xl shadow-2xl transition-all duration-75 ease-in-out cursor-pointer z-20 -translate-x-1/2 translate-y-0 opacity-100" data-astro-cid-qi2ey7q5> <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl overflow-hidden border border-primary-orange/20" data-astro-cid-qi2ey7q5> <!-- Best Value Badge --> <div class="absolute -top-2 right-8" data-astro-cid-qi2ey7q5> <div class="bg-primary-orange text-white text-xs uppercase tracking-wider font-bold px-3 py-1 rounded-full shadow-lg" data-astro-cid-qi2ey7q5>
Best Value
</div> </div> <!-- Card Header --> <div class="px-8 pt-8 pb-6" data-astro-cid-qi2ey7q5> <div class="flex justify-between items-start mb-4" data-astro-cid-qi2ey7q5> <div data-astro-cid-qi2ey7q5> <h3 class="text-xl font-bold text-white mb-1" data-astro-cid-qi2ey7q5>Yearly</h3> <p class="text-slate-400 text-sm" data-astro-cid-qi2ey7q5>Maximum savings + price lock</p> </div> <div id="yearly-check" class="w-6 h-6 rounded-full bg-primary-orange flex items-center justify-center" data-astro-cid-qi2ey7q5> <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> </div> </div> <div class="relative mb-1" data-astro-cid-qi2ey7q5> <div class="flex items-baseline gap-1" data-astro-cid-qi2ey7q5> <span class="text-white text-4xl font-bold" data-astro-cid-qi2ey7q5>$29</span> <span class="text-slate-400 text-lg" data-astro-cid-qi2ey7q5>/month</span> </div> <div class="absolute right-0 top-0 bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full font-medium" data-astro-cid-qi2ey7q5>
Save 61%
</div> </div> <div class="flex items-center mb-1" data-astro-cid-qi2ey7q5> <span class="text-slate-500 line-through text-sm" data-astro-cid-qi2ey7q5>$74/month</span> <span class="text-slate-400 text-xs ml-2" data-astro-cid-qi2ey7q5>Future Phase 6 price</span> </div> <div class="text-slate-400 text-sm" data-astro-cid-qi2ey7q5>Billed annually ($348)</div> </div> <!-- Divider with Badge --> <div class="relative h-px bg-white/10" data-astro-cid-qi2ey7q5> <div class="absolute -top-4 right-8 bg-primary-orange/10 border border-primary-orange/10 px-3 py-1 rounded-full" data-astro-cid-qi2ey7q5> <span class="text-primary-orange text-xs" data-astro-cid-qi2ey7q5>Price locked for 12 months</span> </div> </div> <!-- Card Details --> <div class="px-8 py-8" data-astro-cid-qi2ey7q5> <ul class="space-y-4 mb-8" data-astro-cid-qi2ey7q5> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>All monthly features</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Priority support</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Early access to new features</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <div data-astro-cid-qi2ey7q5> <span class="text-primary-orange font-medium" data-astro-cid-qi2ey7q5>Price guarantee</span> <p class="text-xs text-slate-400" data-astro-cid-qi2ey7q5>Lock in current rates, even as we reach Phase 6</p> </div> </li> </ul> <div id="yearly-cta" class="block" data-astro-cid-qi2ey7q5> <a href="/sign-up" class="block w-full py-3 bg-gradient-to-b from-amber-500 to-primary-orange text-white text-center text-sm font-medium rounded-xl transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-amber-500 hover:to-amber-600 hover:shadow-[0_1px_rgba(255,255,255,0.15)_inset,0_1px_4px_rgba(0,0,0,0.3)]" data-astro-cid-qi2ey7q5>
Start 7-Day Free Trial
</a> <p class="text-center text-slate-500 text-xs mt-4 max-w-[80%] mx-auto" data-astro-cid-qi2ey7q5>
Cancel before trial ends and you won't be charged.
</p> </div> <div id="yearly-select" class="hidden" data-astro-cid-qi2ey7q5> <button class="select-plan-btn block w-full py-3 border border-white/20 text-white text-center text-sm font-medium rounded-xl transition-colors hover:bg-white/5" data-plan="yearly" data-astro-cid-qi2ey7q5>
Select Plan
</button> </div> </div> </div> </div> </div> <!-- Mobile Single Card View --> <div class="md:hidden max-w-md mx-auto" data-astro-cid-qi2ey7q5> <!-- Monthly Card (initially hidden) --> <div id="mobile-monthly-card" class="hidden" data-astro-cid-qi2ey7q5> <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl overflow-hidden border border-white/10" data-astro-cid-qi2ey7q5> <!-- Card Header --> <div class="px-8 pt-8 pb-6" data-astro-cid-qi2ey7q5> <div class="relative mb-1" data-astro-cid-qi2ey7q5> <div class="flex items-baseline gap-1" data-astro-cid-qi2ey7q5> <span class="text-white text-4xl font-bold" data-astro-cid-qi2ey7q5>$39</span> <span class="text-slate-400 text-lg" data-astro-cid-qi2ey7q5>/month</span> </div> <div class="absolute right-0 top-0 bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full font-medium" data-astro-cid-qi2ey7q5>
Save 60%
</div> </div> <div class="flex items-center" data-astro-cid-qi2ey7q5> <span class="text-slate-500 line-through text-sm" data-astro-cid-qi2ey7q5>$99/month</span> <span class="text-slate-400 text-xs ml-2" data-astro-cid-qi2ey7q5>Future Phase 6 price</span> </div> </div> <!-- Divider with Badge --> <div class="relative h-px bg-white/10" data-astro-cid-qi2ey7q5> <div class="absolute -top-4 right-8 bg-slate-900 border border-white/10 px-3 py-1 rounded-full" data-astro-cid-qi2ey7q5> <span class="text-slate-300 text-xs" data-astro-cid-qi2ey7q5>Phase 1 pricing</span> </div> </div> <!-- Card Details --> <div class="px-8 py-8" data-astro-cid-qi2ey7q5> <ul class="space-y-4 mb-8" data-astro-cid-qi2ey7q5> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Professional trading calculator</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Advanced risk management</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Save unlimited configurations</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Desktop app for Mac & Windows</span> </li> </ul> <a href="/sign-up" class="block w-full py-3 bg-white text-slate-900 text-center text-sm font-medium rounded-xl transition-colors hover:bg-slate-100" data-astro-cid-qi2ey7q5>
Start 7-Day Free Trial
</a> <p class="text-center text-slate-500 text-xs mt-4 max-w-[80%] mx-auto" data-astro-cid-qi2ey7q5>
Cancel before trial ends and you won't be charged.
</p> </div> </div> </div> <!-- Yearly Card (initially visible) --> <div id="mobile-yearly-card" class="block" data-astro-cid-qi2ey7q5> <div class="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl overflow-hidden border border-primary-orange/20" data-astro-cid-qi2ey7q5> <!-- Best Value Badge --> <div class="absolute -top-2 right-8" data-astro-cid-qi2ey7q5> <div class="bg-primary-orange text-white text-xs uppercase tracking-wider font-bold px-3 py-1 rounded-full shadow-lg" data-astro-cid-qi2ey7q5>
Best Value
</div> </div> <!-- Card Header --> <div class="px-8 pt-8 pb-6" data-astro-cid-qi2ey7q5> <div class="relative mb-1" data-astro-cid-qi2ey7q5> <div class="flex items-baseline gap-1" data-astro-cid-qi2ey7q5> <span class="text-white text-4xl font-bold" data-astro-cid-qi2ey7q5>$29</span> <span class="text-slate-400 text-lg" data-astro-cid-qi2ey7q5>/month</span> </div> <div class="absolute right-0 top-0 bg-green-500/20 text-green-400 text-xs px-2 py-0.5 rounded-full font-medium" data-astro-cid-qi2ey7q5>
Save 61%
</div> </div> <div class="flex items-center mb-1" data-astro-cid-qi2ey7q5> <span class="text-slate-500 line-through text-sm" data-astro-cid-qi2ey7q5>$74/month</span> <span class="text-slate-400 text-xs ml-2" data-astro-cid-qi2ey7q5>Future Phase 6 price</span> </div> <div class="text-slate-400 text-sm" data-astro-cid-qi2ey7q5>Billed annually ($348)</div> </div> <!-- Divider with Badge --> <div class="relative h-px bg-white/10" data-astro-cid-qi2ey7q5> <div class="absolute -top-4 right-8 bg-primary-orange/10 border border-primary-orange/10 px-3 py-1 rounded-full" data-astro-cid-qi2ey7q5> <span class="text-primary-orange text-xs" data-astro-cid-qi2ey7q5>Price locked for 12 months</span> </div> </div> <!-- Card Details --> <div class="px-8 py-8" data-astro-cid-qi2ey7q5> <ul class="space-y-4 mb-8" data-astro-cid-qi2ey7q5> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>All monthly features</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Priority support</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <span class="text-slate-300" data-astro-cid-qi2ey7q5>Early access to new features</span> </li> <li class="flex items-start" data-astro-cid-qi2ey7q5> <svg class="w-5 h-5 text-primary-orange mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-astro-cid-qi2ey7q5> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" data-astro-cid-qi2ey7q5></path> </svg> <div data-astro-cid-qi2ey7q5> <span class="text-primary-orange font-medium" data-astro-cid-qi2ey7q5>Price guarantee</span> <p class="text-xs text-slate-400" data-astro-cid-qi2ey7q5>Lock in current rates, even as we reach Phase 6</p> </div> </li> </ul> <a href="/sign-up" class="block w-full py-3 bg-gradient-to-b from-amber-500 to-primary-orange text-white text-center text-sm font-medium rounded-xl transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-amber-500 hover:to-amber-600 hover:shadow-[0_1px_rgba(255,255,255,0.15)_inset,0_1px_4px_rgba(0,0,0,0.3)]" data-astro-cid-qi2ey7q5>
Start 7-Day Free Trial
</a> <p class="text-center text-slate-500 text-xs mt-4 max-w-[80%] mx-auto" data-astro-cid-qi2ey7q5>
Cancel before trial ends and you won't be charged.
</p> </div> </div> </div> </div> <!-- Footer text removed --> </div> </div></section> ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/pricing/WalletStylePricing.astro?astro&type=script&index=0&lang.ts")} `;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/pricing/WalletStylePricing.astro", void 0);
const $$Roadmap = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<section class="py-16 bg-slate-800/30" data-astro-cid-nndcmjcn> <div class="max-w-6xl mx-auto px-4" data-astro-cid-nndcmjcn> <div class="text-center mb-10" data-astro-cid-nndcmjcn> <h2 class="text-4xl font-bold text-slate-200 mb-3" data-astro-cid-nndcmjcn>Product Roadmap</h2> <p class="text-slate-400 font-light max-w-2xl mx-auto" data-astro-cid-nndcmjcn>
From precise calculations to a complete trading terminal – making 2‑click trading possible.
</p> </div> <div class="flex flex-col items-center" data-astro-cid-nndcmjcn> <!-- Premium styled timeline with numbered circles --> <div class="relative mb-6 flex justify-center" data-astro-cid-nndcmjcn> <div class="flex items-center gap-2 px-4 py-1.5 bg-slate-900/90 rounded-full shadow-lg border border-white/5" data-astro-cid-nndcmjcn> <div class="flex items-center gap-1" data-astro-cid-nndcmjcn> <div class="w-6 h-6 rounded-full bg-gradient-to-b from-amber-500 to-primary-orange flex items-center justify-center shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)]" data-astro-cid-nndcmjcn> <span class="text-white text-xs font-bold tabular-nums" data-astro-cid-nndcmjcn>1</span> </div> <span class="text-amber-500 text-xs font-normal" data-astro-cid-nndcmjcn>Crypto</span> </div> <div class="relative h-0.5 w-8 animation-container" data-astro-cid-nndcmjcn> <!-- Base line --> <div class="absolute inset-0 bg-amber-500" data-astro-cid-nndcmjcn></div> <!-- Glowing effect that moves across the line --> <div class="glow-effect" data-astro-cid-nndcmjcn></div> </div> <div class="flex items-center gap-1" data-astro-cid-nndcmjcn> <div class="w-6 h-6 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700" data-astro-cid-nndcmjcn> <span class="text-slate-400 text-xs font-bold tabular-nums" data-astro-cid-nndcmjcn>2</span> </div> <span class="text-slate-400 text-xs font-normal" data-astro-cid-nndcmjcn>Stocks</span> </div> <div class="h-0.5 w-8 bg-slate-700" data-astro-cid-nndcmjcn></div> <div class="flex items-center gap-1" data-astro-cid-nndcmjcn> <div class="w-6 h-6 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700" data-astro-cid-nndcmjcn> <span class="text-slate-400 text-xs font-bold tabular-nums" data-astro-cid-nndcmjcn>3</span> </div> <span class="text-slate-400 text-xs font-normal" data-astro-cid-nndcmjcn>Forex</span> </div> <div class="h-0.5 w-8 bg-slate-700" data-astro-cid-nndcmjcn></div> <div class="flex items-center gap-1" data-astro-cid-nndcmjcn> <div class="w-6 h-6 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700" data-astro-cid-nndcmjcn> <span class="text-slate-400 text-xs font-bold tabular-nums" data-astro-cid-nndcmjcn>4</span> </div> <span class="text-slate-400 text-xs font-normal" data-astro-cid-nndcmjcn>Futures</span> </div> <div class="h-0.5 w-8 bg-slate-700" data-astro-cid-nndcmjcn></div> <div class="flex items-center gap-1" data-astro-cid-nndcmjcn> <div class="w-6 h-6 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700" data-astro-cid-nndcmjcn> <span class="text-slate-400 text-xs font-bold tabular-nums" data-astro-cid-nndcmjcn>5</span> </div> <span class="text-slate-400 text-xs font-normal" data-astro-cid-nndcmjcn>Statistics</span> </div> <div class="h-0.5 w-8 bg-slate-700" data-astro-cid-nndcmjcn></div> <div class="flex items-center gap-1" data-astro-cid-nndcmjcn> <div class="w-6 h-6 rounded-full bg-slate-800 flex items-center justify-center border border-slate-700" data-astro-cid-nndcmjcn> <span class="text-slate-400 text-xs font-bold tabular-nums" data-astro-cid-nndcmjcn>6</span> </div> <span class="text-slate-400 text-xs font-normal" data-astro-cid-nndcmjcn>Execution</span> </div> </div> </div> <div class="mt-4 text-center" data-astro-cid-nndcmjcn> <p class="text-xs text-slate-400 front-light max-w-2xl mx-auto" data-astro-cid-nndcmjcn>
We're currently in Phase 1 (Crypto). Stay tuned for our progress through the remaining phases.
</p> </div> </div> </div> </section> `;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/roadmap/roadmap.astro", void 0);
const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "SiteLayout", $$SiteLayout, { "title": "Quer Calculator - Trading Position Calculator with Risk Management", "data-astro-cid-j7pv25f6": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main data-astro-cid-j7pv25f6> <!-- Hero Section --> <section class="py-16 md:py-24" data-astro-cid-j7pv25f6> <div class="max-w-6xl mx-auto px-4" data-astro-cid-j7pv25f6> <div class="grid md:grid-cols-2 gap-8 items-center" data-astro-cid-j7pv25f6> <!-- Left column with text content --> <div class="text-left" data-astro-cid-j7pv25f6> <h1 class="text-4xl md:text-6xl font-bold mb-4 text-transparent bg-clip-text bg-[linear-gradient(60deg,#99a0a5,#e2e8f0,#99a0a5)]" data-astro-cid-j7pv25f6>
Know your<br data-astro-cid-j7pv25f6>sh*t or feed the market.
</h1> <p class="text-lg font-light mb-4 max-w-3xl text-slate-300" data-astro-cid-j7pv25f6>
Losers use the same numbers every trade. Winners adapt precisely. The difference? Millions.
</p> <p class="text-lg font-light mb-4 max-w-3xl text-slate-300" data-astro-cid-j7pv25f6> <span class="bg-primary-orange/20 px-3 py-1 rounded text-primary-orange font-light" data-astro-cid-j7pv25f6>Try free for 7 days</span> — No risk, cancel anytime.
</p> <!-- Platform availability - minimal version --> <div class="flex items-center gap-2.5 mb-4" data-astro-cid-j7pv25f6> <span class="text-lg font-light text-slate-300" data-astro-cid-j7pv25f6>Available on:</span> <div class="flex items-center gap-3 text-lg font-light text-slate-300" data-astro-cid-j7pv25f6> <div class="flex gap-1 items-baseline" data-astro-cid-j7pv25f6> ${renderComponent($$result2, "PiAppleLogo", PiAppleLogo, { "size": 19, "className": "relative top-[3px]", "data-astro-cid-j7pv25f6": true })} <span data-astro-cid-j7pv25f6>macOS</span> </div> <div class="flex gap-1 items-baseline" data-astro-cid-j7pv25f6> ${renderComponent($$result2, "PiWindowsLogo", PiWindowsLogo, { "size": 19, "className": "relative top-[3px]", "data-astro-cid-j7pv25f6": true })} <span data-astro-cid-j7pv25f6>Windows</span> </div> </div> </div> <div class="flex flex-col sm:flex-row gap-4 justify-start" data-astro-cid-j7pv25f6> <a href="/sign-up" class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-b from-amber-500 to-primary-orange text-white text-sm font-medium rounded-md transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-amber-500 hover:to-amber-600 hover:shadow-[0_1px_rgba(255,255,255,0.15)_inset,0_1px_4px_rgba(0,0,0,0.3)] focus:outline-none focus:ring-2 focus:ring-white/70" data-astro-cid-j7pv25f6> <span data-astro-cid-j7pv25f6>Start Free Trial</span> </a> <a href="#features" class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-b from-slate-700 to-slate-800 text-white text-sm font-medium rounded-md transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.07)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-slate-600 hover:to-slate-700 hover:shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_4px_rgba(0,0,0,0.3)] focus:outline-none focus:ring-2 focus:ring-primary-orange/70" data-astro-cid-j7pv25f6> <span data-astro-cid-j7pv25f6>Learn more</span> </a> </div> </div> <!-- Right column with calculator screenshot/GIF --> <div class="flex justify-center md:justify-end order-first md:order-last mb-8 md:mb-0" data-astro-cid-j7pv25f6> <div class="relative" data-astro-cid-j7pv25f6> <picture data-astro-cid-j7pv25f6> <source type="image/webp" srcset="/images/calculator-screenshot-544.webp 1x, /images/calculator-screenshot-1088.webp 2x" data-astro-cid-j7pv25f6> <img src="/images/calculator-screenshot-544.webp" alt="Quer Calculator Interface" width="544" height="350" class="rounded-lg shadow-2xl w-full" data-astro-cid-j7pv25f6> </picture> </div> </div> </div> </div> </section> ${renderComponent($$result2, "Roadmap", $$Roadmap, { "data-astro-cid-j7pv25f6": true })} ${renderComponent($$result2, "Features", $$Features, { "data-astro-cid-j7pv25f6": true })} ${renderComponent($$result2, "WalletStylePricing", $$WalletStylePricing, { "data-astro-cid-j7pv25f6": true })} </main> <footer class="bg-slate-950 py-32" data-astro-cid-j7pv25f6> <div class="max-w-6xl mx-auto px-4 text-center" data-astro-cid-j7pv25f6> <div class="flex flex-col items-center gap-4" data-astro-cid-j7pv25f6> <div class="flex gap-6 text-sm" data-astro-cid-j7pv25f6> <a href="https://quer.us/privacy-policy/" class="text-slate-400 hover:text-slate-300 transition-colors" data-astro-cid-j7pv25f6>
Privacy Policy
</a> <a href="https://quer.us/terms-of-service/" class="text-slate-400 hover:text-slate-300 transition-colors" data-astro-cid-j7pv25f6>
Terms of Service
</a> </div> <p class="text-sm text-slate-500" data-astro-cid-j7pv25f6>© Quer 2025. All rights reserved.</p> </div> </div> </footer>  ` })}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/index.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/index.astro";
const $$url = "";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

/* empty css                                   */
import { e as createAstro, f as createComponent, i as renderComponent, j as renderScript, r as renderTemplate, m as maybeRenderHead, k as Fragment } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$SiteLayout } from "../chunks/SiteLayout_DiyTDXZM.mjs";
import { a as authService } from "../chunks/auth_DyaLaZ23.mjs";
import { renderers } from "../renderers.mjs";
const $$Astro = createAstro("https://quer.us");
const $$Account = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Account;
  const { userId, getToken } = Astro2.locals.auth();
  if (!userId) {
    return Astro2.redirect("/sign-in");
  }
  let sessionToken = "";
  try {
    console.log("Getting token from Clerk...");
    sessionToken = await getToken() || "";
    console.log("Token received, length:", sessionToken ? sessionToken.length : 0);
    if (!sessionToken) {
      throw new Error("Unable to get authentication token");
    }
  } catch (error) {
    console.error("Error getting token:", error);
  }
  let subscriptionDetails = null;
  let subscriptionError = null;
  let subscriptionLoading = false;
  try {
    if (sessionToken) {
      console.log("Initializing auth service with token...");
      subscriptionLoading = true;
      await authService.initialize(sessionToken);
      console.log("Auth service initialized, getting subscription...");
      subscriptionDetails = await authService.getSubscription();
      console.log("Subscription details received:", subscriptionDetails);
    } else {
      console.log("No session token available");
      subscriptionError = "Authentication token is missing";
    }
  } catch (error) {
    console.error("Error in subscription flow:", error);
    subscriptionError = error instanceof Error ? error.message : "An unknown error occurred";
  } finally {
    subscriptionLoading = false;
  }
  function formatDate(dateString) {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  const subscriptionActive = subscriptionDetails?.isActive ?? false;
  const subscriptionInactive = !subscriptionActive;
  const isTrialing = subscriptionDetails?.isTrialing ?? false;
  subscriptionDetails?.planName ?? "Pro Plan";
  const renewalDate = subscriptionDetails?.renewsAt ? formatDate(subscriptionDetails.renewsAt) : "";
  const trialEndDate = subscriptionDetails?.trialEndsAt ? formatDate(subscriptionDetails.trialEndsAt) : "";
  return renderTemplate`${renderComponent($$result, "SiteLayout", $$SiteLayout, { "title": "Account - Quer Terminal" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-4xl mx-auto px-4 py-12"> <div class="flex justify-between items-center mb-8"> <h1 class="text-sm font-light text-slate-600 font-mono">Terminal: Phase [ 1 ] > Calc</h1> </div> <div class="bg-slate-900 border border-white/10 rounded-xl p-6 mb-8"> <h2 class="text-xl font-bold mb-4 text-slate-300">Account Status</h2> ${subscriptionLoading && renderTemplate`<p class="text-sm font-light text-slate-400 mb-4">Loading subscription details...</p>`} ${subscriptionDetails && renderTemplate`<div> ${isTrialing && renderTemplate`${renderComponent($$result2, "Fragment", Fragment, {}, { "default": async ($$result3) => renderTemplate` <div class="bg-blue-900/30 text-blue-500 text-sm font-medium px-3 py-1 rounded-full inline-block mb-4">Trial Active</div> <p class="text-slate-400 mb-6">Your trial ends on ${trialEndDate}. Cancel anytime at no cost.</p> ` })}`} ${!isTrialing && subscriptionActive && renderTemplate`${renderComponent($$result2, "Fragment", Fragment, {}, { "default": async ($$result3) => renderTemplate` <div class="bg-emerald-900/30 text-emerald-500 text-sm font-medium px-3 py-1 rounded-full inline-block mb-4">Active</div> <p class="text-slate-400 mb-6">Renews on ${renewalDate}</p> ` })}`} ${subscriptionInactive && renderTemplate`${renderComponent($$result2, "Fragment", Fragment, {}, { "default": async ($$result3) => renderTemplate` <div class="bg-red-900/30 text-red-500 text-sm font-medium px-3 py-1 rounded-full inline-block mb-4">Not Active</div> ` })}`} </div>`} ${subscriptionError && renderTemplate`<p class="text-red-500 mb-4">Error: ${subscriptionError}</p>`} ${(subscriptionActive || isTrialing) && renderTemplate`<div class="bg-slate-800 p-6 rounded-lg mb-6"> <h3 class="text-lg font-semibold mb-3 text-primary-orange">Download Quer Calculator</h3> <p class="text-slate-400 mb-4">Choose your platform to download the calculator app:</p> <div class="flex gap-4"> <a href="/api/downloads/quer-calculator-mac.dmg" download class="inline-flex items-center bg-slate-700 hover:bg-slate-600 text-white px-6 py-2 rounded-lg transition-colors"> <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path> </svg>
Download for Mac
</a> <a href="/api/downloads/quer-calculator-windows.exe" download class="inline-flex items-center bg-slate-700 hover:bg-slate-600 text-white px-6 py-2 rounded-lg transition-colors"> <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path> </svg>
Download for Windows
</a> </div> </div>`} ${subscriptionInactive && !isTrialing && renderTemplate`<div class="bg-slate-800 p-6 rounded-lg mb-6"> <h3 class="text-lg font-semibold mb-3 text-primary-orange">Start your 7-day free trial today</h3> <ul class="text-sm font-light mb-4 space-y-2 text-slate-400"> <li>✓ Full access</li> <li>✓ Advanced position sizing and risk management</li> <li>✓ Desktop app for Mac and Windows</li> </ul> <p class="text-slate-500 text-sm font-light mb-4">
We require payment information to begin your trial, but you won't be charged until your trial ends. Cancel anytime before then at no cost.
</p> <a href="/checkout" class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-b from-amber-500 to-primary-orange text-white text-sm font-medium rounded-md transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-amber-500 hover:to-amber-600 hover:shadow-[0_1px_rgba(255,255,255,0.15)_inset,0_1px_4px_rgba(0,0,0,0.3)] focus:outline-none focus:ring-2 focus:ring-white/70"> <span>Start Free Trial</span> </a> </div>`} </div> </div> ` })} ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/account.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/account.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/account.astro";
const $$url = "/account";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$Account,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

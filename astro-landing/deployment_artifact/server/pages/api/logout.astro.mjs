import { a as clerkClient } from "../../chunks/index_DgNMlsvN.mjs";
import { renderers } from "../../renderers.mjs";
const POST = async (context) => {
  const { sessionId } = context.locals.auth();
  if (sessionId) {
    await clerkClient(context).sessions.revokeSession(sessionId);
  }
  return context.redirect("/", 302);
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  POST
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

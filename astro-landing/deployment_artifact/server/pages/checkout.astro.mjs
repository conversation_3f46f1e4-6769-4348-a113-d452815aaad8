/* empty css                                   */
import { e as createAstro, f as createComponent, r as renderTemplate, l as defineScriptVars, i as renderComponent, m as maybeRenderHead, h as addAttribute } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$SiteLayout } from "../chunks/SiteLayout_DiyTDXZM.mjs";
import { a as authService } from "../chunks/auth_DyaLaZ23.mjs";
import { renderers } from "../renderers.mjs";
var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro = createAstro("https://quer.us");
const $$Checkout = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Checkout;
  const { userId, getToken } = Astro2.locals.auth();
  if (!userId) {
    return Astro2.redirect("/sign-in");
  }
  let sessionToken = "";
  try {
    sessionToken = await getToken() || "";
    if (!sessionToken) {
      throw new Error("Unable to get authentication token");
    }
  } catch (error) {
    console.error("Error getting token:", error);
  }
  if (sessionToken) {
    try {
      await authService.initialize(sessionToken);
    } catch (error) {
      console.error("Error initializing auth service:", error);
    }
  }
  const monthlyPriceId = "price_1QeSZNEZgCBYDrxL605n1wOl";
  const yearlyPriceId = "price_1QfFswEZgCBYDrxLsKWtpfyl";
  const apiUrl = "https://quer.us/api/landing";
  return renderTemplate(_a || (_a = __template(["", " <script>(function(){", "\n  // Wait for DOM to be fully loaded\n  document.addEventListener('DOMContentLoaded', () => {\n    console.log('DOM loaded, setting up checkout handlers...');\n    \n    // Get UI elements\n    const errorMessage = document.getElementById('error-message');\n    const loadingMessage = document.getElementById('loading-message');\n    const checkoutButtons = document.querySelectorAll('.checkout-btn');\n    \n    // Create auth service directly in client code\n    class ClientAuthService {\n      constructor() {\n        this.apiUrl = apiUrl;\n        this.sessionToken = sessionToken;\n        this.apiToken = '';\n        this.tokenExpiry = 0;\n        console.log('Auth service created with API URL:', this.apiUrl);\n      }\n      \n      async initialize() {\n        console.log('Initializing auth service...');\n        if (!this.sessionToken) {\n          throw new Error('Session token is required');\n        }\n        \n        await this.verifySession();\n        console.log('Auth service initialized successfully');\n      }\n      \n      async verifySession() {\n        try {\n          console.log('Verifying session...');\n          // Fix: Use a conditional URL based on environment\n          const url = this.apiUrl ?\n            `${this.apiUrl}/api/auth/verify-session` :\n            `/api/landing/auth/verify-session`;\n            \n          console.log('Verification URL:', url);\n          \n          const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${this.sessionToken}`\n            }\n          });\n          \n          if (!response.ok) {\n            console.error('Session verification failed');\n            return false;\n          }\n          \n          console.log('Session verified successfully');\n          return true;\n        } catch (error) {\n          console.error('Session verification error:', error);\n          return false;\n        }\n      }\n      \n      async getToken() {\n        // Check if we have a valid token\n        const now = Math.floor(Date.now() / 1000);\n        if (this.apiToken && this.tokenExpiry > now + 60) {\n          return this.apiToken;\n        }\n        \n        try {\n          console.log('Getting API token...');\n          // Fix: Use a conditional URL based on environment\n          const url = this.apiUrl ? \n            `${this.apiUrl}/api/auth/token` : \n            `/api/landing/auth/token`;\n            \n          console.log('Token URL:', url);\n          \n          // Request new token from backend\n          const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${this.sessionToken}`\n            }\n          });\n          \n          if (!response.ok) {\n            throw new Error('Failed to get API token');\n          }\n          \n          const data = await response.json();\n          \n          // Store token and expiry\n          this.apiToken = data.token;\n          this.tokenExpiry = Math.floor(Date.now() / 1000) + data.expiresIn;\n          \n          console.log('API token received successfully');\n          return this.apiToken;\n        } catch (error) {\n          console.error('Error getting API token:', error);\n          throw error;\n        }\n      }\n      \n      async fetchAuthenticated(url, options = {}) {\n        try {\n          // Get token\n          const token = await this.getToken();\n          \n          // Add authorization header\n          const headers = new Headers(options.headers);\n          headers.set('Authorization', `Bearer ${token}`);\n          \n          // Add debug logging\n          console.log(`Making authenticated request to: ${url}`);\n          \n          // Make request\n          return fetch(url, {\n            ...options,\n            headers\n          });\n        } catch (error) {\n          console.error('Authenticated fetch error:', error);\n          throw error;\n        }\n      }\n      \n      async createCheckout(priceId) {\n        try {\n          console.log('Creating checkout session for price ID:', priceId);\n          // Fix: Use a conditional URL based on environment\n          const url = this.apiUrl ? \n            `${this.apiUrl}/api/checkout` : \n            `/api/landing/checkout`;\n            \n          console.log('Checkout URL:', url);\n          \n          const response = await this.fetchAuthenticated(url, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({ priceId })\n          });\n          \n          if (!response.ok) {\n            throw new Error('Failed to create checkout session');\n          }\n          \n          const data = await response.json();\n          console.log('Checkout session created, redirecting to:', data.url);\n          return data.url;\n        } catch (error) {\n          console.error('Error creating checkout:', error);\n          throw error;\n        }\n      }\n    }\n    \n    // Create auth service instance\n    const authService = new ClientAuthService();\n    \n    console.log('Initializing checkout buttons...');\n    console.log('Monthly price ID:', monthlyPriceId);\n    console.log('Yearly price ID:', yearlyPriceId);\n    \n    // Add click handlers to all checkout buttons\n    checkoutButtons.forEach(button => {\n      console.log('Setting up button:', button.id);\n      button.addEventListener('click', async (event) => {\n        event.preventDefault();\n        console.log('Checkout button clicked:', button.id);\n        \n        const priceId = button.getAttribute('data-price-id');\n        console.log('Price ID from button:', priceId);\n        \n        if (!priceId) {\n          showError('Invalid price ID');\n          return;\n        }\n        \n        // Show loading and hide error\n        hideError();\n        showLoading();\n        \n        // Disable all buttons\n        checkoutButtons.forEach(btn => {\n          btn.disabled = true;\n        });\n        \n        try {\n          // Initialize auth service (only once)\n          await authService.initialize();\n          \n          // Create checkout session via auth service\n          const checkoutUrl = await authService.createCheckout(priceId);\n          \n          // Redirect to Stripe Checkout\n          window.location.href = checkoutUrl;\n        } catch (error) {\n          console.error('Checkout error:', error);\n          showError(error instanceof Error ? error.message : 'Failed to create checkout session');\n          enableButtons();\n          hideLoading();\n        }\n      });\n    });\n    \n    // Helper functions\n    function showError(message) {\n      if (errorMessage) {\n        errorMessage.textContent = message;\n        errorMessage.classList.remove('hidden');\n      }\n    }\n    \n    function hideError() {\n      if (errorMessage) {\n        errorMessage.classList.add('hidden');\n      }\n    }\n    \n    function showLoading() {\n      if (loadingMessage) {\n        loadingMessage.classList.remove('hidden');\n      }\n    }\n    \n    function hideLoading() {\n      if (loadingMessage) {\n        loadingMessage.classList.add('hidden');\n      }\n    }\n    \n    function enableButtons() {\n      checkoutButtons.forEach(btn => {\n        btn.disabled = false;\n      });\n    }\n  });\n})();<\/script>"], ["", " <script>(function(){", "\n  // Wait for DOM to be fully loaded\n  document.addEventListener('DOMContentLoaded', () => {\n    console.log('DOM loaded, setting up checkout handlers...');\n    \n    // Get UI elements\n    const errorMessage = document.getElementById('error-message');\n    const loadingMessage = document.getElementById('loading-message');\n    const checkoutButtons = document.querySelectorAll('.checkout-btn');\n    \n    // Create auth service directly in client code\n    class ClientAuthService {\n      constructor() {\n        this.apiUrl = apiUrl;\n        this.sessionToken = sessionToken;\n        this.apiToken = '';\n        this.tokenExpiry = 0;\n        console.log('Auth service created with API URL:', this.apiUrl);\n      }\n      \n      async initialize() {\n        console.log('Initializing auth service...');\n        if (!this.sessionToken) {\n          throw new Error('Session token is required');\n        }\n        \n        await this.verifySession();\n        console.log('Auth service initialized successfully');\n      }\n      \n      async verifySession() {\n        try {\n          console.log('Verifying session...');\n          // Fix: Use a conditional URL based on environment\n          const url = this.apiUrl ?\n            \\`\\${this.apiUrl}/api/auth/verify-session\\` :\n            \\`/api/landing/auth/verify-session\\`;\n            \n          console.log('Verification URL:', url);\n          \n          const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': \\`Bearer \\${this.sessionToken}\\`\n            }\n          });\n          \n          if (!response.ok) {\n            console.error('Session verification failed');\n            return false;\n          }\n          \n          console.log('Session verified successfully');\n          return true;\n        } catch (error) {\n          console.error('Session verification error:', error);\n          return false;\n        }\n      }\n      \n      async getToken() {\n        // Check if we have a valid token\n        const now = Math.floor(Date.now() / 1000);\n        if (this.apiToken && this.tokenExpiry > now + 60) {\n          return this.apiToken;\n        }\n        \n        try {\n          console.log('Getting API token...');\n          // Fix: Use a conditional URL based on environment\n          const url = this.apiUrl ? \n            \\`\\${this.apiUrl}/api/auth/token\\` : \n            \\`/api/landing/auth/token\\`;\n            \n          console.log('Token URL:', url);\n          \n          // Request new token from backend\n          const response = await fetch(url, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': \\`Bearer \\${this.sessionToken}\\`\n            }\n          });\n          \n          if (!response.ok) {\n            throw new Error('Failed to get API token');\n          }\n          \n          const data = await response.json();\n          \n          // Store token and expiry\n          this.apiToken = data.token;\n          this.tokenExpiry = Math.floor(Date.now() / 1000) + data.expiresIn;\n          \n          console.log('API token received successfully');\n          return this.apiToken;\n        } catch (error) {\n          console.error('Error getting API token:', error);\n          throw error;\n        }\n      }\n      \n      async fetchAuthenticated(url, options = {}) {\n        try {\n          // Get token\n          const token = await this.getToken();\n          \n          // Add authorization header\n          const headers = new Headers(options.headers);\n          headers.set('Authorization', \\`Bearer \\${token}\\`);\n          \n          // Add debug logging\n          console.log(\\`Making authenticated request to: \\${url}\\`);\n          \n          // Make request\n          return fetch(url, {\n            ...options,\n            headers\n          });\n        } catch (error) {\n          console.error('Authenticated fetch error:', error);\n          throw error;\n        }\n      }\n      \n      async createCheckout(priceId) {\n        try {\n          console.log('Creating checkout session for price ID:', priceId);\n          // Fix: Use a conditional URL based on environment\n          const url = this.apiUrl ? \n            \\`\\${this.apiUrl}/api/checkout\\` : \n            \\`/api/landing/checkout\\`;\n            \n          console.log('Checkout URL:', url);\n          \n          const response = await this.fetchAuthenticated(url, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({ priceId })\n          });\n          \n          if (!response.ok) {\n            throw new Error('Failed to create checkout session');\n          }\n          \n          const data = await response.json();\n          console.log('Checkout session created, redirecting to:', data.url);\n          return data.url;\n        } catch (error) {\n          console.error('Error creating checkout:', error);\n          throw error;\n        }\n      }\n    }\n    \n    // Create auth service instance\n    const authService = new ClientAuthService();\n    \n    console.log('Initializing checkout buttons...');\n    console.log('Monthly price ID:', monthlyPriceId);\n    console.log('Yearly price ID:', yearlyPriceId);\n    \n    // Add click handlers to all checkout buttons\n    checkoutButtons.forEach(button => {\n      console.log('Setting up button:', button.id);\n      button.addEventListener('click', async (event) => {\n        event.preventDefault();\n        console.log('Checkout button clicked:', button.id);\n        \n        const priceId = button.getAttribute('data-price-id');\n        console.log('Price ID from button:', priceId);\n        \n        if (!priceId) {\n          showError('Invalid price ID');\n          return;\n        }\n        \n        // Show loading and hide error\n        hideError();\n        showLoading();\n        \n        // Disable all buttons\n        checkoutButtons.forEach(btn => {\n          btn.disabled = true;\n        });\n        \n        try {\n          // Initialize auth service (only once)\n          await authService.initialize();\n          \n          // Create checkout session via auth service\n          const checkoutUrl = await authService.createCheckout(priceId);\n          \n          // Redirect to Stripe Checkout\n          window.location.href = checkoutUrl;\n        } catch (error) {\n          console.error('Checkout error:', error);\n          showError(error instanceof Error ? error.message : 'Failed to create checkout session');\n          enableButtons();\n          hideLoading();\n        }\n      });\n    });\n    \n    // Helper functions\n    function showError(message) {\n      if (errorMessage) {\n        errorMessage.textContent = message;\n        errorMessage.classList.remove('hidden');\n      }\n    }\n    \n    function hideError() {\n      if (errorMessage) {\n        errorMessage.classList.add('hidden');\n      }\n    }\n    \n    function showLoading() {\n      if (loadingMessage) {\n        loadingMessage.classList.remove('hidden');\n      }\n    }\n    \n    function hideLoading() {\n      if (loadingMessage) {\n        loadingMessage.classList.add('hidden');\n      }\n    }\n    \n    function enableButtons() {\n      checkoutButtons.forEach(btn => {\n        btn.disabled = false;\n      });\n    }\n  });\n})();<\/script>"])), renderComponent($$result, "SiteLayout", $$SiteLayout, { "title": "Checkout - QueR Calculator" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-4xl mx-auto px-4 py-12"> <h1 class="text-3xl font-bold mb-8 text-slate-200">Choose Your Plan</h1> <p class="text-slate-400 mb-8">
Your 7-day free trial begins today. Payment information is required to start your trial, but you won't be charged until the trial ends. Your selected plan will begin automatically after your 7-day trial unless canceled.
</p> <div class="grid md:grid-cols-2 gap-8"> <div class="bg-slate-900 border border-white/10 rounded-xl p-6"> <h2 class="text-xl font-bold mb-4 text-primary-orange">Pro Monthly</h2> <p class="text-3xl font-bold mb-2 text-slate-200">$12.99<span class="text-lg text-slate-400">/month</span></p> <p class="text-sm text-slate-500 mb-4">After 7-day free trial</p> <ul class="mb-6 space-y-2 text-slate-400"> <li>✓ Full calculator access</li> <li>✓ Save unlimited configurations</li> <li>✓ Desktop app access</li> <li>✓ Advanced risk management tools</li> </ul> <button id="checkout-monthly"${addAttribute(monthlyPriceId, "data-price-id")} class="checkout-btn inline-flex items-center justify-center w-full px-4 py-2 bg-gradient-to-b from-amber-500 to-primary-orange text-white text-sm font-medium rounded-md transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-amber-500 hover:to-amber-600 hover:shadow-[0_1px_rgba(255,255,255,0.15)_inset,0_1px_4px_rgba(0,0,0,0.3)] focus:outline-none focus:ring-2 focus:ring-white/70"> <span>Start 7-Day Free Trial</span> </button> </div> <div class="bg-slate-900 border border-white/10 rounded-xl p-6"> <h2 class="text-xl font-bold mb-4 text-primary-orange">Pro Yearly</h2> <p class="text-3xl font-bold mb-2 text-slate-200">$119.99<span class="text-lg text-slate-400">/year</span></p> <p class="text-sm text-slate-500 mb-4">After 7-day free trial</p> <div class="bg-emerald-900/30 text-emerald-500 text-sm font-medium px-3 py-1 rounded-full inline-block mb-4">Save 23%</div> <ul class="mb-6 space-y-2 text-slate-400"> <li>✓ All monthly features</li> <li>✓ Priority support</li> <li>✓ Early access to new features</li> <li>✓ Best value for serious traders</li> </ul> <button id="checkout-yearly"${addAttribute(monthlyPriceId, "data-price-id")} class="checkout-btn inline-flex items-center justify-center w-full px-4 py-2 bg-gradient-to-b from-amber-500 to-primary-orange text-white text-sm font-medium rounded-md transition-all duration-300 shadow-[0_1px_rgba(255,255,255,0.1)_inset,0_1px_3px_rgba(33,33,38,0.2)] hover:from-amber-500 hover:to-amber-600 hover:shadow-[0_1px_rgba(255,255,255,0.15)_inset,0_1px_4px_rgba(0,0,0,0.3)] focus:outline-none focus:ring-2 focus:ring-white/70">
Start 7-Day Free Trial
</button> </div> </div> <div class="mt-8 text-center"> <a href="/account" class="text-slate-500 hover:text-slate-300 transition-colors">
Back to Account
</a> </div> <div id="error-message" class="mt-4 text-center text-crimson-500 hidden"></div> <div id="loading-message" class="mt-4 text-center text-slate-400 hidden">Processing your request...</div> <div class="mt-8 p-4 bg-slate-800 rounded-lg"> <h3 class="font-medium text-slate-300 mb-2">Why do we ask for payment info?</h3> <ul class="text-sm text-slate-400 space-y-1"> <li>• It helps us verify you're a real person</li> <li>• Ensures seamless transition after your trial if you love the product</li> <li>• Protects our service from fraudulent sign-ups</li> <li>• You can cancel anytime before the trial ends at no cost</li> </ul> </div> </div> ` }), defineScriptVars({ sessionToken, apiUrl, monthlyPriceId, yearlyPriceId }));
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/checkout.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/checkout.astro";
const $$url = "/checkout";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$Checkout,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

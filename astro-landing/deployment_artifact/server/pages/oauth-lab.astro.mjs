/* empty css                                   */
import { f as createComponent, n as renderHead, j as renderScript, r as renderTemplate } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import "clsx";
/* empty css                                     */
import { renderers } from "../renderers.mjs";
const $$OauthLab = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`<html data-astro-cid-fuxy7j5g> <head><title>OAuth Diagnostic Lab</title><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">${renderHead()}</head> <body data-astro-cid-fuxy7j5g> <div class="container" data-astro-cid-fuxy7j5g> <h1 data-astro-cid-fuxy7j5g>OAuth Diagnostic Lab</h1> <p data-astro-cid-fuxy7j5g>Tool for diagnosing, testing and fixing OAuth flows in QUER application</p> <div class="card" data-astro-cid-fuxy7j5g> <h2 data-astro-cid-fuxy7j5g>Updates In Progress</h2> <p data-astro-cid-fuxy7j5g>The diagnostic tool is currently being updated for better compatibility.</p> <p data-astro-cid-fuxy7j5g>Basic functionality is available below:</p> <div data-astro-cid-fuxy7j5g> <button id="test-oauth" data-astro-cid-fuxy7j5g>Test OAuth</button> <button id="test-protocol" data-astro-cid-fuxy7j5g>Test App Protocol</button> <button id="view-params" data-astro-cid-fuxy7j5g>View URL Parameters</button> </div> <pre id="output" data-astro-cid-fuxy7j5g>Click a button to start</pre> </div> </div> ${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-lab.astro?astro&type=script&index=0&lang.ts")} </body> </html>`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-lab.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-lab.astro";
const $$url = "/oauth-lab";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$OauthLab,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

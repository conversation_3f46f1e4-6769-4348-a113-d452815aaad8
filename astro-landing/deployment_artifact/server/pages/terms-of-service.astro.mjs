/* empty css                                   */
import { f as createComponent, i as renderComponent, r as renderTemplate, m as maybeRenderHead } from "../chunks/astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "../chunks/Layout_GAVVpe6b.mjs";
import { renderers } from "../renderers.mjs";
const $$TermsOfService = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Terms of Service - Quer Calculator" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="min-h-screen bg-slate-950 py-16"> <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"> <div class="bg-slate-900/50 rounded-lg p-8"> <h1 class="text-3xl font-bold text-white mb-8">Terms of Service</h1> <div class="prose prose-invert max-w-none"> <p class="text-slate-300 mb-6"> <strong>Effective Date:</strong> June 15, 2025
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">1. Acceptance of Terms</h2> <p class="text-slate-300 mb-6">
By accessing and using Quer Calculator, you accept and agree to be bound by the terms and provision of this agreement.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">2. Description of Service</h2> <p class="text-slate-300 mb-6">
Quer Calculator is a web-based application that provides trading position calculation tools, risk management features, and analytical capabilities for traders and investors.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">3. User Accounts</h2> <ul class="text-slate-300 mb-6 list-disc pl-6"> <li>You must provide accurate and complete information when creating an account</li> <li>You are responsible for maintaining the confidentiality of your account credentials</li> <li>You must notify us immediately of any unauthorized use of your account</li> <li>You must be at least 18 years old to use our service</li> </ul> <h2 class="text-xl font-semibold text-white mt-8 mb-4">4. Acceptable Use</h2> <p class="text-slate-300 mb-4">You agree not to:</p> <ul class="text-slate-300 mb-6 list-disc pl-6"> <li>Use the service for any illegal or unauthorized purpose</li> <li>Attempt to gain unauthorized access to our systems</li> <li>Interfere with or disrupt the service or servers</li> <li>Transmit any malicious code or harmful content</li> <li>Violate any applicable laws or regulations</li> </ul> <h2 class="text-xl font-semibold text-white mt-8 mb-4">5. Financial Disclaimer</h2> <div class="bg-amber-900/20 border border-amber-500/30 rounded-lg p-4 mb-6"> <p class="text-amber-200 mb-2"><strong>Important Notice:</strong></p> <p class="text-slate-300">
Quer Calculator is a tool for educational and analytical purposes only. All calculations and suggestions provided are not financial advice. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. You should consult with a qualified financial advisor before making any investment decisions.
</p> </div> <h2 class="text-xl font-semibold text-white mt-8 mb-4">6. Subscription and Payment</h2> <ul class="text-slate-300 mb-6 list-disc pl-6"> <li>Subscription fees are charged in advance and are non-refundable</li> <li>We reserve the right to change pricing with 30 days notice</li> <li>Free trial periods may be offered at our discretion</li> <li>You may cancel your subscription at any time through your account settings</li> </ul> <h2 class="text-xl font-semibold text-white mt-8 mb-4">7. Intellectual Property</h2> <p class="text-slate-300 mb-6">
All content, features, and functionality of Quer Calculator are owned by us and are protected by copyright, trademark, and other intellectual property laws. You may not reproduce, distribute, or create derivative works without our express written permission.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">8. Data and Privacy</h2> <p class="text-slate-300 mb-6">
Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">9. Service Availability</h2> <p class="text-slate-300 mb-6">
We strive to maintain high service availability but cannot guarantee uninterrupted access. We reserve the right to modify, suspend, or discontinue the service with reasonable notice.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">10. Limitation of Liability</h2> <p class="text-slate-300 mb-6">
To the maximum extent permitted by law, Quer Calculator shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of the service.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">11. Indemnification</h2> <p class="text-slate-300 mb-6">
You agree to indemnify and hold harmless Quer Calculator from any claims, damages, or expenses arising from your use of the service or violation of these terms.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">12. Termination</h2> <p class="text-slate-300 mb-6">
We may terminate or suspend your account and access to the service at our discretion, with or without notice, for any reason including violation of these terms.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">13. Governing Law</h2> <p class="text-slate-300 mb-6">
These terms shall be governed by and construed in accordance with the laws of the United States, without regard to conflict of law principles.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">14. Changes to Terms</h2> <p class="text-slate-300 mb-6">
We reserve the right to modify these terms at any time. Users will be notified of significant changes via email or through the application. Continued use of the service constitutes acceptance of modified terms.
</p> <h2 class="text-xl font-semibold text-white mt-8 mb-4">15. Contact Information</h2> <p class="text-slate-300 mb-6">
If you have questions about these terms, please contact us at:
</p> <div class="text-slate-300 mb-8"> <p><strong>Email:</strong> <EMAIL></p> <p><strong>Website:</strong> https://quer.us</p> </div> <div class="border-t border-slate-700 pt-6 mt-8"> <p class="text-slate-400 text-sm">
These terms of service were last updated on June 15, 2025.
</p> </div> </div> </div> </div> </div> ` })}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/terms-of-service.astro", void 0);
const $$file = "/Users/<USER>/quer-calc-ui/astro-landing/src/pages/terms-of-service.astro";
const $$url = "/terms-of-service";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: $$TermsOfService,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  renderers
};

import "kleur/colors";
import { p as decodeKey } from "./chunks/astro/server_DE_ibNJ9.mjs";
import "clsx";
import "cookie";
import "./chunks/astro-designed-error-pages_BJZr4zH9.mjs";
import "es-module-lexer";
import { N as NOOP_MIDDLEWARE_FN } from "./chunks/noop-middleware_Uvi_wcGe.mjs";
function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}
function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}
function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}
const manifest = deserializeManifest({"hrefRoot":"file:///Users/<USER>/quer-calc-ui/astro-landing/","cacheDir":"file:///Users/<USER>/quer-calc-ui/astro-landing/node_modules/.astro/","outDir":"file:///Users/<USER>/quer-calc-ui/astro-landing/dist/","srcDir":"file:///Users/<USER>/quer-calc-ui/astro-landing/src/","publicDir":"file:///Users/<USER>/quer-calc-ui/astro-landing/public/","buildClientDir":"file:///Users/<USER>/quer-calc-ui/astro-landing/dist/client/","buildServerDir":"file:///Users/<USER>/quer-calc-ui/astro-landing/dist/server/","adapterName":"@astrojs/node","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/node.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/account","isIndex":false,"type":"page","pattern":"^\\/account\\/?$","segments":[[{"content":"account","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/account.astro","pathname":"/account","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[],"routeData":{"route":"/api/logout","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/logout\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"logout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/logout.ts","pathname":"/api/logout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/auth-debug","isIndex":false,"type":"page","pattern":"^\\/auth-debug\\/?$","segments":[[{"content":"auth-debug","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/auth-debug.astro","pathname":"/auth-debug","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/checkout","isIndex":false,"type":"page","pattern":"^\\/checkout\\/?$","segments":[[{"content":"checkout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/checkout.astro","pathname":"/checkout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/clerk-oauth-callback","isIndex":false,"type":"page","pattern":"^\\/clerk-oauth-callback\\/?$","segments":[[{"content":"clerk-oauth-callback","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/clerk-oauth-callback.astro","pathname":"/clerk-oauth-callback","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"},{"type":"inline","content":":root{--primary-orange: #f97316}#loading-indicator[data-astro-cid-njv33vyp]{border-top-color:var(--primary-orange);animation:spin 1s linear infinite}@keyframes spin{to{transform:rotate(360deg)}}\n"}],"routeData":{"route":"/direct-oauth","isIndex":false,"type":"page","pattern":"^\\/direct-oauth\\/?$","segments":[[{"content":"direct-oauth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/direct-oauth.astro","pathname":"/direct-oauth","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"},{"type":"inline","content":"body{font-family:monospace;background-color:#111;color:#eee;padding:20px;margin:0}h1[data-astro-cid-bswva3tm]{color:#f97316}.debug-info[data-astro-cid-bswva3tm]{background-color:#222;padding:15px;border-radius:4px;margin-bottom:20px;overflow-wrap:break-word}.section[data-astro-cid-bswva3tm]{margin-bottom:30px}.label[data-astro-cid-bswva3tm]{font-weight:700;margin-bottom:5px;color:#f97316}.value[data-astro-cid-bswva3tm]{white-space:pre-wrap;max-height:300px;overflow:auto}.btn[data-astro-cid-bswva3tm]{background-color:#f97316;color:#fff;border:none;padding:10px 15px;border-radius:4px;cursor:pointer;font-size:14px;margin-right:10px;margin-top:20px}.btn[data-astro-cid-bswva3tm]:hover{background-color:#ea580c}\n"}],"routeData":{"route":"/oauth-debug","isIndex":false,"type":"page","pattern":"^\\/oauth-debug\\/?$","segments":[[{"content":"oauth-debug","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/oauth-debug.astro","pathname":"/oauth-debug","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"},{"type":"inline","content":"body{font-family:system-ui,sans-serif;background-color:#020617;color:#e2e8f0;padding:0;margin:0}.container[data-astro-cid-fuxy7j5g]{max-width:800px;margin:0 auto;padding:2rem}h1[data-astro-cid-fuxy7j5g]{color:#f97316}.card[data-astro-cid-fuxy7j5g]{background-color:#0f172a;border-radius:.5rem;padding:1.5rem;margin-bottom:1.5rem;border:1px solid #1e293b}button[data-astro-cid-fuxy7j5g]{background-color:#f97316;color:#fff;border:none;padding:.5rem 1rem;border-radius:.375rem;cursor:pointer;margin-right:.5rem;margin-bottom:.5rem}\n"}],"routeData":{"route":"/oauth-lab","isIndex":false,"type":"page","pattern":"^\\/oauth-lab\\/?$","segments":[[{"content":"oauth-lab","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/oauth-lab.astro","pathname":"/oauth-lab","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/oauth-redirect","isIndex":false,"type":"page","pattern":"^\\/oauth-redirect\\/?$","segments":[[{"content":"oauth-redirect","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/oauth-redirect.astro","pathname":"/oauth-redirect","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/oauth-redirect.html","isIndex":false,"type":"page","pattern":"^\\/oauth-redirect\\.html\\/?$","segments":[[{"content":"oauth-redirect.html","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/oauth-redirect.html.astro","pathname":"/oauth-redirect.html","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/privacy-policy","isIndex":false,"type":"page","pattern":"^\\/privacy-policy\\/?$","segments":[[{"content":"privacy-policy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy-policy.astro","pathname":"/privacy-policy","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/protocol-test","isIndex":false,"type":"page","pattern":"^\\/protocol-test\\/?$","segments":[[{"content":"protocol-test","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/protocol-test.astro","pathname":"/protocol-test","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/sign-in","isIndex":false,"type":"page","pattern":"^\\/sign-in\\/?$","segments":[[{"content":"sign-in","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/sign-in.astro","pathname":"/sign-in","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/sign-up","isIndex":false,"type":"page","pattern":"^\\/sign-up\\/?$","segments":[[{"content":"sign-up","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/sign-up.astro","pathname":"/sign-up","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"}],"routeData":{"route":"/terms-of-service","isIndex":false,"type":"page","pattern":"^\\/terms-of-service\\/?$","segments":[[{"content":"terms-of-service","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms-of-service.astro","pathname":"/terms-of-service","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/_astro/page.qmoXb6i2.js"}],"styles":[{"type":"external","src":"/_astro/account.BeaC67wh.css"},{"type":"inline","content":".backdrop-blur-md[data-astro-cid-qi2ey7q5]{backdrop-filter:blur(12px);-webkit-backdrop-filter:blur(12px)}.backdrop-blur-sm[data-astro-cid-qi2ey7q5]{backdrop-filter:blur(1px);-webkit-backdrop-filter:blur(1px)}.inactive-card[data-astro-cid-qi2ey7q5]{filter:blur(0px);transition:all .2s ease}.inactive-card[data-astro-cid-qi2ey7q5]:hover{filter:blur(0px)}.active-card[data-astro-cid-qi2ey7q5]{transition:all .2s ease}.animation-container[data-astro-cid-nndcmjcn]{position:relative;overflow:hidden}.glow-effect[data-astro-cid-nndcmjcn]{position:absolute;left:0;width:14px;height:calc(100% + 20px);top:-10px;background:radial-gradient(circle,#ff9f00e6,#ff7e00b3,#ff7e0066 60%,#ff7e0000);border-radius:50%;box-shadow:0 0 8px 2px #ff8a00cc;animation:moveGlow 3s infinite;z-index:2}.glow-effect[data-astro-cid-nndcmjcn]:before{content:\"\";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:1px;height:6px;background-color:#ffffffe6;border-radius:50%;box-shadow:0 0 4px 2px #fffc}@keyframes moveGlow{0%{left:-10px;opacity:0}15%{opacity:1}85%{opacity:1}to{left:calc(100% + 10px);opacity:0}}.timeline-track[data-astro-cid-j7pv25f6]{position:relative;height:2px;background-color:#94a3b8b3;width:100%;margin:16px 0}.progress-container[data-astro-cid-j7pv25f6]{position:absolute;height:100%;width:16.7%;background-color:#f59e0b;top:0;left:0;overflow:hidden;z-index:5}.shimmer-effect[data-astro-cid-j7pv25f6]{position:absolute;height:100%;width:50%;background:linear-gradient(90deg,#fff0,#ffffff80,#fff0);animation:shimmer 2s infinite linear;top:0;left:-100%}@keyframes shimmer{0%{transform:translate(0)}to{transform:translate(300%)}}\n"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://quer.us","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-debug.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-lab.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/account.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/checkout.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/index.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/auth-debug.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/clerk-oauth-callback.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/direct-oauth.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-redirect.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-redirect.html.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/privacy-policy.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/protocol-test.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/sign-in.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/sign-up.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/terms-of-service.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-page:src/pages/account@_@astro":"pages/account.astro.mjs","\u0000@astro-page:src/pages/api/logout@_@ts":"pages/api/logout.astro.mjs","\u0000@astro-page:src/pages/auth-debug@_@astro":"pages/auth-debug.astro.mjs","\u0000@astro-page:src/pages/checkout@_@astro":"pages/checkout.astro.mjs","\u0000@astro-page:src/pages/clerk-oauth-callback@_@astro":"pages/clerk-oauth-callback.astro.mjs","\u0000@astro-page:src/pages/direct-oauth@_@astro":"pages/direct-oauth.astro.mjs","\u0000@astro-page:src/pages/oauth-debug@_@astro":"pages/oauth-debug.astro.mjs","\u0000@astro-page:src/pages/oauth-lab@_@astro":"pages/oauth-lab.astro.mjs","\u0000@astro-page:src/pages/oauth-redirect@_@astro":"pages/oauth-redirect.astro.mjs","\u0000@astro-page:src/pages/oauth-redirect.html@_@astro":"pages/oauth-redirect.html.astro.mjs","\u0000@astro-page:src/pages/privacy-policy@_@astro":"pages/privacy-policy.astro.mjs","\u0000@astro-page:src/pages/protocol-test@_@astro":"pages/protocol-test.astro.mjs","\u0000@astro-page:src/pages/sign-in@_@astro":"pages/sign-in.astro.mjs","\u0000@astro-page:src/pages/sign-up@_@astro":"pages/sign-up.astro.mjs","\u0000@astro-page:src/pages/terms-of-service@_@astro":"pages/terms-of-service.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/node@_@js":"pages/_image.astro.mjs","\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000@astrojs-manifest":"manifest_D8gFyTe9.mjs","/Users/<USER>/quer-calc-ui/astro-landing/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_BHKyQvN-.mjs","astro:scripts/before-hydration.js":"_astro/astro_scripts/before-hydration.js.CiQ3kpFg.js","/Users/<USER>/quer-calc-ui/astro-landing/src/components/ClientSignIn.tsx":"_astro/ClientSignIn.B8hl1vuW.js","/Users/<USER>/quer-calc-ui/astro-landing/src/components/ClientSignUp.tsx":"_astro/ClientSignUp.DTZ1SWRm.js","/Users/<USER>/quer-calc-ui/astro-landing/src/components/ClientNavigationAuth.tsx":"_astro/ClientNavigationAuth.fNEhj2qT.js","@astrojs/react/client.js":"_astro/client.DyS41jpO.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/account.astro?astro&type=script&index=0&lang.ts":"_astro/account.astro_astro_type_script_index_0_lang.klJZSFGk.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/auth-debug.astro?astro&type=script&index=0&lang.ts":"_astro/auth-debug.astro_astro_type_script_index_0_lang.jjyrxQcY.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/clerk-oauth-callback.astro?astro&type=script&index=0&lang.ts":"_astro/clerk-oauth-callback.astro_astro_type_script_index_0_lang.CI_DLfmM.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/direct-oauth.astro?astro&type=script&index=0&lang.ts":"_astro/direct-oauth.astro_astro_type_script_index_0_lang.ipNXn6jw.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-debug.astro?astro&type=script&index=0&lang.ts":"_astro/oauth-debug.astro_astro_type_script_index_0_lang.d8_sPsSu.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-lab.astro?astro&type=script&index=0&lang.ts":"_astro/oauth-lab.astro_astro_type_script_index_0_lang.DdGz-Jip.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-redirect.astro?astro&type=script&index=0&lang.ts":"_astro/oauth-redirect.astro_astro_type_script_index_0_lang.DMmg9Sb5.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-redirect.html.astro?astro&type=script&index=0&lang.ts":"_astro/oauth-redirect.html.astro_astro_type_script_index_0_lang.CNeqj2Q4.js","/Users/<USER>/quer-calc-ui/astro-landing/src/pages/protocol-test.astro?astro&type=script&index=0&lang.ts":"_astro/protocol-test.astro_astro_type_script_index_0_lang.DzbJfs1J.js","/Users/<USER>/quer-calc-ui/astro-landing/src/components/RedirectProps.astro?astro&type=script&index=0&lang.ts":"_astro/RedirectProps.astro_astro_type_script_index_0_lang.DxtVGX9W.js","/Users/<USER>/quer-calc-ui/astro-landing/src/components/pricing/WalletStylePricing.astro?astro&type=script&index=0&lang.ts":"_astro/WalletStylePricing.astro_astro_type_script_index_0_lang.C-tRt-yE.js","astro:scripts/page.js":"_astro/page.qmoXb6i2.js","\u0000astro:transitions/client":"_astro/client.BYwDIDqY.js"},"inlinedScripts":[["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/account.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{console.log(\"Account page loaded\")});"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/auth-debug.astro?astro&type=script&index=0&lang.ts","document.getElementById(\"check-cookies\").addEventListener(\"click\",async()=>{const e=document.getElementById(\"cookies-result\");e.textContent=\"Получение cookies...\";try{const n=await(await fetch(\"/api/landing/debug/cookies\")).json();e.innerHTML=`<pre>${JSON.stringify(n,null,2)}</pre>`}catch(t){e.textContent=`Ошибка: ${t.message}`}});document.getElementById(\"check-api\").addEventListener(\"click\",async()=>{const e=document.getElementById(\"api-result\");e.textContent=\"Проверка API...\";try{const n=await(await fetch(\"/api/landing/debug/cookies\")).json();e.innerHTML=`<pre>${JSON.stringify(n,null,2)}</pre>`}catch(t){e.textContent=`Ошибка: ${t.message}`}});"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/clerk-oauth-callback.astro?astro&type=script&index=0&lang.ts","function c(e){console.log(e);const t=document.getElementById(\"debugInfo\");if(t){const n=document.createElement(\"div\");n.textContent=e,t.appendChild(n)}}document.addEventListener(\"keydown\",e=>{if(e.shiftKey&&e.key===\"D\"){const t=document.getElementById(\"debugInfo\");t&&t.classList.toggle(\"hidden\")}});function d(){try{c(\"Preparing to redirect to app...\");const e=window.electronAPI&&window.electronAPI.isElectron===!0;if(c(`Running in ${e?\"Electron\":\"Browser\"} environment`),e){const t=document.createElement(\"div\");t.className=\"bg-emerald-900/20 border border-emerald-500/20 rounded-lg p-4 text-center max-w-md mx-auto mt-8\",t.innerHTML=`\n          <p class=\"text-white mb-2\">Authentication completed successfully!</p>\n          <p class=\"text-slate-400\">You can close this window.</p>\n        `,document.querySelector(\".flex.flex-col\").appendChild(t)}else{const t=window.location.search||\"\",n=window.location.hash||\"\",r=`querapp://oauth-complete${t}${n}`;c(`Redirecting to: ${r}`);const l=document.createElement(\"div\");l.className=\"text-center\";const o=document.createElement(\"a\");o.textContent=\"Open QUER App\",o.href=r,o.className=\"inline-block px-6 py-3 bg-primary-orange text-white font-semibold rounded-lg\",l.appendChild(o),document.querySelector(\".flex.flex-col\").appendChild(l),window.location.href=r}}catch(e){c(`Error during redirect: ${e.message}`),console.error(e)}}setTimeout(d,1500);"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/direct-oauth.astro?astro&type=script&index=0&lang.ts","function u(){console.log(\"[direct-oauth] Checking parameters for redirect\");const s=window.location.href;console.log(\"[direct-oauth] Current URL:\",s);const o=new URLSearchParams(window.location.search),c=new URLSearchParams(window.location.hash.replace(\"#\",\"\")),l=o.get(\"clerk_session_token\")||c.get(\"clerk_session_token\"),i=o.get(\"__clerk_ticket\")||c.get(\"__clerk_ticket\"),d=o.get(\"__clerk_handshake\")||c.get(\"__clerk_handshake\"),h=o.get(\"source\")===\"electron\"||c.get(\"source\")===\"electron\",a=document.getElementById(\"status\"),r=document.getElementById(\"status-message\"),n=new URLSearchParams;for(const[e,t]of o.entries())n.set(e,t);for(const[e,t]of c.entries())n.has(e)||n.set(e,t);if(h||i||d||l){console.log(\"[direct-oauth] Detected OAuth parameters, redirecting to oauth-callback-receiver\"),a.textContent=\"OAuth detected\",r.textContent=\"Redirecting to the app authentication handler...\";const t=`${window.location.hostname===\"localhost\"?\"http://localhost:4321\":`${window.location.protocol}//${window.location.hostname}`}/terminal/oauth-callback-receiver?${n.toString()}`;console.log(\"[direct-oauth] Redirecting to:\",t),window.location.href=t}else{console.log(\"[direct-oauth] No OAuth parameters detected, redirecting to account page\"),a.textContent=\"Standard login\",r.textContent=\"Redirecting to your account...\";const e=\"/account\";console.log(\"[direct-oauth] Redirecting to:\",e),window.location.href=e}}setTimeout(u,500);"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-debug.astro?astro&type=script&index=0&lang.ts","document.getElementById(\"currentUrl\").textContent=window.location.href;const a=new URLSearchParams(window.location.search);let o=\"\";a.forEach((t,e)=>{o+=`${e}: ${t}\n`});const s=new URLSearchParams(window.location.hash.replace(\"#\",\"\"));s.toString()&&(o+=`\nHash Parameters:\n`,s.forEach((t,e)=>{o+=`${e}: ${t}\n`}));document.getElementById(\"urlParams\").textContent=o||\"No URL parameters\";document.getElementById(\"cookies\").textContent=document.cookie||\"No cookies\";function c(){const t=document.getElementById(\"clerkState\");try{window.Clerk?(t.textContent=\"Clerk is loaded. Checking session...\",window.Clerk.session?(t.textContent=`Session exists:\nUser ID: ${window.Clerk.session.userId||\"unknown\"}\nStatus: ${window.Clerk.session.status||\"unknown\"}`,window.Clerk.session.getToken().then(e=>{if(e){const n=e.substring(0,20)+\"...\"+e.substring(e.length-20);t.textContent+=`\nToken: ${n}`,localStorage.setItem(\"last_clerk_token\",e),t.textContent+=`\n\nToken saved to localStorage for debugging`}else t.textContent+=`\nNo token available`}).catch(e=>{t.textContent+=`\nError fetching token: ${e.message}`})):(t.textContent=\"Clerk is loaded but no session exists.\",localStorage.getItem(\"last_clerk_token\")&&(t.textContent+=`\n\nFound saved token in localStorage from previous session`))):(t.textContent=\"Clerk is not loaded on this page.\",document.cookie.includes(\"__session=\")&&(t.textContent+=`\n\nBut a __session cookie is present.`),localStorage.getItem(\"last_clerk_token\")&&(t.textContent+=`\n\nFound saved token in localStorage from previous session`))}catch(e){t.textContent=`Error checking Clerk state: ${e.message}`}}setTimeout(c,1e3);document.getElementById(\"redirectBtn\").addEventListener(\"click\",()=>{const t=localStorage.getItem(\"last_clerk_token\"),e=new URLSearchParams(window.location.search);t&&!e.has(\"token\")&&e.set(\"token\",t),!t&&!e.has(\"token\")&&!e.has(\"code\")&&e.set(\"token\",\"test_token\"),e.set(\"timestamp\",Date.now().toString()),e.has(\"source\")||e.set(\"source\",\"manual_test\");const n=`querapp://oauth-complete?${e.toString()}${window.location.hash}`;document.getElementById(\"output\").textContent=`Redirecting to: ${n}`,alert(`Redirecting to app with protocol: querapp://\n\nIf prompted, choose to allow opening the app.`),window.location.href=n});document.getElementById(\"clearLogsBtn\").addEventListener(\"click\",()=>{document.getElementById(\"urlParams\").textContent=\"\",document.getElementById(\"headers\").textContent=\"\",document.getElementById(\"cookies\").textContent=\"\",document.getElementById(\"clerkState\").textContent=\"Cleared...\"});const l={\"User-Agent\":navigator.userAgent,Referrer:document.referrer||\"No referrer\"};let r=\"\";for(const[t,e]of Object.entries(l))r+=`${t}: ${e}\n`;document.getElementById(\"headers\").textContent=r;"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-lab.astro?astro&type=script&index=0&lang.ts","document.getElementById(\"test-oauth\").addEventListener(\"click\",function(){document.getElementById(\"output\").textContent=\"Opening OAuth test window...\",window.open(\"https://s.quer.us/oauth-debug?source=manual_test\",\"_blank\")});document.getElementById(\"test-protocol\").addEventListener(\"click\",function(){const t=`querapp://oauth-complete?token=test_token&source=manual_test&timestamp=${Date.now()}`;document.getElementById(\"output\").textContent=`Redirecting to: ${t}`,window.location.href=t});document.getElementById(\"view-params\").addEventListener(\"click\",function(){const t=new URL(window.location.href),e={};t.searchParams.forEach((n,o)=>{e[o]=n}),document.getElementById(\"output\").textContent=JSON.stringify({url:t.toString(),params:e},null,2)});"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-redirect.astro?astro&type=script&index=0&lang.ts","(function(){console.log(\"[oauth-redirect] Legacy redirect handler loaded\");function e(){const t=window.location.search||\"\",c=window.location.hash||\"\",o=`https://s.quer.us/direct-oauth${t}${c}`;console.log(\"[oauth-redirect] Redirecting to:\",o),window.location.replace(o)}setTimeout(e,500)})();"],["/Users/<USER>/quer-calc-ui/astro-landing/src/pages/oauth-redirect.html.astro?astro&type=script&index=0&lang.ts","(function(){const o=document.getElementById(\"log\");function r(t){if(console.log(t),o){const n=document.createElement(\"div\");n.textContent=t,o.appendChild(n),o.scrollTop=o.scrollHeight}}document.addEventListener(\"keydown\",function(t){t.shiftKey&&t.key===\"L\"&&o&&o.classList.toggle(\"hidden\")}),r(\"OAuth redirect page (HTML version) loaded - v3\");function p(){return window.electronAPI&&window.electronAPI.isElectron===!0}if(p())r(\"Already in Electron, no redirect needed\"),document.body.innerHTML+=`\n        <div class=\"bg-emerald-900/20 border border-emerald-500/20 rounded-lg p-4 text-center max-w-md mx-auto mt-8\">\n          <p class=\"text-white mb-2\">Authentication complete!</p>\n          <p class=\"text-slate-400\">You can close this window and return to the app.</p>\n        </div>\n      `;else{r(\"Running in system browser, redirecting to desktop app...\");const t=\"querapp://oauth-complete\",n=window.location.search||\"\",i=window.location.hash||\"\";let l=\"\";const c=new URLSearchParams(window.location.search);c.has(\"__clerk_handshake\")&&(l=`?__clerk_handshake=${c.get(\"__clerk_handshake\")}`,r(\"Found Clerk handshake parameter\")),r(\"Current URL:\"),r(\"Search params:\"),r(\"Hash params:\");const d=l?`${t}${l}`:`${t}${n}${i}`;r(\"Redirecting to: \"+d),setTimeout(()=>{try{const e=document.createElement(\"a\");e.href=d,e.textContent=\"Open QUER app\",e.style.position=\"relative\",e.style.display=\"inline-block\",e.style.padding=\"12px 20px\",e.style.backgroundColor=\"#f97316\",e.style.color=\"white\",e.style.borderRadius=\"8px\",e.style.textDecoration=\"none\",e.style.fontWeight=\"bold\",e.style.margin=\"10px auto\";const a=document.createElement(\"div\");a.style.textAlign=\"center\",a.appendChild(e),document.querySelector(\".flex.flex-col\").appendChild(a),window.location.href=d,r(\"Redirect initiated via window.location\"),setTimeout(()=>{e.click(),r(\"Clicked protocol link\")},300),setTimeout(()=>{const s=document.createElement(\"div\");s.innerHTML=`\n              <div class=\"bg-slate-800 border border-primary-orange/20 rounded-lg p-4 text-center max-w-md mx-auto mt-8\">\n                <p class=\"text-white mb-2\">If you're not automatically redirected to the app:</p>\n                <p class=\"text-slate-400 mb-4\">Click the orange button above to launch the app.</p>\n                <p class=\"text-slate-400 mb-2\">If that doesn't work, the app may not be installed or not properly registered.</p>\n                <a href=\"https://s.quer.us/terminal/auth${n}${i}\" class=\"text-primary-orange hover:text-amber-400 underline\">\n                  Or continue in browser\n                </a>\n              </div>\n            `,document.querySelector(\".flex.flex-col\").appendChild(s)},1e3)}catch(e){r(\"Error during redirect: \"+e.message);const a=document.createElement(\"div\");a.innerHTML=`\n            <div class=\"bg-red-900/20 border border-red-500/20 rounded-lg p-4 text-center max-w-md mx-auto mt-8\">\n              <p class=\"text-white mb-2\">Error during redirect</p>\n              <p class=\"text-slate-400 mb-4\">${e.message||\"Unknown error\"}</p>\n              <a href=\"/\" class=\"text-primary-orange hover:text-amber-400 underline\">Return to Quer.us</a>\n            </div>\n          `,document.querySelector(\".flex.flex-col\").appendChild(a)}},500)}window.addEventListener(\"message\",function(t){if(r(\"Received message event: \"+JSON.stringify(t.data)),t.data&&(t.data.__clerk_handshake||t.data.clerk)){r(\"Detected Clerk message, redirecting to app\");const n=t.data.__clerk_handshake||JSON.stringify(t.data),i=`querapp://oauth-complete?clerk_data=${encodeURIComponent(n)}`;window.location.href=i}})})();"],["/Users/<USER>/quer-calc-ui/astro-landing/src/components/RedirectProps.astro?astro&type=script&index=0&lang.ts","function c(){console.log(\"Adding redirect props to Clerk components\");const r=[\".cl-signIn\",\".cl-signUp\",\".cl-signInButton\",\".cl-signUpButton\",\".cl-userButton\",\"[data-clerk-component]\"],e=document.querySelectorAll(r.join(\",\"));console.log(`Found ${e.length} Clerk components`),e.forEach(t=>{t.removeAttribute(\"data-redirect-url\"),t.setAttribute(\"data-force-redirect-url\",\"/account\"),t.setAttribute(\"data-sign-in-force-redirect-url\",\"/account\"),t.setAttribute(\"data-sign-up-force-redirect-url\",\"/account\"),t.setAttribute(\"data-fallback-redirect-url\",\"/account\"),t.setAttribute(\"data-sign-in-fallback-redirect-url\",\"/account\"),t.setAttribute(\"data-sign-up-fallback-redirect-url\",\"/account\"),console.log(`Added redirect props to component: ${t.className||t.tagName}`)})}document.addEventListener(\"DOMContentLoaded\",c);const o=new MutationObserver(r=>{let e=!1;r.forEach(t=>{t.addedNodes.length>0&&(e=!0)}),e&&c()});o.observe(document.body,{childList:!0,subtree:!0});"],["/Users/<USER>/quer-calc-ui/astro-landing/src/components/pricing/WalletStylePricing.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const e=document.getElementById(\"monthly-toggle\"),t=document.getElementById(\"yearly-toggle\"),y=document.getElementById(\"monthly-card\"),h=document.getElementById(\"yearly-card\"),l=document.getElementById(\"monthly-check\"),a=document.getElementById(\"yearly-check\"),o=document.getElementById(\"monthly-cta\"),d=document.getElementById(\"yearly-cta\"),n=document.getElementById(\"monthly-select\"),c=document.getElementById(\"yearly-select\"),i=document.getElementById(\"mobile-monthly-card\"),r=document.getElementById(\"mobile-yearly-card\"),u=y&&h&&l&&a&&o&&d&&n&&c,v=i&&r,x=e&&t;if(!x||!u&&!v){console.error(\"Required pricing elements not found\");return}const g=\"z-20 -translate-x-1/2 translate-y-0 opacity-100\",b=\"z-10 translate-x-[-100px] translate-y-4 opacity-90 scale-95 hover:translate-y-3 inactive-card\";function s(m){m===\"monthly\"?(e.classList.add(\"bg-slate-700/60\",\"text-white\"),e.classList.remove(\"text-slate-400\",\"hover:text-slate-300\"),t.classList.remove(\"bg-slate-700/60\",\"text-white\"),t.classList.add(\"text-slate-400\",\"hover:text-slate-300\")):(t.classList.add(\"bg-slate-700/60\",\"text-white\"),t.classList.remove(\"text-slate-400\",\"hover:text-slate-300\"),e.classList.remove(\"bg-slate-700/60\",\"text-white\"),e.classList.add(\"text-slate-400\",\"hover:text-slate-300\")),u&&(m===\"monthly\"?(y.className=`absolute top-0 left-1/2 w-full max-w-md rounded-2xl shadow-2xl transition-all duration-500 ease-in-out cursor-pointer ${g}`,h.className=`absolute top-0 left-1/2 w-full max-w-md rounded-2xl shadow-2xl transition-all duration-500 ease-in-out cursor-pointer ${b}`,l.classList.remove(\"hidden\"),l.classList.add(\"flex\",\"items-center\",\"justify-center\"),a.classList.remove(\"flex\",\"items-center\",\"justify-center\"),a.classList.add(\"hidden\"),o.classList.remove(\"hidden\"),o.classList.add(\"block\"),d.classList.remove(\"block\"),d.classList.add(\"hidden\"),n.classList.remove(\"block\"),n.classList.add(\"hidden\"),c.classList.remove(\"hidden\"),c.classList.add(\"block\")):(h.className=`absolute top-0 left-1/2 w-full max-w-md rounded-2xl shadow-2xl transition-all duration-500 ease-in-out cursor-pointer ${g}`,y.className=`absolute top-0 left-1/2 w-full max-w-md rounded-2xl shadow-2xl transition-all duration-500 ease-in-out cursor-pointer ${b}`,a.classList.remove(\"hidden\"),a.classList.add(\"flex\",\"items-center\",\"justify-center\"),l.classList.remove(\"flex\",\"items-center\",\"justify-center\"),l.classList.add(\"hidden\"),d.classList.remove(\"hidden\"),d.classList.add(\"block\"),o.classList.remove(\"block\"),o.classList.add(\"hidden\"),c.classList.remove(\"block\"),c.classList.add(\"hidden\"),n.classList.remove(\"hidden\"),n.classList.add(\"block\"))),v&&(m===\"monthly\"?(i.classList.remove(\"hidden\"),i.classList.add(\"block\"),r.classList.remove(\"block\"),r.classList.add(\"hidden\")):(r.classList.remove(\"hidden\"),r.classList.add(\"block\"),i.classList.remove(\"block\"),i.classList.add(\"hidden\")))}u&&(y.addEventListener(\"click\",()=>s(\"monthly\")),h.addEventListener(\"click\",()=>s(\"yearly\")),document.querySelectorAll(\".select-plan-btn\").forEach(m=>{m.addEventListener(\"click\",f=>{f.stopPropagation();const L=f.currentTarget;if(L.dataset&&L.dataset.plan){const k=L.dataset.plan;s(k)}})})),x&&(e.addEventListener(\"click\",()=>s(\"monthly\")),t.addEventListener(\"click\",()=>s(\"yearly\"))),s(\"yearly\")});"]],"assets":["/_astro/account.BeaC67wh.css","/oauth-redirect.html","/robots.txt","/_astro/ClientNavigationAuth.fNEhj2qT.js","/_astro/ClientSignIn.B8hl1vuW.js","/_astro/ClientSignUp.DTZ1SWRm.js","/_astro/InternalUIComponentRenderer.Djr_-AHC.js","/_astro/chunk-6EJX3HJ2.COMaeMaZ.js","/_astro/client.BYwDIDqY.js","/_astro/client.DyS41jpO.js","/_astro/index.BVOCwoKb.js","/_astro/page.qmoXb6i2.js","/_astro/protocol-test.astro_astro_type_script_index_0_lang.DzbJfs1J.js","/images/calculator-screenshot-1088.webp","/images/calculator-screenshot-544.webp","/images/qlb.svg","/_astro/astro_scripts/before-hydration.js.CiQ3kpFg.js","/_astro/page.qmoXb6i2.js"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"3oqlrU0DRlrYh+xq6yxd2rwx5U1Ls4ZtnjBYAuRwbIc="});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = null;
export {
  manifest
};

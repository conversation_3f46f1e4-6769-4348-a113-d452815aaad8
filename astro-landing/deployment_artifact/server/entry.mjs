import { renderers } from "./renderers.mjs";
import { c as createExports, s as serverEntrypointModule } from "./chunks/_@astrojs-ssr-adapter_CptmlEkW.mjs";
import { manifest } from "./manifest_D8gFyTe9.mjs";
const serverIslandMap = /* @__PURE__ */ new Map();
;
const _page0 = () => import("./pages/_image.astro.mjs");
const _page1 = () => import("./pages/account.astro.mjs");
const _page2 = () => import("./pages/api/logout.astro.mjs");
const _page3 = () => import("./pages/auth-debug.astro.mjs");
const _page4 = () => import("./pages/checkout.astro.mjs");
const _page5 = () => import("./pages/clerk-oauth-callback.astro.mjs");
const _page6 = () => import("./pages/direct-oauth.astro.mjs");
const _page7 = () => import("./pages/oauth-debug.astro.mjs");
const _page8 = () => import("./pages/oauth-lab.astro.mjs");
const _page9 = () => import("./pages/oauth-redirect.astro.mjs");
const _page10 = () => import("./pages/oauth-redirect.html.astro.mjs");
const _page11 = () => import("./pages/privacy-policy.astro.mjs");
const _page12 = () => import("./pages/protocol-test.astro.mjs");
const _page13 = () => import("./pages/sign-in.astro.mjs");
const _page14 = () => import("./pages/sign-up.astro.mjs");
const _page15 = () => import("./pages/terms-of-service.astro.mjs");
const _page16 = () => import("./pages/index.astro.mjs");
const pageMap = /* @__PURE__ */ new Map([
  ["node_modules/astro/dist/assets/endpoint/node.js", _page0],
  ["src/pages/account.astro", _page1],
  ["src/pages/api/logout.ts", _page2],
  ["src/pages/auth-debug.astro", _page3],
  ["src/pages/checkout.astro", _page4],
  ["src/pages/clerk-oauth-callback.astro", _page5],
  ["src/pages/direct-oauth.astro", _page6],
  ["src/pages/oauth-debug.astro", _page7],
  ["src/pages/oauth-lab.astro", _page8],
  ["src/pages/oauth-redirect.astro", _page9],
  ["src/pages/oauth-redirect.html.astro", _page10],
  ["src/pages/privacy-policy.astro", _page11],
  ["src/pages/protocol-test.astro", _page12],
  ["src/pages/sign-in.astro", _page13],
  ["src/pages/sign-up.astro", _page14],
  ["src/pages/terms-of-service.astro", _page15],
  ["src/pages/index.astro", _page16]
]);
const _manifest = Object.assign(manifest, {
  pageMap,
  serverIslandMap,
  renderers,
  actions: () => import("./_noop-actions.mjs"),
  middleware: () => import("./_astro-internal_middleware.mjs")
});
const _args = {
  "mode": "standalone",
  "client": "file:///Users/<USER>/quer-calc-ui/astro-landing/dist/client/",
  "server": "file:///Users/<USER>/quer-calc-ui/astro-landing/dist/server/",
  "host": false,
  "port": 4321,
  "assets": "_astro"
};
const _exports = createExports(_manifest, _args);
const handler = _exports["handler"];
const startServer = _exports["startServer"];
const options = _exports["options"];
const _start = "start";
if (_start in serverEntrypointModule) {
  serverEntrypointModule[_start](_manifest, _args);
}
export {
  handler,
  options,
  pageMap,
  startServer
};

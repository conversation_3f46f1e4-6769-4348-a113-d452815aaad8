import { f as createComponent, j as renderScript, r as renderTemplate } from "./astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import "clsx";
const $$RedirectProps = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderScript($$result, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/RedirectProps.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/RedirectProps.astro", void 0);
export {
  $$RedirectProps as $
};

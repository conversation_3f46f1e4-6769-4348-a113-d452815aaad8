import { e as createAstro, f as createComponent, m as maybeRenderHead, i as renderComponent, r as renderTemplate, o as renderSlot } from "./astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import { $ as $$Layout } from "./Layout_GAVVpe6b.mjs";
const $$Astro$1 = createAstro("https://quer.us");
const $$NavigationBar = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$NavigationBar;
  Astro2.url.pathname;
  return renderTemplate`${maybeRenderHead()}<div class="sticky top-0 w-full z-50 bg-slate-950"> <div class="flex justify-center w-full"> <nav class="h-16 flex items-center justify-between w-full max-w-6xl px-4"> <!-- Logo on the left --> <div class="flex items-center pt-0.5"> <a href="/"> <img src="/images/qlb.svg" alt="Quer logo" class="h-8 w-auto"> </a> </div> <!-- Right side with sign in/up buttons or user button --> ${renderComponent($$result, "ClientNavigationAuth", null, { "client:only": "react", "client:component-hydration": "only", "client:component-path": "/Users/<USER>/quer-calc-ui/astro-landing/src/components/ClientNavigationAuth.tsx", "client:component-export": "default" })} </nav> </div> </div>`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/components/NavigationBar.astro", void 0);
const $$Astro = createAstro("https://quer.us");
const $$SiteLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$SiteLayout;
  const { title } = Astro2.props;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "NavigationBar", $$NavigationBar, {})} ${renderSlot($$result2, $$slots["default"])} ` })}`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/layouts/SiteLayout.astro", void 0);
export {
  $$SiteLayout as $
};

function getApiBaseUrl() {
  if (typeof window !== "undefined") {
    const hostname = window.location.hostname;
    if (hostname === "localhost" || hostname === "127.0.0.1") {
      return "https://quer.us/api/landing";
    }
    const explicitApiUrl = "https://quer.us/api/landing";
    {
      return explicitApiUrl;
    }
  }
  const serverApiUrl = "https://quer.us/api/landing";
  {
    return serverApiUrl;
  }
}
class AuthService {
  static instance;
  apiUrl;
  sessionToken = "";
  constructor() {
    this.apiUrl = getApiBaseUrl();
    console.log("🔌 API URL:", this.apiUrl);
  }
  /**
   * Get singleton instance
   */
  static getInstance() {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }
  /**
   * Initialize auth service with session token from Clerk
   */
  async initialize(sessionToken) {
    if (!sessionToken) {
      throw new Error("Session token is required");
    }
    this.sessionToken = sessionToken;
    await this.verifySession();
  }
  /**
   * Verify current session
   */
  async verifySession() {
    try {
      const isBrowser = typeof window !== "undefined";
      const baseUrl = this.apiUrl || (isBrowser ? "" : "http://localhost:8091");
      const url = isBrowser ? this.apiUrl ? `${this.apiUrl}/api/auth/verify-session` : `/api/landing/auth/verify-session` : `${baseUrl}/api/auth/verify-session`;
      console.log("Verifying session at URL:", url);
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.sessionToken}`
        }
      });
      if (!response.ok) {
        console.error("Session verification failed");
        return false;
      }
      return true;
    } catch (error) {
      console.error("Session verification error:", error);
      return false;
    }
  }
  /**
   * Get authentication token for backend API calls
   * Returns the Clerk session token directly (no need to generate additional API tokens)
   */
  async getToken() {
    if (!this.sessionToken) {
      throw new Error("Session token is not set. Call initialize first.");
    }
    console.log("Using Clerk session token directly for API calls");
    return this.sessionToken;
  }
  /**
   * Make authenticated API request
   */
  async fetchAuthenticated(url, options = {}) {
    try {
      const token = await this.getToken();
      const headers = new Headers(options.headers);
      headers.set("Authorization", `Bearer ${token}`);
      return fetch(url, {
        ...options,
        headers
      });
    } catch (error) {
      console.error("Authenticated fetch error:", error);
      throw error;
    }
  }
  /**
   * Get user profile
   */
  async getUserProfile() {
    try {
      const isBrowser = typeof window !== "undefined";
      const baseUrl = this.apiUrl || (isBrowser ? "" : "http://localhost:8091");
      const url = isBrowser ? this.apiUrl ? `${this.apiUrl}/api/user/profile` : `/api/landing/user/profile` : `${baseUrl}/api/user/profile`;
      console.log("Getting user profile from URL:", url);
      const response = await this.fetchAuthenticated(url);
      if (!response.ok) {
        throw new Error("Failed to get user profile");
      }
      return response.json();
    } catch (error) {
      console.error("Error getting user profile:", error);
      throw error;
    }
  }
  /**
   * Get subscription status
   */
  async getSubscription() {
    try {
      const isBrowser = typeof window !== "undefined";
      const baseUrl = this.apiUrl || (isBrowser ? "" : "http://localhost:8091");
      const url = isBrowser ? this.apiUrl ? `${this.apiUrl}/api/subscription` : `/api/landing/subscription` : `${baseUrl}/api/subscription`;
      console.log("Getting subscription from URL:", url);
      const response = await this.fetchAuthenticated(url);
      console.log("Subscription response status:", response.status);
      if (!response.ok) {
        console.error("Subscription response error:", await response.text());
        throw new Error("Failed to get subscription");
      }
      return response.json();
    } catch (error) {
      console.error("Error getting subscription:", error);
      throw error;
    }
  }
  /**
   * Create checkout session
   */
  async createCheckout(priceId) {
    try {
      const isBrowser = typeof window !== "undefined";
      const baseUrl = this.apiUrl || (isBrowser ? "" : "http://localhost:8091");
      const url = isBrowser ? this.apiUrl ? `${this.apiUrl}/api/checkout` : `/api/landing/checkout` : `${baseUrl}/api/checkout`;
      console.log("Creating checkout at URL:", url);
      const response = await this.fetchAuthenticated(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ priceId })
      });
      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }
      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error("Error creating checkout:", error);
      throw error;
    }
  }
}
const authService = AuthService.getInstance();
export {
  authService as a
};

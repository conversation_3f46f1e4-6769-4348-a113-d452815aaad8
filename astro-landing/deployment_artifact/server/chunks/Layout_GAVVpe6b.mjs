import { e as createAstro, f as createComponent, h as addAttribute, n as renderHead, o as renderSlot, r as renderTemplate } from "./astro/server_DE_ibNJ9.mjs";
import "kleur/colors";
import "clsx";
const $$Astro = createAstro("https://quer.us");
const $$Layout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Layout;
  const { title, description = "Trading position size calculator with risk management tools" } = Astro2.props;
  return renderTemplate`<html lang="en"> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator"${addAttribute(Astro2.generator, "content")}><title>${title}</title><meta name="description"${addAttribute(description, "content")}><!-- Open Graph / Social Media Meta Tags --><meta property="og:title"${addAttribute(title, "content")}><meta property="og:description"${addAttribute(description, "content")}><meta property="og:type" content="website"><meta property="og:url" content="https://quer.us"><meta property="og:image" content="https://quer.us/og-image.png"><!-- Twitter Meta Tags --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title"${addAttribute(title, "content")}><meta name="twitter:description"${addAttribute(description, "content")}><meta name="twitter:image" content="https://quer.us/twitter-image.png"><!-- Font --><link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">${renderHead()}</head> <body class="bg-slate-950 text-slate-300 font-sans min-h-screen"> ${renderSlot($$result, $$slots["default"])} </body></html>`;
}, "/Users/<USER>/quer-calc-ui/astro-landing/src/layouts/Layout.astro", void 0);
export {
  $$Layout as $
};
